#!/usr/bin/env python3
"""
Script para investigar el registro faltante en los archivos temporales del pipeline
"""
import pandas as pd
import os
import glob
from pathlib import Path

def buscar_en_archivos_temporales():
    """Busca el registro faltante en todos los archivos temporales del pipeline"""
    print("🔍 INVESTIGACIÓN DEL REGISTRO FALTANTE EN ARCHIVOS TEMPORALES")
    print("=" * 70)
    
    # Calcular la diferencia neta exacta
    archivo_modernizado = "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/output/20250609/csv_exports/csv_final_procesado/LOGUSR-FCOMPARTAMOS-20250609074148.csv"
    archivo_original = "/home/<USER>/generate/log_usuarios/output/LOGUSR-FCOMPARTAMOS-20250610052953.csv"
    
    df_modernizado = pd.read_csv(archivo_modernizado, header=None)
    df_original = pd.read_csv(archivo_original, header=None)
    
    print(f"📊 CONTEOS EXACTOS:")
    print(f"   • Original: {len(df_original):,} registros")
    print(f"   • Modernizado: {len(df_modernizado):,} registros")
    print(f"   • Diferencia: {len(df_original) - len(df_modernizado):,} registros")
    
    # Identificar el registro que está en original pero no en modernizado
    columnas = ['OPERACION', 'TRANSACTIONID', 'FECHA_HORA', 'CANAL', 'TIPODOCUMENTO', 
               'DOCUMENTO', 'CELULAR', 'EMPRESA'] + [f'COL{i}' for i in range(8, 30)]
    
    df_modernizado.columns = columnas
    df_original.columns = columnas
    
    # Crear clave única sin TransactionID
    def crear_clave(row):
        return f"{row['OPERACION']}|{row['FECHA_HORA']}|{row['TIPODOCUMENTO']}|{row['DOCUMENTO']}|{row['CELULAR']}|{row['EMPRESA']}"
    
    df_modernizado['CLAVE'] = df_modernizado.apply(crear_clave, axis=1)
    df_original['CLAVE'] = df_original.apply(crear_clave, axis=1)
    
    # Encontrar registros únicos
    claves_modernizado = set(df_modernizado['CLAVE'])
    claves_original = set(df_original['CLAVE'])
    
    solo_en_original = claves_original - claves_modernizado
    solo_en_modernizado = claves_modernizado - claves_original
    
    print(f"\n🔍 ANÁLISIS DE DIFERENCIAS ÚNICAS:")
    print(f"   • Solo en original: {len(solo_en_original)}")
    print(f"   • Solo en modernizado: {len(solo_en_modernizado)}")
    print(f"   • Diferencia neta: {len(solo_en_original) - len(solo_en_modernizado)}")
    
    # Si hay diferencia neta de 1, encontrar ese registro específico
    if len(solo_en_original) - len(solo_en_modernizado) == 1:
        # Encontrar registros que están en original pero no tienen equivalente en modernizado
        registros_verdaderamente_faltantes = []
        
        for clave_orig in solo_en_original:
            registro_orig = df_original[df_original['CLAVE'] == clave_orig].iloc[0]
            
            # Buscar si existe un registro similar en modernizado (mismo usuario, misma operación, día similar)
            usuario_doc = registro_orig['DOCUMENTO']
            usuario_cel = registro_orig['CELULAR']
            operacion = registro_orig['OPERACION']
            
            # Buscar registros del mismo usuario y operación en modernizado
            registros_similares = df_modernizado[
                (df_modernizado['DOCUMENTO'] == usuario_doc) & 
                (df_modernizado['CELULAR'] == usuario_cel) & 
                (df_modernizado['OPERACION'] == operacion)
            ]
            
            if len(registros_similares) == 0:
                registros_verdaderamente_faltantes.append(registro_orig)
                print(f"\n❌ REGISTRO VERDADERAMENTE FALTANTE:")
                print(f"   • Operación: {registro_orig['OPERACION']}")
                print(f"   • TransactionID: {registro_orig['TRANSACTIONID']}")
                print(f"   • Fecha/Hora: {registro_orig['FECHA_HORA']}")
                print(f"   • Documento: {registro_orig['TIPODOCUMENTO']} {registro_orig['DOCUMENTO']}")
                print(f"   • Celular: {registro_orig['CELULAR']}")
                print(f"   • Empresa: {registro_orig['EMPRESA']}")
                
                # Buscar este registro en archivos temporales
                buscar_registro_en_temporales(registro_orig)
    
    return solo_en_original, solo_en_modernizado

def buscar_registro_en_temporales(registro_faltante):
    """Busca un registro específico en todos los archivos temporales"""
    print(f"\n🔍 BÚSQUEDA EN ARCHIVOS TEMPORALES:")
    
    # Directorios donde buscar archivos temporales
    directorios_busqueda = [
        "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/output/20250609",
        "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/output/20250609/parquet_exports",
        "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/output/20250609/csv_exports",
        "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/temp",
        "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER"
    ]
    
    documento = str(registro_faltante['DOCUMENTO'])
    celular = str(registro_faltante['CELULAR'])
    operacion = registro_faltante['OPERACION']
    
    print(f"   🎯 Buscando: {operacion} - DNI {documento} - Celular {celular}")
    
    archivos_encontrados = []
    
    for directorio in directorios_busqueda:
        if os.path.exists(directorio):
            print(f"\n   📁 Buscando en: {directorio}")
            
            # Buscar archivos CSV
            archivos_csv = glob.glob(f"{directorio}/**/*.csv", recursive=True)
            for archivo_csv in archivos_csv:
                try:
                    if buscar_en_archivo(archivo_csv, documento, celular, operacion):
                        archivos_encontrados.append(archivo_csv)
                        print(f"      ✅ ENCONTRADO en: {archivo_csv}")
                except Exception as e:
                    print(f"      ❌ Error leyendo {archivo_csv}: {e}")
            
            # Buscar archivos Parquet
            archivos_parquet = glob.glob(f"{directorio}/**/*.parquet", recursive=True)
            for archivo_parquet in archivos_parquet:
                try:
                    if buscar_en_archivo_parquet(archivo_parquet, documento, celular, operacion):
                        archivos_encontrados.append(archivo_parquet)
                        print(f"      ✅ ENCONTRADO en: {archivo_parquet}")
                except Exception as e:
                    print(f"      ❌ Error leyendo {archivo_parquet}: {e}")
        else:
            print(f"   ❌ Directorio no existe: {directorio}")
    
    if not archivos_encontrados:
        print(f"\n   ❌ REGISTRO NO ENCONTRADO en ningún archivo temporal")
        print(f"   💡 Esto sugiere que el registro se perdió durante el procesamiento")
    else:
        print(f"\n   ✅ Registro encontrado en {len(archivos_encontrados)} archivos")
        
        # Analizar en qué etapa se perdió
        analizar_perdida_registro(archivos_encontrados, documento, celular, operacion)

def buscar_en_archivo(archivo_path, documento, celular, operacion):
    """Busca un registro específico en un archivo CSV"""
    try:
        df = pd.read_csv(archivo_path, header=None)
        if len(df.columns) >= 7:  # Asegurar que tiene las columnas necesarias
            # Buscar por documento y celular
            encontrado = df[
                (df.iloc[:, 5].astype(str).str.contains(documento, na=False)) &
                (df.iloc[:, 6].astype(str).str.contains(celular, na=False)) &
                (df.iloc[:, 0].astype(str) == operacion)
            ]
            return len(encontrado) > 0
    except:
        pass
    return False

def buscar_en_archivo_parquet(archivo_path, documento, celular, operacion):
    """Busca un registro específico en un archivo Parquet"""
    try:
        df = pd.read_parquet(archivo_path)
        # Buscar por documento y celular en diferentes posibles nombres de columnas
        columnas_documento = [col for col in df.columns if 'DOCUMENTO' in col.upper() or 'DOC' in col.upper()]
        columnas_celular = [col for col in df.columns if 'CELULAR' in col.upper() or 'PHONE' in col.upper()]
        columnas_operacion = [col for col in df.columns if 'OPERACION' in col.upper() or 'OPERATION' in col.upper()]
        
        if columnas_documento and columnas_celular and columnas_operacion:
            encontrado = df[
                (df[columnas_documento[0]].astype(str).str.contains(documento, na=False)) &
                (df[columnas_celular[0]].astype(str).str.contains(celular, na=False)) &
                (df[columnas_operacion[0]].astype(str) == operacion)
            ]
            return len(encontrado) > 0
    except:
        pass
    return False

def analizar_perdida_registro(archivos_encontrados, documento, celular, operacion):
    """Analiza en qué etapa del pipeline se perdió el registro"""
    print(f"\n🔍 ANÁLISIS DE PÉRDIDA DEL REGISTRO:")
    
    etapas = {
        'parquet_exports': 'Exportación inicial a Parquet',
        'csv_exports': 'Conversión a CSV',
        'csv_final_procesado': 'Procesamiento final',
        'temp': 'Archivos temporales'
    }
    
    etapas_encontradas = []
    for archivo in archivos_encontrados:
        for etapa, descripcion in etapas.items():
            if etapa in archivo:
                etapas_encontradas.append((etapa, descripcion, archivo))
                break
    
    print(f"   📋 Etapas donde se encontró el registro:")
    for etapa, descripcion, archivo in etapas_encontradas:
        print(f"      ✅ {descripcion}: {archivo}")
    
    # Determinar dónde se perdió
    if any('csv_final_procesado' in archivo for archivo in archivos_encontrados):
        print(f"\n   ⚠️  REGISTRO EXISTE EN ARCHIVO FINAL - Posible error en comparación")
    elif any('csv_exports' in archivo for archivo in archivos_encontrados):
        print(f"\n   ❌ REGISTRO SE PERDIÓ en el procesamiento final")
    elif any('parquet_exports' in archivo for archivo in archivos_encontrados):
        print(f"\n   ❌ REGISTRO SE PERDIÓ en la conversión a CSV")
    else:
        print(f"\n   ❌ REGISTRO SE PERDIÓ en etapa temprana del pipeline")

if __name__ == "__main__":
    print("🕵️ DETECTIVE DE REGISTROS FALTANTES - INVESTIGACIÓN PROFUNDA")
    print("=" * 80)
    
    solo_en_original, solo_en_modernizado = buscar_en_archivos_temporales()
    
    print(f"\n✅ Investigación completada")
