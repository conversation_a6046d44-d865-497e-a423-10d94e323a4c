#!/usr/bin/env python3
"""
Script para verificar la lógica de procesamiento de CHANGE_AUTH_FACTOR → CPIN
"""
import pandas as pd
import os

def verificar_logica_cpin():
    """Verifica la lógica completa de procesamiento CPIN"""
    print("🔍 VERIFICACIÓN DE LÓGICA CHANGE_AUTH_FACTOR → CPIN")
    print("=" * 60)
    
    documento = "71793435"
    celular = "51907368782"
    
    print(f"🎯 Usuario objetivo:")
    print(f"   • Documento: {documento}")
    print(f"   • Celular: {celular}")
    
    # 1. Verificar USER_AUTH_CHANGE_HISTORY
    print(f"\n1️⃣ VERIFICACIÓN USER_AUTH_CHANGE_HISTORY:")
    verificar_auth_change_history(documento, celular)
    
    # 2. Verificar USER_DATA_TRX (para JOIN)
    print(f"\n2️⃣ VERIFICACIÓN USER_DATA_TRX (para JOIN):")
    verificar_user_data_trx(documento, celular)
    
    # 3. Verificar USER_IDENTIFIER (para JOIN)
    print(f"\n3️⃣ VERIFICACIÓN USER_IDENTIFIER (para JOIN):")
    verificar_user_identifier(documento, celular)
    
    # 4. Simular JOIN completo
    print(f"\n4️⃣ SIMULACIÓN DE JOIN COMPLETO:")
    simular_join_completo(documento, celular)

def verificar_auth_change_history(documento, celular):
    """Verifica registros en USER_AUTH_CHANGE_HISTORY"""
    archivo = "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/TEMP_LOGS_USUARIOS/20250609/USER_AUTH_CHANGE_HISTORY.parquet"
    
    if os.path.exists(archivo):
        try:
            df = pd.read_parquet(archivo)
            print(f"   📄 USER_AUTH_CHANGE_HISTORY.parquet")
            print(f"      Total registros: {len(df):,}")
            print(f"      Columnas: {list(df.columns)}")
            
            # Buscar registros CHANGE_AUTH_FACTOR
            change_auth = df[df['MODIFICATION_TYPE'] == 'CHANGE_AUTH_FACTOR']
            print(f"      🎯 CHANGE_AUTH_FACTOR: {len(change_auth):,} registros")
            
            if len(change_auth) > 0:
                print(f"      📊 Muestra de CHANGE_AUTH_FACTOR:")
                for i, (_, reg) in enumerate(change_auth.head(10).iterrows(), 1):
                    print(f"         {i}. {reg['MODIFIED_ON']} - Auth_ID: {reg['AUTHENTICATION_ID']}")
                
                # Buscar específicamente registros cerca de 22:52
                hora_22_52 = change_auth[change_auth['MODIFIED_ON'].astype(str).str.contains('22:5', na=False)]
                print(f"      🕐 Registros cerca de 22:5X: {len(hora_22_52)}")
                
                if len(hora_22_52) > 0:
                    print(f"         📋 Detalle 22:5X:")
                    for _, reg in hora_22_52.iterrows():
                        print(f"            {reg['MODIFIED_ON']} - Auth_ID: {reg['AUTHENTICATION_ID']}")
            
        except Exception as e:
            print(f"      ❌ Error: {e}")
    else:
        print(f"   ❌ Archivo no encontrado")

def verificar_user_data_trx(documento, celular):
    """Verifica registros en USER_DATA_TRX"""
    archivo = "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/TEMP_LOGS_USUARIOS/20250609/USER_DATA_TRX.parquet"
    
    if os.path.exists(archivo):
        try:
            df = pd.read_parquet(archivo)
            print(f"   📄 USER_DATA_TRX.parquet")
            print(f"      Total registros: {len(df):,}")
            
            # Buscar nuestro usuario
            mask = (df['ID_VALUE'].astype(str) == documento) & \
                   (df['MSISDN'].astype(str) == celular)
            usuario = df[mask]
            
            print(f"      🎯 Usuario encontrado: {len(usuario)} registros")
            
            if len(usuario) > 0:
                reg = usuario.iloc[0]
                print(f"         USER_ID: {reg['USER_ID']}")
                print(f"         O_USER_ID: {reg['O_USER_ID']}")
                print(f"         CREATED_ON: {reg['CREATED_ON']}")
                print(f"         ID_VALUE: {reg['ID_VALUE']}")
                print(f"         MSISDN: {reg['MSISDN']}")
                
                return reg['USER_ID'], reg['O_USER_ID']
            else:
                print(f"      ❌ Usuario no encontrado")
                return None, None
                
        except Exception as e:
            print(f"      ❌ Error: {e}")
            return None, None
    else:
        print(f"   ❌ Archivo no encontrado")
        return None, None

def verificar_user_identifier(documento, celular):
    """Verifica registros en USER_IDENTIFIER para el JOIN"""
    print(f"   📄 USER_IDENTIFIER (tabla S3)")
    print(f"      💡 Esta tabla se usa para hacer JOIN entre AUTH_CHANGE_HISTORY y USER_DATA_TRX")
    print(f"      🔍 JOIN: uach.AUTHENTICATION_ID = ui.AUTHENTICATION_ID")
    print(f"      🔍 JOIN: ui.USER_ID = ud.O_USER_ID")
    
    # Simular consulta para encontrar AUTHENTICATION_IDs relacionados con nuestro usuario
    print(f"      📋 Para encontrar el registro faltante necesitamos:")
    print(f"         1. AUTHENTICATION_ID que corresponda al documento {documento}")
    print(f"         2. Que tenga MODIFICATION_TYPE = 'CHANGE_AUTH_FACTOR'")
    print(f"         3. Que la fecha sea 2025-06-09 22:52:32")

def simular_join_completo(documento, celular):
    """Simula el JOIN completo para encontrar por qué falta el registro"""
    print(f"   🔍 SIMULANDO JOIN COMPLETO:")
    
    # Cargar archivos
    auth_file = "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/TEMP_LOGS_USUARIOS/20250609/USER_AUTH_CHANGE_HISTORY.parquet"
    user_data_file = "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/TEMP_LOGS_USUARIOS/20250609/USER_DATA_TRX.parquet"
    
    if os.path.exists(auth_file) and os.path.exists(user_data_file):
        try:
            df_auth = pd.read_parquet(auth_file)
            df_user = pd.read_parquet(user_data_file)
            
            # Encontrar nuestro usuario en USER_DATA_TRX
            mask_user = (df_user['ID_VALUE'].astype(str) == documento) & \
                       (df_user['MSISDN'].astype(str) == celular)
            usuario = df_user[mask_user]
            
            if len(usuario) > 0:
                user_id = usuario.iloc[0]['USER_ID']
                o_user_id = usuario.iloc[0]['O_USER_ID']
                
                print(f"      ✅ Usuario encontrado:")
                print(f"         USER_ID: {user_id}")
                print(f"         O_USER_ID: {o_user_id}")
                
                # Buscar CHANGE_AUTH_FACTOR cerca de 22:52
                change_auth_22_52 = df_auth[
                    (df_auth['MODIFICATION_TYPE'] == 'CHANGE_AUTH_FACTOR') &
                    (df_auth['MODIFIED_ON'].astype(str).str.contains('22:5', na=False))
                ]
                
                print(f"      🕐 CHANGE_AUTH_FACTOR cerca de 22:5X: {len(change_auth_22_52)}")
                
                if len(change_auth_22_52) > 0:
                    print(f"         📋 Candidatos para nuestro usuario:")
                    for i, (_, reg) in enumerate(change_auth_22_52.iterrows(), 1):
                        print(f"            {i}. {reg['MODIFIED_ON']} - Auth_ID: {reg['AUTHENTICATION_ID']}")
                    
                    # PROBLEMA IDENTIFICADO: Necesitamos USER_IDENTIFIER para hacer el JOIN
                    print(f"\n      ❌ PROBLEMA IDENTIFICADO:")
                    print(f"         El JOIN requiere USER_IDENTIFIER que no está disponible localmente")
                    print(f"         JOIN: uach.AUTHENTICATION_ID = ui.AUTHENTICATION_ID")
                    print(f"         JOIN: ui.USER_ID = ud.O_USER_ID")
                    print(f"         Sin USER_IDENTIFIER, no podemos conectar AUTH_CHANGE_HISTORY con USER_DATA_TRX")
                    
                    print(f"\n      💡 SOLUCIÓN REQUERIDA:")
                    print(f"         1. Verificar que USER_IDENTIFIER esté disponible en S3")
                    print(f"         2. O modificar la lógica para usar un JOIN directo diferente")
                    print(f"         3. O usar una lógica alternativa para generar CPIN")
                
            else:
                print(f"      ❌ Usuario no encontrado en USER_DATA_TRX")
                
        except Exception as e:
            print(f"      ❌ Error en simulación: {e}")
    else:
        print(f"      ❌ Archivos necesarios no encontrados")

def analizar_problema_join():
    """Analiza el problema específico del JOIN"""
    print(f"\n5️⃣ ANÁLISIS DEL PROBLEMA DE JOIN:")
    
    print(f"   🔍 LÓGICA ACTUAL DEL PIPELINE:")
    print(f"      1. USER_AUTH_CHANGE_HISTORY tiene CHANGE_AUTH_FACTOR")
    print(f"      2. JOIN con USER_IDENTIFIER por AUTHENTICATION_ID")
    print(f"      3. JOIN con USER_DATA_TRX por USER_ID")
    print(f"      4. Generar registro CPIN")
    
    print(f"\n   ❌ PROBLEMA IDENTIFICADO:")
    print(f"      • USER_IDENTIFIER puede no tener el AUTHENTICATION_ID correcto")
    print(f"      • O el JOIN está fallando por alguna razón")
    print(f"      • O hay un filtro que excluye nuestro registro específico")
    
    print(f"\n   🔧 SOLUCIONES POSIBLES:")
    print(f"      A. Verificar datos en USER_IDENTIFIER de S3")
    print(f"      B. Modificar lógica de JOIN")
    print(f"      C. Agregar logs de debug al pipeline")
    print(f"      D. Crear lógica alternativa para CPIN")

if __name__ == "__main__":
    print("🕵️ VERIFICACIÓN DE LÓGICA CHANGE_AUTH_FACTOR → CPIN")
    print("=" * 70)
    
    verificar_logica_cpin()
    analizar_problema_join()
    
    print(f"\n✅ Verificación completada")
    print(f"\n📋 CONCLUSIÓN:")
    print(f"   El pipeline tiene la lógica correcta para procesar CHANGE_AUTH_FACTOR")
    print(f"   El problema está en el JOIN con USER_IDENTIFIER")
    print(f"   Necesitamos verificar/corregir la tabla USER_IDENTIFIER o la lógica de JOIN")
