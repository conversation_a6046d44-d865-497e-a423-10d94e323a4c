#!/usr/bin/env python3
"""
Script para encontrar el registro faltante entre el archivo modernizado y el original
"""
import pandas as pd
import sys
from collections import Counter

def comparar_archivos_detallado():
    """Compara los archivos línea por línea para encontrar diferencias"""
    print("🔍 BÚSQUEDA DEL REGISTRO FALTANTE")
    print("=" * 50)
    
    # Rutas de los archivos
    archivo_modernizado = "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/output/20250609/csv_exports/csv_final_procesado/LOGUSR-FCOMPARTAMOS-20250609074148.csv"
    archivo_original = "/home/<USER>/generate/log_usuarios/output/LOGUSR-FCOMPARTAMOS-20250610052953.csv"
    
    print(f"📄 Archivo modernizado: {archivo_modernizado}")
    print(f"📄 Archivo original: {archivo_original}")
    
    try:
        # Leer ambos archivos
        print("\n📊 Cargando archivos...")
        df_modernizado = pd.read_csv(archivo_modernizado, header=None)
        df_original = pd.read_csv(archivo_original, header=None)
        
        print(f"   • Modernizado: {len(df_modernizado):,} registros")
        print(f"   • Original: {len(df_original):,} registros")
        print(f"   • Diferencia: {len(df_original) - len(df_modernizado):,} registros")
        
        # Asignar nombres de columnas
        columnas = ['OPERACION', 'TRANSACTIONID', 'FECHA_HORA', 'CANAL', 'TIPODOCUMENTO', 
                   'DOCUMENTO', 'CELULAR', 'EMPRESA'] + [f'COL{i}' for i in range(8, 30)]
        
        df_modernizado.columns = columnas
        df_original.columns = columnas
        
        # Crear una clave única para cada registro (excluyendo TRANSACTIONID que puede variar)
        print("\n🔑 Creando claves únicas para comparación...")
        
        def crear_clave(row):
            # Usar campos que deberían ser idénticos entre sistemas
            return f"{row['OPERACION']}|{row['FECHA_HORA']}|{row['TIPODOCUMENTO']}|{row['DOCUMENTO']}|{row['CELULAR']}|{row['EMPRESA']}"
        
        df_modernizado['CLAVE'] = df_modernizado.apply(crear_clave, axis=1)
        df_original['CLAVE'] = df_original.apply(crear_clave, axis=1)
        
        # Encontrar registros únicos en cada archivo
        claves_modernizado = set(df_modernizado['CLAVE'])
        claves_original = set(df_original['CLAVE'])
        
        # Registros que están en original pero no en modernizado
        solo_en_original = claves_original - claves_modernizado
        # Registros que están en modernizado pero no en original  
        solo_en_modernizado = claves_modernizado - claves_original
        
        print(f"\n📋 ANÁLISIS DE DIFERENCIAS:")
        print(f"   • Registros solo en original: {len(solo_en_original)}")
        print(f"   • Registros solo en modernizado: {len(solo_en_modernizado)}")
        
        # Mostrar registros faltantes en modernizado
        if solo_en_original:
            print(f"\n❌ REGISTROS QUE FALTAN EN EL PIPELINE MODERNIZADO:")
            for i, clave in enumerate(solo_en_original, 1):
                registro = df_original[df_original['CLAVE'] == clave].iloc[0]
                print(f"\n   🔸 Registro faltante #{i}:")
                print(f"      • Operación: {registro['OPERACION']}")
                print(f"      • TransactionID: {registro['TRANSACTIONID']}")
                print(f"      • Fecha/Hora: {registro['FECHA_HORA']}")
                print(f"      • Documento: {registro['TIPODOCUMENTO']} {registro['DOCUMENTO']}")
                print(f"      • Celular: {registro['CELULAR']}")
                print(f"      • Empresa: {registro['EMPRESA']}")
                
                # Mostrar registro completo
                print(f"      • Registro completo:")
                for j, col in enumerate(columnas[:15]):  # Mostrar primeras 15 columnas
                    if pd.notna(registro[col]) and str(registro[col]).strip() != '':
                        print(f"        - {col}: {registro[col]}")
        
        # Mostrar registros extra en modernizado
        if solo_en_modernizado:
            print(f"\n➕ REGISTROS EXTRA EN EL PIPELINE MODERNIZADO:")
            for i, clave in enumerate(solo_en_modernizado, 1):
                registro = df_modernizado[df_modernizado['CLAVE'] == clave].iloc[0]
                print(f"\n   🔸 Registro extra #{i}:")
                print(f"      • Operación: {registro['OPERACION']}")
                print(f"      • TransactionID: {registro['TRANSACTIONID']}")
                print(f"      • Fecha/Hora: {registro['FECHA_HORA']}")
                print(f"      • Documento: {registro['TIPODOCUMENTO']} {registro['DOCUMENTO']}")
                print(f"      • Celular: {registro['CELULAR']}")
                print(f"      • Empresa: {registro['EMPRESA']}")
        
        # Análisis por operación
        print(f"\n📊 COMPARACIÓN POR OPERACIÓN:")
        ops_modernizado = df_modernizado['OPERACION'].value_counts()
        ops_original = df_original['OPERACION'].value_counts()
        
        todas_ops = set(ops_modernizado.index) | set(ops_original.index)
        
        for op in sorted(todas_ops):
            count_mod = ops_modernizado.get(op, 0)
            count_orig = ops_original.get(op, 0)
            diff = count_orig - count_mod
            
            status = "✅" if diff == 0 else "❌" if diff > 0 else "➕"
            print(f"   {status} {op}: Modernizado={count_mod:,}, Original={count_orig:,}, Diff={diff:+,}")
        
        # Análisis temporal
        print(f"\n⏰ ANÁLISIS TEMPORAL:")
        
        # Extraer hora de los registros
        df_modernizado['HORA'] = df_modernizado['FECHA_HORA'].str[11:16]
        df_original['HORA'] = df_original['FECHA_HORA'].str[11:16]
        
        print(f"   • Rango temporal modernizado: {df_modernizado['HORA'].min()} - {df_modernizado['HORA'].max()}")
        print(f"   • Rango temporal original: {df_original['HORA'].min()} - {df_original['HORA'].max()}")
        
        # Si hay registros faltantes, analizar su distribución temporal
        if solo_en_original:
            registros_faltantes = df_original[df_original['CLAVE'].isin(solo_en_original)]
            print(f"\n⏰ DISTRIBUCIÓN TEMPORAL DE REGISTROS FALTANTES:")
            for _, registro in registros_faltantes.iterrows():
                print(f"   • {registro['FECHA_HORA']} - {registro['OPERACION']} - {registro['DOCUMENTO']}")
        
        return solo_en_original, solo_en_modernizado
        
    except Exception as e:
        print(f"❌ Error en la comparación: {e}")
        return None, None

if __name__ == "__main__":
    print("🕵️ DETECTIVE DE REGISTROS FALTANTES")
    print("=" * 60)
    
    faltantes, extras = comparar_archivos_detallado()
    
    if faltantes is not None:
        print(f"\n✅ Análisis completado")
        print(f"📊 Resumen: {len(faltantes)} registros faltantes, {len(extras)} registros extra")
    else:
        print(f"\n❌ No se pudo completar el análisis")
