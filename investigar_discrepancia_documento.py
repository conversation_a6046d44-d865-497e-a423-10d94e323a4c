#!/usr/bin/env python3
"""
Investigación profunda de por qué el documento 76730654 no cuadra
cuando los demás registros sí cuadran perfectamente
"""
import pandas as pd
import os

def investigar_discrepancia_documento():
    """Investiga profundamente la discrepancia del documento específico"""
    print("🕵️ INVESTIGACIÓN PROFUNDA DE DISCREPANCIA")
    print("=" * 55)
    
    documento_problema = "76730654"
    
    print(f"🎯 Documento problema: {documento_problema}")
    print(f"❓ Pregunta: ¿Por qué este documento no cuadra si los demás sí?")
    
    # Archivos a analizar
    archivo_modernizado = "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/output/20250609/csv_exports/csv_final_procesado/LOGUSR-FCOMPARTAMOS-20250609081855.csv"
    archivo_oracle = "/home/<USER>/generate/log_usuarios/output/LOGUSR-FCOMPARTAMOS-20250610052953.csv"
    
    # 1. Verificar si el documento existe en archivos temporales
    print(f"\n1️⃣ RASTREO EN ARCHIVOS TEMPORALES:")
    rastrear_en_temporales(documento_problema)
    
    # 2. Buscar el documento en diferentes etapas del pipeline
    print(f"\n2️⃣ RASTREO EN ETAPAS DEL PIPELINE:")
    rastrear_en_pipeline(documento_problema)
    
    # 3. Comparar con documentos que SÍ cuadran
    print(f"\n3️⃣ COMPARACIÓN CON DOCUMENTOS QUE CUADRAN:")
    comparar_con_documentos_correctos(archivo_modernizado, archivo_oracle, documento_problema)
    
    # 4. Análisis temporal detallado
    print(f"\n4️⃣ ANÁLISIS TEMPORAL DETALLADO:")
    analizar_temporal_detallado(documento_problema)
    
    # 5. Verificar si hay duplicados o filtros
    print(f"\n5️⃣ VERIFICACIÓN DE DUPLICADOS Y FILTROS:")
    verificar_duplicados_filtros(documento_problema)

def rastrear_en_temporales(documento):
    """Rastrea el documento en archivos temporales"""
    archivos_temporales = [
        "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/TEMP_LOGS_USUARIOS/20250609/USER_DATA_TRX.parquet",
        "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/TEMP_LOGS_USUARIOS/20250609/USER_AUTH_CHANGE_HISTORY.parquet",
        "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/output/20250609/LOG_USR.parquet"
    ]
    
    for archivo in archivos_temporales:
        if os.path.exists(archivo):
            try:
                df = pd.read_parquet(archivo)
                print(f"   📄 {os.path.basename(archivo)}:")
                print(f"      Total registros: {len(df):,}")
                
                # Buscar el documento
                if 'ID_VALUE' in df.columns:  # USER_DATA_TRX
                    mask = df['ID_VALUE'].astype(str) == documento
                    registros = df[mask]
                    print(f"      🎯 Registros documento {documento}: {len(registros)}")
                    
                    if len(registros) > 0:
                        for _, reg in registros.iterrows():
                            print(f"         USER_ID: {reg['USER_ID']} - CREATED_ON: {reg['CREATED_ON']}")
                
                elif 'DOCUMENTO' in df.columns:  # LOG_USR
                    mask = df['DOCUMENTO'].astype(str) == documento
                    registros = df[mask]
                    print(f"      🎯 Registros documento {documento}: {len(registros)}")
                    
                    if len(registros) > 0:
                        for _, reg in registros.iterrows():
                            print(f"         {reg['REQUESTTYPE']} - {reg['CREATEDON']} - UserHistId: {reg['USERHISTID']}")
                
                else:
                    print(f"      ⚠️  No se puede buscar documento en esta tabla")
                    
            except Exception as e:
                print(f"      ❌ Error: {e}")
        else:
            print(f"   ❌ Archivo no encontrado: {archivo}")

def rastrear_en_pipeline(documento):
    """Rastrea el documento en diferentes etapas del pipeline"""
    archivos_pipeline = [
        "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/output/20250609/LOG_USR.parquet",
        "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/output/20250609/LOG_USR_DEDUPLICATED.parquet"
    ]
    
    for archivo in archivos_pipeline:
        if os.path.exists(archivo):
            try:
                df = pd.read_parquet(archivo)
                print(f"   📄 {os.path.basename(archivo)}:")
                print(f"      Total registros: {len(df):,}")
                
                # Buscar el documento
                mask = df['DOCUMENTO'].astype(str) == documento
                registros = df[mask]
                print(f"      🎯 Registros documento {documento}: {len(registros)}")
                
                if len(registros) > 0:
                    for _, reg in registros.iterrows():
                        print(f"         {reg['REQUESTTYPE']} - {reg['CREATEDON']} - UserHistId: {reg['USERHISTID']}")
                        
                        # Verificar si es CHANGE_AUTH_FACTOR
                        if reg['REQUESTTYPE'] == 'CHANGE_AUTH_FACTOR':
                            print(f"            🎯 CHANGE_AUTH_FACTOR encontrado!")
                            print(f"               CREATEDON: {reg['CREATEDON']}")
                            print(f"               USERHISTID: {reg['USERHISTID']}")
                            print(f"               MSISDN: {reg['MSISDN']}")
                
            except Exception as e:
                print(f"      ❌ Error: {e}")

def comparar_con_documentos_correctos(archivo_modernizado, archivo_oracle, documento_problema):
    """Compara con documentos que SÍ cuadran correctamente"""
    try:
        # Leer ambos archivos
        df_mod = pd.read_csv(archivo_modernizado, header=None)
        df_ora = pd.read_csv(archivo_oracle, header=None)
        
        # Asignar columnas
        columnas = ['OPERACION', 'TRANSACTIONID', 'FECHA_HORA', 'CANAL', 'TIPODOCUMENTO', 
                   'DOCUMENTO', 'CELULAR', 'EMPRESA'] + [f'COL{i}' for i in range(8, 30)]
        
        df_mod.columns = columnas[:len(df_mod.columns)]
        df_ora.columns = columnas[:len(df_ora.columns)]
        
        print(f"   📊 Buscando documentos que cuadran perfectamente...")
        
        # Encontrar documentos que aparecen en ambos archivos
        docs_mod = set(df_mod['DOCUMENTO'].astype(str))
        docs_ora = set(df_ora['DOCUMENTO'].astype(str))
        docs_comunes = docs_mod & docs_ora
        
        # Analizar algunos documentos que cuadran
        documentos_correctos = []
        
        for doc in list(docs_comunes)[:20]:  # Analizar 20 documentos
            if doc == documento_problema:
                continue
                
            # Registros del documento en ambos archivos
            regs_mod = df_mod[df_mod['DOCUMENTO'].astype(str) == doc]
            regs_ora = df_ora[df_ora['DOCUMENTO'].astype(str) == doc]
            
            # Crear claves de negocio (sin TransactionID)
            claves_mod = set()
            claves_ora = set()
            
            for _, reg in regs_mod.iterrows():
                clave = f"{reg['OPERACION']}|{reg['FECHA_HORA']}|{reg['DOCUMENTO']}|{reg['CELULAR']}"
                claves_mod.add(clave)
            
            for _, reg in regs_ora.iterrows():
                clave = f"{reg['OPERACION']}|{reg['FECHA_HORA']}|{reg['DOCUMENTO']}|{reg['CELULAR']}"
                claves_ora.add(clave)
            
            # Verificar si cuadra perfectamente
            if claves_mod == claves_ora and len(claves_mod) > 0:
                documentos_correctos.append({
                    'documento': doc,
                    'registros': len(regs_mod),
                    'operaciones': list(regs_mod['OPERACION'].unique())
                })
        
        print(f"   ✅ Documentos que cuadran perfectamente: {len(documentos_correctos)}")
        
        # Mostrar algunos ejemplos
        for i, doc_info in enumerate(documentos_correctos[:5], 1):
            print(f"      {i}. Doc: {doc_info['documento']} - {doc_info['registros']} registros - Ops: {doc_info['operaciones']}")
        
        # Comparar patrón con documento problema
        if documentos_correctos:
            doc_ejemplo = documentos_correctos[0]['documento']
            print(f"\n   🔍 COMPARANDO CON DOCUMENTO CORRECTO: {doc_ejemplo}")
            
            # Analizar documento correcto
            regs_mod_ej = df_mod[df_mod['DOCUMENTO'].astype(str) == doc_ejemplo]
            regs_ora_ej = df_ora[df_ora['DOCUMENTO'].astype(str) == doc_ejemplo]
            
            print(f"      📊 Documento {doc_ejemplo}:")
            print(f"         Modernizado: {len(regs_mod_ej)} registros")
            print(f"         Oracle: {len(regs_ora_ej)} registros")
            
            for _, reg in regs_mod_ej.iterrows():
                print(f"         MOD: {reg['OPERACION']} - {reg['FECHA_HORA']}")
            
            for _, reg in regs_ora_ej.iterrows():
                print(f"         ORA: {reg['OPERACION']} - {reg['FECHA_HORA']}")
            
            # Comparar con documento problema
            regs_mod_prob = df_mod[df_mod['DOCUMENTO'].astype(str) == documento_problema]
            regs_ora_prob = df_ora[df_ora['DOCUMENTO'].astype(str) == documento_problema]
            
            print(f"\n      📊 Documento problema {documento_problema}:")
            print(f"         Modernizado: {len(regs_mod_prob)} registros")
            print(f"         Oracle: {len(regs_ora_prob)} registros")
            
            for _, reg in regs_mod_prob.iterrows():
                print(f"         MOD: {reg['OPERACION']} - {reg['FECHA_HORA']}")
            
            for _, reg in regs_ora_prob.iterrows():
                print(f"         ORA: {reg['OPERACION']} - {reg['FECHA_HORA']}")
        
    except Exception as e:
        print(f"   ❌ Error: {e}")

def analizar_temporal_detallado(documento):
    """Análisis temporal detallado del documento problema"""
    print(f"   🕐 Analizando patrones temporales...")
    
    # Verificar si hay registros del documento en diferentes horas
    archivo_log_usr = "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/output/20250609/LOG_USR.parquet"
    
    if os.path.exists(archivo_log_usr):
        try:
            df = pd.read_parquet(archivo_log_usr)
            
            # Buscar todos los registros del documento
            mask = df['DOCUMENTO'].astype(str) == documento
            registros = df[mask]
            
            print(f"      📊 Registros en LOG_USR.parquet: {len(registros)}")
            
            if len(registros) > 0:
                print(f"      📋 Todos los registros del documento:")
                for _, reg in registros.iterrows():
                    print(f"         {reg['REQUESTTYPE']} - {reg['CREATEDON']} - UserHistId: {reg['USERHISTID']}")
                
                # Verificar si hay múltiples CHANGE_AUTH_FACTOR
                change_auth = registros[registros['REQUESTTYPE'] == 'CHANGE_AUTH_FACTOR']
                print(f"      🎯 CHANGE_AUTH_FACTOR: {len(change_auth)}")
                
                if len(change_auth) > 1:
                    print(f"         ⚠️  MÚLTIPLES CHANGE_AUTH_FACTOR encontrados!")
                    for _, reg in change_auth.iterrows():
                        print(f"            {reg['CREATEDON']} - UserHistId: {reg['USERHISTID']}")
                elif len(change_auth) == 1:
                    reg = change_auth.iloc[0]
                    print(f"         ✅ UN CHANGE_AUTH_FACTOR: {reg['CREATEDON']}")
                else:
                    print(f"         ❌ NO hay CHANGE_AUTH_FACTOR")
            
        except Exception as e:
            print(f"      ❌ Error: {e}")

def verificar_duplicados_filtros(documento):
    """Verifica si hay duplicados o filtros que afecten al documento"""
    print(f"   🔍 Verificando duplicados y filtros...")
    
    # Verificar en archivo deduplicado
    archivo_dedup = "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/output/20250609/LOG_USR_DEDUPLICATED.parquet"
    
    if os.path.exists(archivo_dedup):
        try:
            df = pd.read_parquet(archivo_dedup)
            
            # Buscar el documento
            mask = df['DOCUMENTO'].astype(str) == documento
            registros = df[mask]
            
            print(f"      📊 Registros en LOG_USR_DEDUPLICATED.parquet: {len(registros)}")
            
            if len(registros) > 0:
                for _, reg in registros.iterrows():
                    print(f"         {reg['REQUESTTYPE']} - {reg['CREATEDON']} - UserHistId: {reg['USERHISTID']}")
            
            # Verificar si se perdió durante deduplicación
            archivo_original = "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/output/20250609/LOG_USR.parquet"
            if os.path.exists(archivo_original):
                df_orig = pd.read_parquet(archivo_original)
                mask_orig = df_orig['DOCUMENTO'].astype(str) == documento
                registros_orig = df_orig[mask_orig]
                
                print(f"      📊 Comparación deduplicación:")
                print(f"         Original: {len(registros_orig)} registros")
                print(f"         Deduplicado: {len(registros)} registros")
                print(f"         Eliminados: {len(registros_orig) - len(registros)} registros")
                
                if len(registros_orig) > len(registros):
                    print(f"         ⚠️  Se eliminaron registros durante deduplicación!")
                    
                    # Mostrar qué se eliminó
                    userhistids_orig = set(registros_orig['USERHISTID'])
                    userhistids_dedup = set(registros['USERHISTID'])
                    eliminados = userhistids_orig - userhistids_dedup
                    
                    if eliminados:
                        print(f"         🗑️  UserHistIds eliminados: {eliminados}")
            
        except Exception as e:
            print(f"      ❌ Error: {e}")

if __name__ == "__main__":
    print("🕵️ INVESTIGACIÓN PROFUNDA - ¿POR QUÉ NO CUADRA ESTE DOCUMENTO?")
    print("=" * 80)
    
    investigar_discrepancia_documento()
    
    print(f"\n📋 HIPÓTESIS A VERIFICAR:")
    print(f"   1. ¿El documento tiene múltiples CHANGE_AUTH_FACTOR?")
    print(f"   2. ¿Se perdió un registro durante deduplicación?")
    print(f"   3. ¿Hay diferencias en ventanas temporales de captura?")
    print(f"   4. ¿El procesamiento de CHANGE_AUTH_FACTOR tiene algún filtro?")
    
    print(f"\n✅ Investigación completada")
