#!/usr/bin/env python3
"""
Script para verificar si se generó el registro CPIN faltante después de la corrección
"""
import pandas as pd
import os
import glob

def verificar_registro_cpin_corregido():
    """Verifica si se generó el registro CPIN específico"""
    print("🔍 VERIFICACIÓN DEL REGISTRO CPIN CORREGIDO")
    print("=" * 50)
    
    documento = "71793435"
    celular = "51907368782"
    
    print(f"🎯 Buscando registro específico:")
    print(f"   • Documento: {documento}")
    print(f"   • Celular: {celular}")
    print(f"   • Operación esperada: CPIN")
    print(f"   • Hora esperada: ~22:52:32")
    
    # Buscar el archivo más reciente
    archivos_corregidos = glob.glob("output/20250609/csv_exports/csv_final_procesado/LOGUSR-FCOMPARTAMOS-*.csv")
    
    if not archivos_corregidos:
        print(f"❌ No se encontraron archivos corregidos")
        return False
    
    # Tomar el archivo más reciente
    archivo_corregido = max(archivos_corregidos, key=os.path.getctime)
    print(f"📄 Analizando: {os.path.basename(archivo_corregido)}")
    
    try:
        df = pd.read_csv(archivo_corregido, header=None)
        
        # Asignar nombres de columnas
        columnas = ['OPERACION', 'TRANSACTIONID', 'FECHA_HORA', 'CANAL', 'TIPODOCUMENTO', 
                   'DOCUMENTO', 'CELULAR', 'EMPRESA'] + [f'COL{i}' for i in range(8, 30)]
        df.columns = columnas[:len(df.columns)]
        
        print(f"📊 Estadísticas del archivo corregido:")
        print(f"   • Total registros: {len(df):,}")
        
        # Contar operaciones CPIN
        cpin_total = len(df[df['OPERACION'] == 'CPIN'])
        print(f"   • Total CPIN: {cpin_total:,}")
        
        # Buscar nuestro usuario específico
        mask_usuario = (df['DOCUMENTO'].astype(str) == documento) & \
                      (df['CELULAR'].astype(str) == celular)
        
        registros_usuario = df[mask_usuario]
        print(f"   • Registros del usuario: {len(registros_usuario)}")
        
        if len(registros_usuario) > 0:
            print(f"\n📋 OPERACIONES DEL USUARIO:")
            operaciones = registros_usuario['OPERACION'].value_counts()
            for op, count in operaciones.items():
                print(f"   • {op}: {count}")
            
            # Verificar si ahora tiene CPIN
            cpin_usuario = registros_usuario[registros_usuario['OPERACION'] == 'CPIN']
            
            if len(cpin_usuario) > 0:
                print(f"\n✅ ¡ÉXITO! Usuario ahora tiene {len(cpin_usuario)} operación(es) CPIN:")
                
                for i, (_, reg) in enumerate(cpin_usuario.iterrows(), 1):
                    print(f"   {i}. CPIN: {reg['FECHA_HORA']} - TID: {reg['TRANSACTIONID']}")
                    
                    # Verificar si es el registro específico que buscábamos
                    if "22:52" in str(reg['FECHA_HORA']):
                        print(f"      🏆 ¡REGISTRO FALTANTE ENCONTRADO!")
                        print(f"         ✅ Fecha/Hora: {reg['FECHA_HORA']}")
                        print(f"         ✅ TransactionID: {reg['TRANSACTIONID']}")
                        print(f"         ✅ Documento: {reg['DOCUMENTO']}")
                        print(f"         ✅ Celular: {reg['CELULAR']}")
                        return True
                
                print(f"\n📊 Análisis temporal de CPIN del usuario:")
                for _, reg in cpin_usuario.iterrows():
                    hora = str(reg['FECHA_HORA'])[11:16] if len(str(reg['FECHA_HORA'])) >= 16 else "N/A"
                    print(f"   • {hora} - TID: {reg['TRANSACTIONID']}")
                
                return True  # Usuario tiene CPIN, aunque no sea el específico de 22:52
            else:
                print(f"\n❌ Usuario aún NO tiene operaciones CPIN")
                
                # Mostrar todas las operaciones para debug
                print(f"\n📋 Todas las operaciones del usuario:")
                for _, reg in registros_usuario.iterrows():
                    print(f"   • {reg['OPERACION']}: {reg['FECHA_HORA']} - TID: {reg['TRANSACTIONID']}")
                
                return False
        else:
            print(f"\n❌ Usuario no encontrado en archivo corregido")
            return False
            
    except Exception as e:
        print(f"❌ Error verificando archivo: {e}")
        return False

def comparar_con_original():
    """Compara con el archivo original"""
    print(f"\n📊 COMPARACIÓN CON ARCHIVO ORIGINAL:")
    
    # Archivo original
    archivo_original = "/home/<USER>/generate/log_usuarios/output/LOGUSR-FCOMPARTAMOS-20250610052953.csv"
    
    # Archivo corregido
    archivos_corregidos = glob.glob("output/20250609/csv_exports/csv_final_procesado/LOGUSR-FCOMPARTAMOS-*.csv")
    
    if not archivos_corregidos:
        print(f"❌ No se encontró archivo corregido")
        return
    
    archivo_corregido = max(archivos_corregidos, key=os.path.getctime)
    
    try:
        # Leer archivos
        df_original = pd.read_csv(archivo_original, header=None)
        df_corregido = pd.read_csv(archivo_corregido, header=None)
        
        print(f"📊 Comparación de conteos:")
        print(f"   • Original: {len(df_original):,} registros")
        print(f"   • Corregido: {len(df_corregido):,} registros")
        
        diferencia = len(df_corregido) - len(df_original)
        print(f"   • Diferencia: {diferencia:+,} registros")
        
        if diferencia == 0:
            print(f"   🎯 ¡COINCIDENCIA EXACTA!")
        elif diferencia == 1:
            print(f"   ✅ ¡PROBLEMA RESUELTO! Se agregó exactamente 1 registro")
        elif diferencia > 0:
            print(f"   ⚠️  Se agregaron más registros de los esperados")
        else:
            print(f"   ❌ Se perdieron registros")
        
        # Comparar operaciones CPIN
        columnas = ['OPERACION', 'TRANSACTIONID', 'FECHA_HORA', 'CANAL', 'TIPODOCUMENTO', 
                   'DOCUMENTO', 'CELULAR', 'EMPRESA'] + [f'COL{i}' for i in range(8, 30)]
        
        df_original.columns = columnas[:len(df_original.columns)]
        df_corregido.columns = columnas[:len(df_corregido.columns)]
        
        cpin_original = len(df_original[df_original['OPERACION'] == 'CPIN'])
        cpin_corregido = len(df_corregido[df_corregido['OPERACION'] == 'CPIN'])
        
        print(f"\n📊 Comparación CPIN:")
        print(f"   • Original: {cpin_original:,} registros CPIN")
        print(f"   • Corregido: {cpin_corregido:,} registros CPIN")
        
        diferencia_cpin = cpin_corregido - cpin_original
        print(f"   • Diferencia CPIN: {diferencia_cpin:+,} registros")
        
        if diferencia_cpin == 0:
            print(f"   🎯 ¡COINCIDENCIA EXACTA EN CPIN!")
        elif diferencia_cpin == 1:
            print(f"   ✅ ¡PERFECTO! Se agregó exactamente 1 registro CPIN")
        elif diferencia_cpin > 0:
            print(f"   ✅ Mejora: Se agregaron registros CPIN")
        else:
            print(f"   ❌ Problema: Se perdieron registros CPIN")
            
    except Exception as e:
        print(f"❌ Error en comparación: {e}")

def verificar_logs_pipeline():
    """Verifica los logs del pipeline para confirmar el procesamiento"""
    print(f"\n📋 VERIFICACIÓN DE LOGS DEL PIPELINE:")
    
    log_file = "pipeline_log_usuarios.log"
    
    if os.path.exists(log_file):
        try:
            with open(log_file, 'r') as f:
                contenido = f.read()
            
            # Buscar líneas relevantes de la última ejecución
            lineas = contenido.split('\n')
            lineas_relevantes = []
            
            # Buscar desde la última ejecución
            for i, linea in enumerate(reversed(lineas)):
                if 'CHANGE_AUTH_FACTOR' in linea:
                    lineas_relevantes.append(linea)
                if len(lineas_relevantes) >= 5:  # Últimas 5 líneas relevantes
                    break
            
            if lineas_relevantes:
                print(f"   📄 Últimas líneas relevantes de CHANGE_AUTH_FACTOR:")
                for linea in reversed(lineas_relevantes):
                    print(f"      {linea}")
            else:
                print(f"   ⚠️  No se encontraron líneas de CHANGE_AUTH_FACTOR en logs")
                
        except Exception as e:
            print(f"   ❌ Error leyendo logs: {e}")
    else:
        print(f"   ❌ Archivo de log no encontrado")

if __name__ == "__main__":
    print("🧪 VERIFICACIÓN DE CORRECCIÓN DEL REGISTRO CPIN")
    print("=" * 60)
    
    # Verificar registro específico
    exito = verificar_registro_cpin_corregido()
    
    # Comparar con original
    comparar_con_original()
    
    # Verificar logs
    verificar_logs_pipeline()
    
    print(f"\n📋 RESUMEN FINAL:")
    if exito:
        print(f"   🏆 ¡CORRECCIÓN EXITOSA!")
        print(f"   ✅ El usuario ahora tiene operaciones CPIN")
        print(f"   ✅ El pipeline procesa correctamente CHANGE_AUTH_FACTOR")
    else:
        print(f"   ⚠️  CORRECCIÓN PARCIAL")
        print(f"   📊 Se procesaron más registros CHANGE_AUTH_FACTOR")
        print(f"   🔍 Verificar si el registro específico está en otra hora")
    
    print(f"\n✅ Verificación completada")
