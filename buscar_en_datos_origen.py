#!/usr/bin/env python3
"""
Script para buscar el registro faltante en los datos de origen (archivos parquet)
"""
import pandas as pd
import os
import glob
from pathlib import Path

def buscar_en_datos_origen():
    """Busca el registro faltante en los datos de origen"""
    print("🔍 BÚSQUEDA EN DATOS DE ORIGEN (ARCHIVOS PARQUET)")
    print("=" * 60)
    
    # Datos del registro faltante
    documento_faltante = "71793435"
    celular_faltante = "51907368782"
    fecha_faltante = "2025-06-09 22:52:32"
    
    print(f"🎯 REGISTRO FALTANTE:")
    print(f"   • Operación: CPIN")
    print(f"   • Documento: DNI {documento_faltante}")
    print(f"   • Celular: {celular_faltante}")
    print(f"   • Fecha/Hora: {fecha_faltante}")
    print(f"   • TransactionID Original: 7536801689742")
    
    # Buscar en archivos parquet de origen
    directorios_parquet = [
        "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/output/20250609",
        "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER",
        "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/output"
    ]
    
    archivos_encontrados = []
    
    for directorio in directorios_parquet:
        if os.path.exists(directorio):
            print(f"\n📁 Buscando en: {directorio}")
            
            # Buscar archivos parquet
            archivos_parquet = glob.glob(f"{directorio}/**/*.parquet", recursive=True)
            print(f"   📄 Archivos parquet encontrados: {len(archivos_parquet)}")
            
            for archivo in archivos_parquet:
                print(f"   🔍 Analizando: {os.path.basename(archivo)}")
                try:
                    resultado = buscar_usuario_en_parquet(archivo, documento_faltante, celular_faltante)
                    if resultado:
                        archivos_encontrados.append((archivo, resultado))
                        print(f"      ✅ USUARIO ENCONTRADO!")
                        for reg in resultado:
                            print(f"         - Fecha: {reg.get('fecha', 'N/A')}")
                            print(f"         - Operación: {reg.get('operacion', 'N/A')}")
                            print(f"         - TransactionID: {reg.get('transactionid', 'N/A')}")
                except Exception as e:
                    print(f"      ❌ Error: {e}")
        else:
            print(f"❌ Directorio no existe: {directorio}")
    
    # Buscar también en archivos CSV intermedios
    print(f"\n📁 BÚSQUEDA EN ARCHIVOS CSV INTERMEDIOS:")
    archivos_csv = glob.glob("/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/**/*.csv", recursive=True)
    
    for archivo in archivos_csv:
        if 'final_procesado' not in archivo:  # Excluir el archivo final que ya sabemos que no lo tiene
            print(f"   🔍 Analizando: {os.path.basename(archivo)}")
            try:
                resultado = buscar_usuario_en_csv(archivo, documento_faltante, celular_faltante)
                if resultado:
                    archivos_encontrados.append((archivo, resultado))
                    print(f"      ✅ USUARIO ENCONTRADO!")
                    for reg in resultado:
                        print(f"         - Fecha: {reg.get('fecha', 'N/A')}")
                        print(f"         - Operación: {reg.get('operacion', 'N/A')}")
                        print(f"         - TransactionID: {reg.get('transactionid', 'N/A')}")
            except Exception as e:
                print(f"      ❌ Error: {e}")
    
    # Resumen
    print(f"\n📊 RESUMEN DE BÚSQUEDA:")
    if archivos_encontrados:
        print(f"   ✅ Usuario encontrado en {len(archivos_encontrados)} archivos:")
        for archivo, registros in archivos_encontrados:
            print(f"      📄 {archivo}")
            print(f"         Registros encontrados: {len(registros)}")
    else:
        print(f"   ❌ Usuario NO encontrado en ningún archivo de origen")
        print(f"   💡 Posibles causas:")
        print(f"      1. El usuario no realizó transacciones en el período del pipeline")
        print(f"      2. Los datos de origen no incluyen este registro")
        print(f"      3. Filtros en el pipeline excluyeron este registro")
    
    return archivos_encontrados

def buscar_usuario_en_parquet(archivo_path, documento, celular):
    """Busca un usuario específico en un archivo parquet"""
    try:
        df = pd.read_parquet(archivo_path)
        
        # Mostrar columnas disponibles para debug
        print(f"         Columnas: {list(df.columns)[:10]}...")
        
        registros_encontrados = []
        
        # Buscar por diferentes combinaciones de nombres de columnas
        posibles_doc = ['DOCUMENTO', 'documento', 'DOC', 'doc', 'USERID', 'userid']
        posibles_cel = ['CELULAR', 'celular', 'PHONE', 'phone', 'TELEFONO', 'telefono']
        posibles_fecha = ['FECHA_HORA', 'fecha_hora', 'TIMESTAMP', 'timestamp', 'FECHA', 'fecha']
        posibles_op = ['OPERACION', 'operacion', 'OPERATION', 'operation', 'TIPO', 'tipo']
        posibles_tid = ['TRANSACTIONID', 'transactionid', 'TID', 'tid', 'ID', 'id']
        
        col_doc = None
        col_cel = None
        col_fecha = None
        col_op = None
        col_tid = None
        
        for col in df.columns:
            if col.upper() in [x.upper() for x in posibles_doc]:
                col_doc = col
            elif col.upper() in [x.upper() for x in posibles_cel]:
                col_cel = col
            elif col.upper() in [x.upper() for x in posibles_fecha]:
                col_fecha = col
            elif col.upper() in [x.upper() for x in posibles_op]:
                col_op = col
            elif col.upper() in [x.upper() for x in posibles_tid]:
                col_tid = col
        
        if col_doc and col_cel:
            # Buscar registros que coincidan
            mask = (df[col_doc].astype(str).str.contains(documento, na=False)) & \
                   (df[col_cel].astype(str).str.contains(celular, na=False))
            
            registros = df[mask]
            
            for _, registro in registros.iterrows():
                reg_info = {
                    'documento': registro[col_doc] if col_doc else 'N/A',
                    'celular': registro[col_cel] if col_cel else 'N/A',
                    'fecha': registro[col_fecha] if col_fecha else 'N/A',
                    'operacion': registro[col_op] if col_op else 'N/A',
                    'transactionid': registro[col_tid] if col_tid else 'N/A'
                }
                registros_encontrados.append(reg_info)
        
        return registros_encontrados if registros_encontrados else None
        
    except Exception as e:
        raise e

def buscar_usuario_en_csv(archivo_path, documento, celular):
    """Busca un usuario específico en un archivo CSV"""
    try:
        df = pd.read_csv(archivo_path, header=None, low_memory=False)
        
        registros_encontrados = []
        
        # Buscar en las primeras columnas (asumiendo estructura estándar)
        if len(df.columns) >= 7:
            # Columnas típicas: OPERACION, TRANSACTIONID, FECHA_HORA, CANAL, TIPODOCUMENTO, DOCUMENTO, CELULAR
            mask = (df.iloc[:, 5].astype(str).str.contains(documento, na=False)) & \
                   (df.iloc[:, 6].astype(str).str.contains(celular, na=False))
            
            registros = df[mask]
            
            for _, registro in registros.iterrows():
                reg_info = {
                    'operacion': registro.iloc[0] if len(registro) > 0 else 'N/A',
                    'transactionid': registro.iloc[1] if len(registro) > 1 else 'N/A',
                    'fecha': registro.iloc[2] if len(registro) > 2 else 'N/A',
                    'documento': registro.iloc[5] if len(registro) > 5 else 'N/A',
                    'celular': registro.iloc[6] if len(registro) > 6 else 'N/A'
                }
                registros_encontrados.append(reg_info)
        
        return registros_encontrados if registros_encontrados else None
        
    except Exception as e:
        raise e

def verificar_filtros_pipeline():
    """Verifica si hay filtros en el pipeline que puedan estar excluyendo el registro"""
    print(f"\n🔍 VERIFICACIÓN DE FILTROS DEL PIPELINE:")
    
    # Leer el archivo principal del pipeline para buscar filtros
    pipeline_file = "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/pipeline_log_usuarios_duckdb.py"
    
    if os.path.exists(pipeline_file):
        print(f"   📄 Analizando: {pipeline_file}")
        
        with open(pipeline_file, 'r') as f:
            contenido = f.read()
        
        # Buscar filtros de fecha/hora
        if "WHERE" in contenido.upper():
            print(f"   🔍 Filtros WHERE encontrados en el pipeline")
            
            # Extraer líneas con WHERE
            lineas = contenido.split('\n')
            for i, linea in enumerate(lineas):
                if 'WHERE' in linea.upper() and ('FECHA' in linea.upper() or 'DATE' in linea.upper() or 'TIME' in linea.upper()):
                    print(f"      Línea {i+1}: {linea.strip()}")
        
        # Buscar filtros de hora específicos
        if "22:" in contenido or "23:" in contenido:
            print(f"   ⚠️  Posibles filtros de hora nocturna encontrados")
            lineas = contenido.split('\n')
            for i, linea in enumerate(lineas):
                if "22:" in linea or "23:" in linea:
                    print(f"      Línea {i+1}: {linea.strip()}")
    else:
        print(f"   ❌ Archivo del pipeline no encontrado: {pipeline_file}")

if __name__ == "__main__":
    print("🕵️ INVESTIGACIÓN EN DATOS DE ORIGEN")
    print("=" * 50)
    
    archivos_encontrados = buscar_en_datos_origen()
    verificar_filtros_pipeline()
    
    print(f"\n✅ Investigación completada")
    
    if not archivos_encontrados:
        print(f"\n💡 RECOMENDACIONES:")
        print(f"   1. Verificar si el usuario {documento_faltante} existe en las tablas de origen de Oracle")
        print(f"   2. Revisar filtros de fecha/hora en el pipeline")
        print(f"   3. Verificar si hay filtros que excluyan transacciones nocturnas (22:52:32)")
        print(f"   4. Comparar con los datos de S3 originales")
