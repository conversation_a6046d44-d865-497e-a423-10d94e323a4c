#!/usr/bin/env python3
"""
Script para investigar específicamente el JOIN con USER_IDENTIFIER
"""
import pandas as pd
import os

def investigar_join_user_identifier():
    """Investiga el JOIN específico que puede estar fallando"""
    print("🔍 INVESTIGACIÓN DEL JOIN USER_IDENTIFIER")
    print("=" * 50)
    
    documento = "71793435"
    celular = "51907368782"
    auth_id_esperado = "AU.8511734046727622"  # Del análisis anterior
    fecha_esperada = "2025-06-09 22:52:32"
    
    print(f"🎯 Investigando JOIN específico:")
    print(f"   • Documento: {documento}")
    print(f"   • Celular: {celular}")
    print(f"   • AUTHENTICATION_ID esperado: {auth_id_esperado}")
    print(f"   • Fecha esperada: {fecha_esperada}")
    
    # 1. Verificar USER_AUTH_CHANGE_HISTORY
    print(f"\n1️⃣ VERIFICACIÓN USER_AUTH_CHANGE_HISTORY:")
    auth_encontrado = verificar_auth_change_history(auth_id_esperado, fecha_esperada)
    
    # 2. Verificar USER_DATA_TRX
    print(f"\n2️⃣ VERIFICACIÓN USER_DATA_TRX:")
    user_data = verificar_user_data_trx(documento, celular)
    
    # 3. Simular consulta a USER_IDENTIFIER de S3
    print(f"\n3️⃣ SIMULACIÓN DE CONSULTA USER_IDENTIFIER:")
    simular_user_identifier_query(auth_id_esperado, user_data)
    
    # 4. Verificar LOG_USR generado
    print(f"\n4️⃣ VERIFICACIÓN LOG_USR GENERADO:")
    verificar_log_usr_generado(documento, celular)

def verificar_auth_change_history(auth_id, fecha):
    """Verifica si el registro específico existe en AUTH_CHANGE_HISTORY"""
    archivo = "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/TEMP_LOGS_USUARIOS/20250609/USER_AUTH_CHANGE_HISTORY.parquet"
    
    if os.path.exists(archivo):
        try:
            df = pd.read_parquet(archivo)
            
            # Buscar el registro específico
            registro_especifico = df[
                (df['AUTHENTICATION_ID'] == auth_id) &
                (df['MODIFIED_ON'].astype(str).str.contains('22:52:32', na=False))
            ]
            
            print(f"   📄 USER_AUTH_CHANGE_HISTORY.parquet")
            print(f"      Total registros: {len(df):,}")
            print(f"      🎯 Registro específico encontrado: {len(registro_especifico)}")
            
            if len(registro_especifico) > 0:
                reg = registro_especifico.iloc[0]
                print(f"         ✅ AUTHENTICATION_ID: {reg['AUTHENTICATION_ID']}")
                print(f"         ✅ MODIFIED_ON: {reg['MODIFIED_ON']}")
                print(f"         ✅ MODIFICATION_TYPE: {reg['MODIFICATION_TYPE']}")
                return True
            else:
                print(f"         ❌ Registro específico NO encontrado")
                
                # Buscar registros similares
                similar = df[df['AUTHENTICATION_ID'] == auth_id]
                print(f"         🔍 Registros con mismo AUTHENTICATION_ID: {len(similar)}")
                
                if len(similar) > 0:
                    for _, reg in similar.iterrows():
                        print(f"            {reg['MODIFIED_ON']} - {reg['MODIFICATION_TYPE']}")
                
                return False
                
        except Exception as e:
            print(f"      ❌ Error: {e}")
            return False
    else:
        print(f"   ❌ Archivo no encontrado")
        return False

def verificar_user_data_trx(documento, celular):
    """Verifica el usuario en USER_DATA_TRX"""
    archivo = "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/TEMP_LOGS_USUARIOS/20250609/USER_DATA_TRX.parquet"
    
    if os.path.exists(archivo):
        try:
            df = pd.read_parquet(archivo)
            
            mask = (df['ID_VALUE'].astype(str) == documento) & \
                   (df['MSISDN'].astype(str) == celular)
            usuario = df[mask]
            
            print(f"   📄 USER_DATA_TRX.parquet")
            print(f"      Total registros: {len(df):,}")
            print(f"      🎯 Usuario encontrado: {len(usuario)}")
            
            if len(usuario) > 0:
                reg = usuario.iloc[0]
                user_data = {
                    'USER_ID': reg['USER_ID'],
                    'O_USER_ID': reg['O_USER_ID'],
                    'CREATED_ON': reg['CREATED_ON']
                }
                
                print(f"         ✅ USER_ID: {user_data['USER_ID']}")
                print(f"         ✅ O_USER_ID: {user_data['O_USER_ID']}")
                print(f"         ✅ CREATED_ON: {user_data['CREATED_ON']}")
                
                return user_data
            else:
                print(f"         ❌ Usuario NO encontrado")
                return None
                
        except Exception as e:
            print(f"      ❌ Error: {e}")
            return None
    else:
        print(f"   ❌ Archivo no encontrado")
        return None

def simular_user_identifier_query(auth_id, user_data):
    """Simula la consulta a USER_IDENTIFIER para verificar el JOIN"""
    print(f"   🔍 Simulando consulta USER_IDENTIFIER...")
    
    if user_data is None:
        print(f"      ❌ No se puede simular sin datos de usuario")
        return
    
    print(f"   📋 JOIN requerido:")
    print(f"      • AUTHENTICATION_ID: {auth_id}")
    print(f"      • USER_ID debe coincidir con: {user_data['O_USER_ID']}")
    
    print(f"   💡 Para que el JOIN funcione, USER_IDENTIFIER debe tener:")
    print(f"      • Un registro con AUTHENTICATION_ID = '{auth_id}'")
    print(f"      • Ese registro debe tener USER_ID = '{user_data['O_USER_ID']}'")
    
    print(f"   🔍 Si este JOIN falla, el registro CHANGE_AUTH_FACTOR no se convierte a CPIN")

def verificar_log_usr_generado(documento, celular):
    """Verifica qué se generó en LOG_USR.parquet"""
    archivo = "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/output/20250609/LOG_USR.parquet"
    
    if os.path.exists(archivo):
        try:
            df = pd.read_parquet(archivo)
            
            print(f"   📄 LOG_USR.parquet")
            print(f"      Total registros: {len(df):,}")
            
            # Buscar registros del usuario
            mask_usuario = (df['DOCUMENTO'].astype(str) == documento) & \
                          (df['MSISDN'].astype(str) == celular)
            
            registros_usuario = df[mask_usuario]
            print(f"      🎯 Registros del usuario: {len(registros_usuario)}")
            
            if len(registros_usuario) > 0:
                print(f"         📋 REQUESTTYPE encontrados:")
                tipos = registros_usuario['REQUESTTYPE'].value_counts()
                for tipo, count in tipos.items():
                    print(f"            {tipo}: {count}")
                
                # Buscar específicamente CHANGE_AUTH_FACTOR
                change_auth = registros_usuario[registros_usuario['REQUESTTYPE'] == 'CHANGE_AUTH_FACTOR']
                print(f"         🎯 CHANGE_AUTH_FACTOR: {len(change_auth)}")
                
                if len(change_auth) > 0:
                    print(f"            ✅ Usuario SÍ tiene CHANGE_AUTH_FACTOR en LOG_USR")
                    for _, reg in change_auth.iterrows():
                        print(f"               {reg['CREATEDON']} - UserHistId: {reg['USERHISTID']}")
                else:
                    print(f"            ❌ Usuario NO tiene CHANGE_AUTH_FACTOR en LOG_USR")
                    print(f"            💡 Esto confirma que el JOIN está fallando")
            else:
                print(f"         ❌ Usuario NO encontrado en LOG_USR")
                
        except Exception as e:
            print(f"      ❌ Error: {e}")
    else:
        print(f"   ❌ Archivo LOG_USR.parquet no encontrado")

def proponer_solucion():
    """Propone soluciones basadas en el análisis"""
    print(f"\n💡 PROPUESTAS DE SOLUCIÓN:")
    
    print(f"   🔧 OPCIÓN A: Verificar USER_IDENTIFIER en S3")
    print(f"      • Consultar directamente la tabla USER_IDENTIFIER")
    print(f"      • Verificar si existe el registro con AUTHENTICATION_ID = AU.8511734046727622")
    print(f"      • Verificar si el USER_ID coincide con US.8368734046721007")
    
    print(f"   🔧 OPCIÓN B: Modificar lógica de JOIN")
    print(f"      • Usar un JOIN alternativo que no dependa de USER_IDENTIFIER")
    print(f"      • Buscar otra forma de conectar AUTHENTICATION_ID con USER_ID")
    
    print(f"   🔧 OPCIÓN C: Agregar logging detallado")
    print(f"      • Agregar logs en el pipeline para ver exactamente dónde falla el JOIN")
    print(f"      • Contar registros en cada paso del JOIN")
    
    print(f"   🔧 OPCIÓN D: Investigar datos de origen")
    print(f"      • Verificar si el problema está en los datos de S3")
    print(f"      • Comparar con Oracle para ver diferencias en USER_IDENTIFIER")

if __name__ == "__main__":
    print("🕵️ INVESTIGACIÓN PROFUNDA DEL JOIN USER_IDENTIFIER")
    print("=" * 70)
    
    investigar_join_user_identifier()
    proponer_solucion()
    
    print(f"\n📋 CONCLUSIÓN:")
    print(f"   El registro CHANGE_AUTH_FACTOR existe en USER_AUTH_CHANGE_HISTORY")
    print(f"   El usuario existe en USER_DATA_TRX")
    print(f"   El problema está en el JOIN con USER_IDENTIFIER")
    print(f"   Necesitamos verificar/corregir la tabla USER_IDENTIFIER")
    
    print(f"\n✅ Investigación completada")
