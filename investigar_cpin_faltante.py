#!/usr/bin/env python3
"""
Script para investigar específicamente por qué falta el registro CPIN
"""
import pandas as pd
import os

def investigar_operacion_cpin():
    """Investiga específicamente la operación CPIN"""
    print("🔍 INVESTIGACIÓN ESPECÍFICA DE OPERACIÓN CPIN")
    print("=" * 55)
    
    # Datos del registro faltante
    documento = "71793435"
    celular = "51907368782"
    transactionid_faltante = "7536801689742"
    fecha_faltante = "2025-06-09 22:52:32"
    
    print(f"🎯 REGISTRO CPIN FALTANTE:")
    print(f"   • Documento: {documento}")
    print(f"   • Celular: {celular}")
    print(f"   • TransactionID: {transactionid_faltante}")
    print(f"   • Fecha/Hora: {fecha_faltante}")
    
    # 1. Verificar en archivos temporales
    print(f"\n1️⃣ VERIFICACIÓN EN ARCHIVOS TEMPORALES:")
    verificar_en_temporales(documento, celular, transactionid_faltante)
    
    # 2. Analizar operaciones CPIN específicamente
    print(f"\n2️⃣ ANÁLISIS DE OPERACIONES CPIN:")
    analizar_cpin_en_archivos(documento, celular)
    
    # 3. Verificar lógica de deduplicación
    print(f"\n3️⃣ VERIFICACIÓN DE LÓGICA DE DEDUPLICACIÓN:")
    verificar_deduplicacion(documento, celular)
    
    # 4. Revisar filtros específicos de CPIN
    print(f"\n4️⃣ REVISIÓN DE FILTROS ESPECÍFICOS:")
    revisar_filtros_cpin()

def verificar_en_temporales(documento, celular, transactionid):
    """Verifica si el registro existe en archivos temporales"""
    archivo_temporal = "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/TEMP_LOGS_USUARIOS/20250609/USER_DATA_TRX.parquet"
    
    if os.path.exists(archivo_temporal):
        try:
            df = pd.read_parquet(archivo_temporal)
            print(f"   📄 Analizando: USER_DATA_TRX.parquet")
            print(f"      Total registros: {len(df):,}")
            
            # Buscar el usuario específico
            mask_usuario = (df['ID_VALUE'].astype(str) == documento) & \
                          (df['MSISDN'].astype(str) == celular)
            
            registros_usuario = df[mask_usuario]
            print(f"      Registros del usuario: {len(registros_usuario)}")
            
            if len(registros_usuario) > 0:
                print(f"      ✅ Usuario encontrado en datos temporales")
                
                # Mostrar todos los registros del usuario
                for i, (_, registro) in enumerate(registros_usuario.iterrows(), 1):
                    print(f"         Registro {i}: {registro['CREATED_ON']} - Status: {registro.get('STATUS', 'N/A')}")
                
                # Buscar específicamente el registro de las 22:52
                mask_hora = registros_usuario['CREATED_ON'].astype(str).str.contains('22:52', na=False)
                registro_22_52 = registros_usuario[mask_hora]
                
                if len(registro_22_52) > 0:
                    print(f"      🎯 REGISTRO 22:52 ENCONTRADO en temporales!")
                    for _, reg in registro_22_52.iterrows():
                        print(f"         Fecha: {reg['CREATED_ON']}")
                        print(f"         Status: {reg.get('STATUS', 'N/A')}")
                        print(f"         User_ID: {reg.get('USER_ID', 'N/A')}")
                else:
                    print(f"      ❌ Registro 22:52 NO encontrado en temporales")
            else:
                print(f"      ❌ Usuario NO encontrado en datos temporales")
                
        except Exception as e:
            print(f"      ❌ Error: {e}")
    else:
        print(f"   ❌ Archivo temporal no encontrado")

def analizar_cpin_en_archivos(documento, celular):
    """Analiza específicamente las operaciones CPIN"""
    
    # Analizar archivo final
    archivo_final = "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/output/20250609/csv_exports/csv_final_procesado/LOGUSR-FCOMPARTAMOS-20250609074148.csv"
    
    if os.path.exists(archivo_final):
        try:
            df = pd.read_csv(archivo_final, header=None)
            
            # Asignar nombres de columnas
            columnas = ['OPERACION', 'TRANSACTIONID', 'FECHA_HORA', 'CANAL', 'TIPODOCUMENTO', 
                       'DOCUMENTO', 'CELULAR', 'EMPRESA'] + [f'COL{i}' for i in range(8, 30)]
            df.columns = columnas[:len(df.columns)]
            
            print(f"   📄 Analizando archivo final:")
            
            # Contar operaciones CPIN
            cpin_total = len(df[df['OPERACION'] == 'CPIN'])
            print(f"      Total operaciones CPIN: {cpin_total:,}")
            
            # Buscar el usuario específico
            mask_usuario = (df['DOCUMENTO'].astype(str) == documento) & \
                          (df['CELULAR'].astype(str) == celular)
            
            registros_usuario = df[mask_usuario]
            print(f"      Registros del usuario: {len(registros_usuario)}")
            
            if len(registros_usuario) > 0:
                print(f"      ✅ Usuario encontrado en archivo final")
                
                # Mostrar operaciones del usuario
                operaciones_usuario = registros_usuario['OPERACION'].value_counts()
                print(f"      📊 Operaciones del usuario:")
                for op, count in operaciones_usuario.items():
                    print(f"         {op}: {count}")
                
                # Verificar si tiene CPIN
                cpin_usuario = registros_usuario[registros_usuario['OPERACION'] == 'CPIN']
                print(f"      CPIN del usuario: {len(cpin_usuario)}")
                
                if len(cpin_usuario) > 0:
                    print(f"      ✅ Usuario SÍ tiene operaciones CPIN")
                    for _, reg in cpin_usuario.iterrows():
                        print(f"         CPIN: {reg['FECHA_HORA']} - TID: {reg['TRANSACTIONID']}")
                else:
                    print(f"      ❌ Usuario NO tiene operaciones CPIN en archivo final")
                
                # Mostrar todas las operaciones del usuario
                print(f"      📋 Todas las operaciones del usuario:")
                for _, reg in registros_usuario.iterrows():
                    print(f"         {reg['OPERACION']}: {reg['FECHA_HORA']} - TID: {reg['TRANSACTIONID']}")
            else:
                print(f"      ❌ Usuario NO encontrado en archivo final")
                
        except Exception as e:
            print(f"      ❌ Error: {e}")

def verificar_deduplicacion(documento, celular):
    """Verifica si hay problemas de deduplicación"""
    print(f"   🔍 Verificando lógica de deduplicación...")
    
    # Buscar archivos con deduplicación
    archivos_buscar = [
        "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/output/20250609/parquet_exports/LOG-USUARIOS-FCOMPARTAMOS-20250609.parquet",
        "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/LOG-USUARIOS-FCOMPARTAMOS-20250609.csv"
    ]
    
    for archivo in archivos_buscar:
        if os.path.exists(archivo):
            print(f"   📄 Verificando: {os.path.basename(archivo)}")
            try:
                if archivo.endswith('.parquet'):
                    df = pd.read_parquet(archivo)
                else:
                    df = pd.read_csv(archivo, header=None)
                    columnas = ['OPERACION', 'TRANSACTIONID', 'FECHA_HORA', 'CANAL', 'TIPODOCUMENTO', 
                               'DOCUMENTO', 'CELULAR', 'EMPRESA'] + [f'COL{i}' for i in range(8, 30)]
                    df.columns = columnas[:len(df.columns)]
                
                # Buscar el usuario
                if 'DOCUMENTO' in df.columns and 'CELULAR' in df.columns:
                    mask = (df['DOCUMENTO'].astype(str) == documento) & \
                           (df['CELULAR'].astype(str) == celular)
                    registros = df[mask]
                    
                    print(f"      Registros del usuario: {len(registros)}")
                    
                    if len(registros) > 0:
                        # Contar operaciones
                        ops = registros['OPERACION'].value_counts()
                        print(f"      Operaciones: {dict(ops)}")
                        
                        # Verificar CPIN específicamente
                        cpin_regs = registros[registros['OPERACION'] == 'CPIN']
                        print(f"      CPIN: {len(cpin_regs)}")
                        
                        if len(cpin_regs) > 0:
                            for _, reg in cpin_regs.iterrows():
                                print(f"         CPIN: {reg['FECHA_HORA']} - TID: {reg['TRANSACTIONID']}")
                
            except Exception as e:
                print(f"      ❌ Error: {e}")

def revisar_filtros_cpin():
    """Revisa filtros específicos que puedan afectar CPIN"""
    print(f"   🔍 Revisando código del pipeline...")
    
    archivo_pipeline = "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/pipeline_log_usuarios_duckdb.py"
    
    if os.path.exists(archivo_pipeline):
        with open(archivo_pipeline, 'r') as f:
            contenido = f.read()
        
        lineas = contenido.split('\n')
        
        # Buscar filtros específicos de CPIN
        filtros_cpin = []
        filtros_transactionid = []
        filtros_dedup = []
        
        for i, linea in enumerate(lineas):
            linea_upper = linea.upper()
            
            # Filtros de CPIN
            if 'CPIN' in linea and any(palabra in linea_upper for palabra in ['WHERE', 'AND', 'OR', 'NOT']):
                filtros_cpin.append((i+1, linea.strip()))
            
            # Filtros de TransactionID
            if 'TRANSACTIONID' in linea_upper and any(palabra in linea_upper for palabra in ['WHERE', 'AND', 'OR', 'DISTINCT']):
                filtros_transactionid.append((i+1, linea.strip()))
            
            # Lógica de deduplicación
            if any(palabra in linea_upper for palabra in ['DISTINCT', 'GROUP BY', 'ROW_NUMBER', 'DEDUPLICATE']):
                filtros_dedup.append((i+1, linea.strip()))
        
        if filtros_cpin:
            print(f"   ⚠️  FILTROS ESPECÍFICOS DE CPIN:")
            for linea_num, linea in filtros_cpin:
                print(f"      Línea {linea_num}: {linea}")
        
        if filtros_transactionid:
            print(f"   🔍 FILTROS DE TRANSACTIONID:")
            for linea_num, linea in filtros_transactionid[:5]:  # Solo primeros 5
                print(f"      Línea {linea_num}: {linea}")
        
        if filtros_dedup:
            print(f"   🔄 LÓGICA DE DEDUPLICACIÓN:")
            for linea_num, linea in filtros_dedup[:5]:  # Solo primeros 5
                print(f"      Línea {linea_num}: {linea}")
        
        # Buscar la lógica específica de generación de CPIN
        if 'CPIN' in contenido:
            print(f"   📋 LÓGICA DE CPIN ENCONTRADA en el pipeline")
            
            # Extraer sección de CPIN
            lineas_cpin = []
            en_seccion_cpin = False
            
            for i, linea in enumerate(lineas):
                if 'CPIN' in linea and ('SELECT' in linea.upper() or 'INSERT' in linea.upper()):
                    en_seccion_cpin = True
                    lineas_cpin.append((i+1, linea.strip()))
                elif en_seccion_cpin:
                    if linea.strip() and not linea.startswith(' ') and not linea.startswith('\t'):
                        en_seccion_cpin = False
                    else:
                        lineas_cpin.append((i+1, linea.strip()))
                        if len(lineas_cpin) > 20:  # Limitar salida
                            break
            
            if lineas_cpin:
                print(f"   📄 SECCIÓN DE LÓGICA CPIN:")
                for linea_num, linea in lineas_cpin[:15]:
                    if linea:
                        print(f"      {linea_num}: {linea}")

if __name__ == "__main__":
    print("🕵️ INVESTIGACIÓN ESPECÍFICA DEL REGISTRO CPIN FALTANTE")
    print("=" * 70)
    
    investigar_operacion_cpin()
    
    print(f"\n✅ Investigación completada")
    print(f"\n📋 PRÓXIMOS PASOS:")
    print(f"   1. Si el registro existe en temporales pero no en final: Problema en procesamiento")
    print(f"   2. Si no existe en temporales: Problema en extracción de datos")
    print(f"   3. Si hay filtros específicos de CPIN: Revisar lógica")
    print(f"   4. Si hay deduplicación: Verificar criterios")
