#!/usr/bin/env python3
"""
Investigar qué diferencia hay entre documentos que SÍ cuadran vs los que NO cuadran
para encontrar qué estamos aplicando mal
"""
import pandas as pd
import os

def investigar_diferencia_cuadran_vs_no_cuadran():
    """Investiga la diferencia entre documentos que cuadran vs los que no"""
    print("🔍 INVESTIGACIÓN: ¿QUÉ APLICAMOS MAL EN EL 3% QUE NO CUADRA?")
    print("=" * 70)
    
    # Archivos a comparar
    archivo_modernizado = "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/output/20250609/csv_exports/csv_final_procesado/LOGUSR-FCOMPARTAMOS-20250609184618.csv"
    archivo_oracle = "/home/<USER>/generate/log_usuarios/output/LOGUSR-FCOMPARTAMOS-20250610052953.csv"
    
    print(f"📄 Archivo modernizado: {os.path.basename(archivo_modernizado)}")
    print(f"📄 Archivo Oracle: {os.path.basename(archivo_oracle)}")
    
    # 1. Identificar documentos que SÍ cuadran vs los que NO cuadran
    print(f"\n1️⃣ IDENTIFICACIÓN DE DOCUMENTOS QUE CUADRAN VS NO CUADRAN:")
    docs_cuadran, docs_no_cuadran = identificar_documentos_cuadran_vs_no_cuadran(archivo_modernizado, archivo_oracle)
    
    # 2. Analizar patrones en documentos que NO cuadran
    print(f"\n2️⃣ ANÁLISIS DE PATRONES EN DOCUMENTOS QUE NO CUADRAN:")
    analizar_patrones_no_cuadran(docs_no_cuadran, archivo_modernizado, archivo_oracle)
    
    # 3. Comparar con documentos que SÍ cuadran
    print(f"\n3️⃣ COMPARACIÓN CON DOCUMENTOS QUE SÍ CUADRAN:")
    comparar_con_documentos_que_cuadran(docs_cuadran[:5], docs_no_cuadran[:5], archivo_modernizado, archivo_oracle)
    
    # 4. Investigar en archivos fuente
    print(f"\n4️⃣ INVESTIGACIÓN EN ARCHIVOS FUENTE:")
    investigar_en_archivos_fuente(docs_no_cuadran[:3])
    
    # 5. Proponer solución específica
    print(f"\n5️⃣ PROPUESTA DE SOLUCIÓN:")
    proponer_solucion_especifica()

def identificar_documentos_cuadran_vs_no_cuadran(archivo_modernizado, archivo_oracle):
    """Identifica qué documentos cuadran y cuáles no"""
    try:
        # Leer archivos
        df_mod = pd.read_csv(archivo_modernizado, header=None)
        df_ora = pd.read_csv(archivo_oracle, header=None)
        
        # Asignar columnas
        columnas = ['OPERACION', 'TRANSACTIONID', 'FECHA_HORA', 'CANAL', 'TIPODOCUMENTO', 
                   'DOCUMENTO', 'CELULAR', 'EMPRESA'] + [f'COL{i}' for i in range(8, 30)]
        
        df_mod.columns = columnas[:len(df_mod.columns)]
        df_ora.columns = columnas[:len(df_ora.columns)]
        
        # Documentos en ambos archivos
        docs_mod = set(df_mod['DOCUMENTO'].astype(str))
        docs_ora = set(df_ora['DOCUMENTO'].astype(str))
        docs_comunes = docs_mod & docs_ora
        
        print(f"   📊 Documentos comunes: {len(docs_comunes):,}")
        
        # Analizar cada documento común
        docs_cuadran = []
        docs_no_cuadran = []
        
        for doc in docs_comunes:
            # Registros del documento en ambos archivos
            regs_mod = df_mod[df_mod['DOCUMENTO'].astype(str) == doc]
            regs_ora = df_ora[df_ora['DOCUMENTO'].astype(str) == doc]
            
            # Crear claves de negocio (sin TransactionID)
            claves_mod = set()
            claves_ora = set()
            
            for _, reg in regs_mod.iterrows():
                clave = f"{reg['OPERACION']}|{reg['FECHA_HORA']}|{reg['DOCUMENTO']}|{reg['CELULAR']}"
                claves_mod.add(clave)
            
            for _, reg in regs_ora.iterrows():
                clave = f"{reg['OPERACION']}|{reg['FECHA_HORA']}|{reg['DOCUMENTO']}|{reg['CELULAR']}"
                claves_ora.add(clave)
            
            # Verificar si cuadra perfectamente
            if claves_mod == claves_ora and len(claves_mod) > 0:
                docs_cuadran.append({
                    'documento': doc,
                    'registros_mod': len(regs_mod),
                    'registros_ora': len(regs_ora),
                    'operaciones_mod': list(regs_mod['OPERACION'].unique()),
                    'operaciones_ora': list(regs_ora['OPERACION'].unique())
                })
            else:
                docs_no_cuadran.append({
                    'documento': doc,
                    'registros_mod': len(regs_mod),
                    'registros_ora': len(regs_ora),
                    'operaciones_mod': list(regs_mod['OPERACION'].unique()),
                    'operaciones_ora': list(regs_ora['OPERACION'].unique()),
                    'claves_mod': claves_mod,
                    'claves_ora': claves_ora
                })
        
        print(f"   ✅ Documentos que CUADRAN: {len(docs_cuadran):,} ({len(docs_cuadran)/len(docs_comunes)*100:.1f}%)")
        print(f"   ❌ Documentos que NO CUADRAN: {len(docs_no_cuadran):,} ({len(docs_no_cuadran)/len(docs_comunes)*100:.1f}%)")
        
        return docs_cuadran, docs_no_cuadran
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return [], []

def analizar_patrones_no_cuadran(docs_no_cuadran, archivo_modernizado, archivo_oracle):
    """Analiza patrones en documentos que NO cuadran"""
    
    if not docs_no_cuadran:
        print(f"   ✅ No hay documentos que no cuadren")
        return
    
    print(f"   📊 Analizando {len(docs_no_cuadran)} documentos que NO cuadran...")
    
    # Patrones de operaciones
    operaciones_mod_no_cuadran = {}
    operaciones_ora_no_cuadran = {}
    
    # Patrones de diferencias temporales
    diferencias_temporales = []
    
    for doc_info in docs_no_cuadran[:10]:  # Analizar primeros 10
        doc = doc_info['documento']
        
        print(f"\n   📄 Documento {doc}:")
        print(f"      Modernizado: {doc_info['registros_mod']} registros - {doc_info['operaciones_mod']}")
        print(f"      Oracle: {doc_info['registros_ora']} registros - {doc_info['operaciones_ora']}")
        
        # Analizar diferencias específicas
        claves_mod = doc_info['claves_mod']
        claves_ora = doc_info['claves_ora']
        
        solo_mod = claves_mod - claves_ora
        solo_ora = claves_ora - claves_mod
        
        if solo_mod:
            print(f"      🔵 Solo en modernizado:")
            for clave in solo_mod:
                partes = clave.split('|')
                if len(partes) >= 2:
                    operacion, fecha_hora = partes[0], partes[1]
                    print(f"         {operacion} - {fecha_hora}")
                    
                    # Contar operaciones
                    operaciones_mod_no_cuadran[operacion] = operaciones_mod_no_cuadran.get(operacion, 0) + 1
        
        if solo_ora:
            print(f"      🔴 Solo en Oracle:")
            for clave in solo_ora:
                partes = clave.split('|')
                if len(partes) >= 2:
                    operacion, fecha_hora = partes[0], partes[1]
                    print(f"         {operacion} - {fecha_hora}")
                    
                    # Contar operaciones
                    operaciones_ora_no_cuadran[operacion] = operaciones_ora_no_cuadran.get(operacion, 0) + 1
        
        # Analizar diferencias temporales si es la misma operación
        if len(claves_mod) == 1 and len(claves_ora) == 1:
            clave_mod = list(claves_mod)[0]
            clave_ora = list(claves_ora)[0]
            
            partes_mod = clave_mod.split('|')
            partes_ora = clave_ora.split('|')
            
            if len(partes_mod) >= 2 and len(partes_ora) >= 2:
                if partes_mod[0] == partes_ora[0]:  # Misma operación
                    fecha_mod = partes_mod[1]
                    fecha_ora = partes_ora[1]
                    
                    try:
                        from datetime import datetime
                        dt_mod = datetime.strptime(fecha_mod, '%Y-%m-%d %H:%M:%S')
                        dt_ora = datetime.strptime(fecha_ora, '%Y-%m-%d %H:%M:%S')
                        
                        diferencia = abs((dt_mod - dt_ora).total_seconds() / 3600)  # Horas
                        diferencias_temporales.append({
                            'documento': doc,
                            'operacion': partes_mod[0],
                            'diferencia_horas': diferencia,
                            'fecha_mod': fecha_mod,
                            'fecha_ora': fecha_ora
                        })
                    except:
                        pass
    
    # Resumen de patrones
    print(f"\n   📊 PATRONES IDENTIFICADOS:")
    
    if operaciones_mod_no_cuadran:
        print(f"      🔵 Operaciones más frecuentes solo en modernizado:")
        for op, count in sorted(operaciones_mod_no_cuadran.items(), key=lambda x: x[1], reverse=True)[:5]:
            print(f"         {op}: {count} casos")
    
    if operaciones_ora_no_cuadran:
        print(f"      🔴 Operaciones más frecuentes solo en Oracle:")
        for op, count in sorted(operaciones_ora_no_cuadran.items(), key=lambda x: x[1], reverse=True)[:5]:
            print(f"         {op}: {count} casos")
    
    if diferencias_temporales:
        print(f"      🕐 Diferencias temporales (misma operación):")
        diferencias_temporales.sort(key=lambda x: x['diferencia_horas'])
        
        for diff in diferencias_temporales[:5]:
            print(f"         Doc {diff['documento']}: {diff['operacion']} - {diff['diferencia_horas']:.1f}h diferencia")
            print(f"            MOD: {diff['fecha_mod']}")
            print(f"            ORA: {diff['fecha_ora']}")

def comparar_con_documentos_que_cuadran(docs_cuadran, docs_no_cuadran, archivo_modernizado, archivo_oracle):
    """Compara documentos que cuadran vs los que no cuadran"""
    
    if not docs_cuadran or not docs_no_cuadran:
        print(f"   ⚠️  No hay suficientes documentos para comparar")
        return
    
    print(f"   🔍 Comparando patrones...")
    
    # Analizar operaciones en documentos que SÍ cuadran
    ops_cuadran = {}
    for doc_info in docs_cuadran:
        for op in doc_info['operaciones_mod']:
            ops_cuadran[op] = ops_cuadran.get(op, 0) + 1
    
    # Analizar operaciones en documentos que NO cuadran
    ops_no_cuadran = {}
    for doc_info in docs_no_cuadran:
        for op in doc_info['operaciones_mod']:
            ops_no_cuadran[op] = ops_no_cuadran.get(op, 0) + 1
    
    print(f"      📊 Operaciones en documentos que CUADRAN:")
    for op, count in sorted(ops_cuadran.items(), key=lambda x: x[1], reverse=True)[:5]:
        print(f"         {op}: {count} casos")
    
    print(f"      📊 Operaciones en documentos que NO CUADRAN:")
    for op, count in sorted(ops_no_cuadran.items(), key=lambda x: x[1], reverse=True)[:5]:
        print(f"         {op}: {count} casos")
    
    # Identificar si hay operaciones específicas que no cuadran
    ops_problematicas = set(ops_no_cuadran.keys()) - set(ops_cuadran.keys())
    if ops_problematicas:
        print(f"      ⚠️  Operaciones SOLO en documentos que NO cuadran: {ops_problematicas}")
    
    # Verificar si CPIN es problemático
    if 'CPIN' in ops_no_cuadran and 'CPIN' in ops_cuadran:
        porcentaje_cpin_problema = ops_no_cuadran['CPIN'] / (ops_no_cuadran['CPIN'] + ops_cuadran['CPIN']) * 100
        print(f"      🎯 CPIN problemático: {porcentaje_cpin_problema:.1f}% de casos CPIN no cuadran")

def investigar_en_archivos_fuente(docs_no_cuadran):
    """Investiga documentos que no cuadran en archivos fuente"""
    
    if not docs_no_cuadran:
        return
    
    print(f"   🔍 Investigando en archivos fuente...")
    
    # Verificar LOG_USR.parquet y LOG_USR_DEDUPLICATED.parquet
    archivo_original = "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/output/20250609/LOG_USR.parquet"
    archivo_dedup = "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/output/20250609/LOG_USR_DEDUPLICATED.parquet"
    
    for doc_info in docs_no_cuadran[:3]:  # Analizar primeros 3
        doc = doc_info['documento']
        
        print(f"\n      📄 Documento {doc}:")
        
        # Verificar en LOG_USR original
        if os.path.exists(archivo_original):
            try:
                df_orig = pd.read_parquet(archivo_original)
                mask = df_orig['DOCUMENTO'].astype(str) == doc
                registros_orig = df_orig[mask]
                
                print(f"         LOG_USR original: {len(registros_orig)} registros")
                
                if len(registros_orig) > 0:
                    # Agrupar por REQUESTTYPE
                    tipos = registros_orig['REQUESTTYPE'].value_counts()
                    for tipo, count in tipos.items():
                        print(f"            {tipo}: {count} registros")
                        
                        # Mostrar fechas para CHANGE_AUTH_FACTOR
                        if tipo == 'CHANGE_AUTH_FACTOR':
                            fechas = registros_orig[registros_orig['REQUESTTYPE'] == tipo]['CREATEDON'].unique()
                            print(f"               Fechas: {list(fechas)}")
                
            except Exception as e:
                print(f"         ❌ Error en LOG_USR: {e}")
        
        # Verificar en LOG_USR_DEDUPLICATED
        if os.path.exists(archivo_dedup):
            try:
                df_dedup = pd.read_parquet(archivo_dedup)
                mask = df_dedup['DOCUMENTO'].astype(str) == doc
                registros_dedup = df_dedup[mask]
                
                print(f"         LOG_USR_DEDUPLICATED: {len(registros_dedup)} registros")
                
                if len(registros_dedup) > 0:
                    for _, reg in registros_dedup.iterrows():
                        print(f"            {reg['REQUESTTYPE']} - {reg['CREATEDON']}")
                
            except Exception as e:
                print(f"         ❌ Error en LOG_USR_DEDUPLICATED: {e}")

def proponer_solucion_especifica():
    """Propone solución específica basada en el análisis"""
    
    print(f"   💡 HIPÓTESIS PRINCIPALES:")
    print(f"      1. MÚLTIPLES CHANGE_AUTH_FACTOR:")
    print(f"         - Usuarios con múltiples eventos en el día")
    print(f"         - Oracle y pipeline usan criterios diferentes de selección")
    print(f"         - Posible diferencia en ventanas de procesamiento")
    
    print(f"      2. LÓGICA DE DEDUPLICACIÓN:")
    print(f"         - Oracle puede usar criterio diferente al nuestro")
    print(f"         - Puede considerar otros campos además de USERHISTID+REQUESTTYPE")
    print(f"         - Puede tener filtros temporales específicos")
    
    print(f"      3. PROCESAMIENTO TEMPORAL:")
    print(f"         - Oracle procesa en ventana específica del día")
    print(f"         - Pipeline modernizado procesa todo el día")
    print(f"         - Diferencias en zona horaria o cortes temporales")
    
    print(f"\n   🔧 SOLUCIONES A PROBAR:")
    print(f"      1. INVESTIGAR LÓGICA ORACLE ORIGINAL:")
    print(f"         - Revisar código Oracle para ver criterio exacto")
    print(f"         - Verificar si usa filtros temporales específicos")
    
    print(f"      2. AJUSTAR CRITERIO DE DEDUPLICACIÓN:")
    print(f"         - Probar diferentes criterios de ordenamiento")
    print(f"         - Considerar campos adicionales en la deduplicación")
    
    print(f"      3. IMPLEMENTAR VENTANA TEMPORAL:")
    print(f"         - Aplicar mismo corte temporal que Oracle")
    print(f"         - Filtrar por horarios específicos")
    
    print(f"      4. ANÁLISIS ESPECÍFICO DE CPIN:")
    print(f"         - Verificar si CPIN tiene lógica especial")
    print(f"         - Comparar con otros tipos de operación")

if __name__ == "__main__":
    print("🕵️ INVESTIGACIÓN PROFUNDA - ¿QUÉ APLICAMOS MAL?")
    print("=" * 80)
    
    investigar_diferencia_cuadran_vs_no_cuadran()
    
    print(f"\n📋 CONCLUSIÓN:")
    print(f"   Si el 97% cuadra perfectamente, el problema está en algo específico")
    print(f"   que aplicamos diferente para el 3% restante. Probablemente:")
    print(f"   - Criterio de selección cuando hay múltiples eventos")
    print(f"   - Ventana temporal de procesamiento")
    print(f"   - Lógica específica para ciertos tipos de operación")
    
    print(f"\n✅ Investigación completada")
