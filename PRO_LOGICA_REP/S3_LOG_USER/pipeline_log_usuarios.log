2025-06-06 20:46:21,054 - LogUsuariosPipeline - INFO - Configurando credenciales S3...
2025-06-06 20:46:21,067 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-06 20:46:21,116 - LogUsuariosPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-06 20:46:21,117 - Log<PERSON>su<PERSON>sPipeline - INFO - === INICIANDO PIPELINE LOG_USUARIOS MODERNIZADO ===
2025-06-06 20:46:21,117 - LogUsuariosPipeline - INFO - Fecha de procesamiento: 2025-06-05 (carpeta: ********)
2025-06-06 20:46:21,117 - LogUsuariosPipeline - INFO - USER-MODIFY: INICIANDO
2025-06-06 20:46:21,117 - Log<PERSON>su<PERSON>sPipeline - INFO - Extrayendo USER_ACCOUNT_HISTORY para fecha: 2025-06-05
2025-06-06 20:46:21,249 - LogUsuariosPipeline - INFO - USER_ACCOUNT_HISTORY extraído: 4 registros -> TEMP_LOGS_USUARIOS/********/USER_ACCOUNT_HISTORY.parquet
2025-06-06 20:46:21,249 - LogUsuariosPipeline - INFO - USER-MODIFY: OK
2025-06-06 20:46:21,249 - LogUsuariosPipeline - INFO - PRE-LOG-USR: INICIANDO
2025-06-06 20:46:21,249 - LogUsuariosPipeline - INFO - Ejecutando SP_PRE_LOG_USR CORREGIDO (incluye USER_IDs múltiples) para fecha: 2025-06-05
2025-06-06 20:46:29,207 - LogUsuariosPipeline - INFO - SP_PRE_LOG_USR completado: 1581374 registros -> TEMP_LOGS_USUARIOS/********/USER_DATA_TRX.parquet
2025-06-06 20:46:29,208 - LogUsuariosPipeline - INFO - Ejecutando SP_USER_MODIFICATION para fecha: 2025-06-05
2025-06-06 20:46:32,657 - LogUsuariosPipeline - INFO - SP_USER_MODIFICATION completado: 7438 registros -> TEMP_LOGS_USUARIOS/********/USER_MODIFICATION_DAY.parquet
2025-06-06 20:46:32,657 - LogUsuariosPipeline - INFO - Ejecutando SP_USER_AUTH_DAY para fecha: 2025-06-05
2025-06-06 20:46:33,386 - LogUsuariosPipeline - INFO - SP_USER_AUTH_DAY completado: 5804 registros -> TEMP_LOGS_USUARIOS/********/USER_AUTH_CHANGE_HISTORY.parquet
2025-06-06 20:46:33,386 - LogUsuariosPipeline - INFO - PRE-LOG-USR: OK
2025-06-06 20:46:33,386 - LogUsuariosPipeline - INFO - LOG-USR: INICIANDO
2025-06-06 20:46:33,386 - LogUsuariosPipeline - INFO - Ejecutando SP_LOG_USR EXACTO (igual a Oracle) para fecha: 2025-06-05
2025-06-06 20:46:35,518 - LogUsuariosPipeline - INFO - SP_LOG_USR EXACTO completado: 21374 registros -> output/********/LOG_USR.parquet
2025-06-06 20:46:35,520 - LogUsuariosPipeline - INFO - LOG-USR: OK
2025-06-06 20:46:35,520 - LogUsuariosPipeline - INFO - EXPORT-CSV: INICIANDO
2025-06-06 20:46:35,520 - LogUsuariosPipeline - INFO - Iniciando exportación CSV con conversiones de perfiles para fecha: 2025-06-05
2025-06-06 20:46:35,521 - LogUsuariosPipeline - INFO - Aplicando SOLO deduplicación como Oracle original (SIN conversión de tipos)
2025-06-06 20:46:35,805 - LogUsuariosPipeline - INFO - Deduplicación completada (manteniendo REQUESTTYPE originales):
2025-06-06 20:46:35,805 - LogUsuariosPipeline - INFO -   - Registros originales: 21,374
2025-06-06 20:46:35,805 - LogUsuariosPipeline - INFO -   - Registros después de deduplicación: 17,305
2025-06-06 20:46:35,805 - LogUsuariosPipeline - INFO -   - Duplicados eliminados: 4,069
2025-06-06 20:46:35,808 - LogUsuariosPipeline - INFO - Tipos de transacción mantenidos:
2025-06-06 20:46:35,808 - LogUsuariosPipeline - INFO -   - User Modification: 7,193 registros
2025-06-06 20:46:35,808 - LogUsuariosPipeline - INFO -   - CHANGE_AUTH_FACTOR: 5,677 registros
2025-06-06 20:46:35,808 - LogUsuariosPipeline - INFO -   - ActivateUser: 1,877 registros
2025-06-06 20:46:35,808 - LogUsuariosPipeline - INFO -   - AfiliaUser: 1,877 registros
2025-06-06 20:46:35,808 - LogUsuariosPipeline - INFO -   - ClosedUserAccount: 218 registros
2025-06-06 20:46:35,808 - LogUsuariosPipeline - INFO -   - Delete User: 218 registros
2025-06-06 20:46:35,808 - LogUsuariosPipeline - INFO -   - ClosedAccount: 218 registros
2025-06-06 20:46:35,808 - LogUsuariosPipeline - INFO -   - Lock Wallet: 18 registros
2025-06-06 20:46:35,808 - LogUsuariosPipeline - INFO -   - Resume User: 7 registros
2025-06-06 20:46:35,808 - LogUsuariosPipeline - INFO -   - Suspend User: 2 registros
2025-06-06 20:46:35,808 - LogUsuariosPipeline - INFO - Aplicando conversiones de perfiles al archivo: output/********/LOG_USR_DEDUPLICATED.parquet
2025-06-06 20:46:35,808 - LogUsuariosPipeline - INFO - Cargando tabla de conversión de perfiles...
2025-06-06 20:46:35,811 - LogUsuariosPipeline - INFO - Tabla conv_perfil.csv cargada: 796 registros
2025-06-06 20:46:35,815 - LogUsuariosPipeline - INFO - Tabla de conversión de perfiles cargada exitosamente en DuckDB
2025-06-06 20:46:35,816 - LogUsuariosPipeline - ERROR - Error aplicando conversiones al archivo: Binder Error: Values list "cp_a" does not have a column named "PERFIL_CODIGO"

LINE 8: ...            LEFT JOIN conv_perfil_table cp_a ON l.perfilA = cp_a.PERFIL_CODIGO
                                                                       ^
2025-06-06 20:46:35,949 - LogUsuariosPipeline - INFO - CSV completo exportado: 21374 registros -> output/********/csv_exports/LOG-USUARIOS-********.csv
2025-06-06 20:46:35,949 - LogUsuariosPipeline - INFO - Iniciando exportación segmentada por BankDomain
2025-06-06 20:46:35,949 - LogUsuariosPipeline - INFO - Procesando TODOS los bancos del sistema: ['0231FCONFIANZA', 'BNACION', 'CCUSCO', 'CRANDES', 'FCOMPARTAMOS', 'FCONFIANZA', None]
2025-06-06 20:46:35,955 - LogUsuariosPipeline - INFO - Exportado dominio 0231FCONFIANZA: 0 registros (archivo vacío SIN headers) -> output/********/csv_exports/by_bank/LOG-USUARIOS-0231FCONFIANZA-********.csv
2025-06-06 20:46:35,982 - LogUsuariosPipeline - INFO - Exportado dominio BNACION: 87 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-BNACION-********.csv
2025-06-06 20:46:35,991 - LogUsuariosPipeline - INFO - Exportado dominio CCUSCO: 2 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-CCUSCO-********.csv
2025-06-06 20:46:36,012 - LogUsuariosPipeline - INFO - Exportado dominio CRANDES: 10 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-CRANDES-********.csv
2025-06-06 20:46:36,137 - LogUsuariosPipeline - INFO - Exportado dominio FCOMPARTAMOS: 21257 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-FCOMPARTAMOS-********.csv
2025-06-06 20:46:36,142 - LogUsuariosPipeline - INFO - Exportado dominio FCONFIANZA: 0 registros (archivo vacío SIN headers) -> output/********/csv_exports/by_bank/LOG-USUARIOS-FCONFIANZA-********.csv
2025-06-06 20:46:36,175 - LogUsuariosPipeline - INFO - Exportado dominio NULL: 18 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-NULL-********.csv
2025-06-06 20:46:36,175 - LogUsuariosPipeline - INFO - Exportación segmentada completada: 7 archivos generados
2025-06-06 20:46:36,175 - LogUsuariosPipeline - INFO - Estructura EXACTA como flujo original: solo by_bank (sin by_profile)
2025-06-06 20:46:36,175 - LogUsuariosPipeline - INFO - Archivo temporal eliminado: TEMP_LOGS_USUARIOS/********/WALLET_OLD.parquet
2025-06-06 20:46:36,175 - LogUsuariosPipeline - INFO - EXPORT-CSV: OK
2025-06-06 20:46:36,175 - LogUsuariosPipeline - INFO - === ARCHIVOS CSV GENERADOS ===
2025-06-06 20:46:36,175 - LogUsuariosPipeline - INFO - Total de archivos: 8
2025-06-06 20:46:36,175 - LogUsuariosPipeline - INFO - Archivo principal:
2025-06-06 20:46:36,175 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-********.csv
2025-06-06 20:46:36,175 - LogUsuariosPipeline - INFO - Archivos por BankDomain (7) - EXACTO como flujo original:
2025-06-06 20:46:36,175 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-0231FCONFIANZA-********.csv
2025-06-06 20:46:36,175 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-BNACION-********.csv
2025-06-06 20:46:36,175 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-CCUSCO-********.csv
2025-06-06 20:46:36,175 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-CRANDES-********.csv
2025-06-06 20:46:36,175 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-FCOMPARTAMOS-********.csv
2025-06-06 20:46:36,175 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-FCONFIANZA-********.csv
2025-06-06 20:46:36,176 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-NULL-********.csv
2025-06-06 20:46:36,176 - LogUsuariosPipeline - INFO - Estructura IDÉNTICA al flujo original: solo carpeta by_bank
2025-06-06 20:46:36,176 - LogUsuariosPipeline - INFO - === PIPELINE COMPLETADO EXITOSAMENTE ===
2025-06-06 20:46:36,176 - LogUsuariosPipeline - INFO - 
=== ARCHIVOS GENERADOS ===
2025-06-06 20:46:36,176 - LogUsuariosPipeline - INFO - Archivos temporales en TEMP_LOGS_USUARIOS/********:
2025-06-06 20:46:36,176 - LogUsuariosPipeline - INFO -   - USER_ACCOUNT_HISTORY.parquet (0.00 MB)
2025-06-06 20:46:36,176 - LogUsuariosPipeline - INFO -   - USER_DATA_TRX.parquet (126.93 MB)
2025-06-06 20:46:36,176 - LogUsuariosPipeline - INFO -   - USER_MODIFICATION_DAY.parquet (3.68 MB)
2025-06-06 20:46:36,176 - LogUsuariosPipeline - INFO -   - USER_AUTH_CHANGE_HISTORY.parquet (0.13 MB)
2025-06-06 20:46:36,176 - LogUsuariosPipeline - INFO - Archivos de salida en output/********:
2025-06-06 20:46:36,176 - LogUsuariosPipeline - INFO -   - LOG_USR.parquet (4.72 MB)
2025-06-06 20:46:36,176 - LogUsuariosPipeline - INFO -   - LOG_USR_DEDUPLICATED.parquet (4.61 MB)
2025-06-06 20:46:36,176 - LogUsuariosPipeline - INFO -   - csv_exports/LOG-USUARIOS-********.csv (33.71 MB)
2025-06-06 20:46:36,176 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-0231FCONFIANZA-********.csv (0.00 MB)
2025-06-06 20:46:36,176 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-BNACION-********.csv (0.07 MB)
2025-06-06 20:46:36,176 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-CCUSCO-********.csv (0.00 MB)
2025-06-06 20:46:36,176 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-CRANDES-********.csv (0.01 MB)
2025-06-06 20:46:36,176 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-FCOMPARTAMOS-********.csv (33.63 MB)
2025-06-06 20:46:36,176 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-FCONFIANZA-********.csv (0.00 MB)
2025-06-06 20:46:36,176 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-NULL-********.csv (0.00 MB)
2025-06-06 22:14:21,015 - LogUsuariosPipeline - INFO - Configurando credenciales S3...
2025-06-06 22:14:21,029 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-06 22:14:21,080 - LogUsuariosPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-06 22:14:21,081 - LogUsuariosPipeline - INFO - === INICIANDO PIPELINE LOG_USUARIOS MODERNIZADO ===
2025-06-06 22:14:21,081 - LogUsuariosPipeline - INFO - Fecha de procesamiento: 2025-06-05 (carpeta: ********)
2025-06-06 22:14:21,081 - LogUsuariosPipeline - INFO - USER-MODIFY: INICIANDO
2025-06-06 22:14:21,081 - LogUsuariosPipeline - INFO - Extrayendo USER_ACCOUNT_HISTORY para fecha: 2025-06-05
2025-06-06 22:14:21,211 - LogUsuariosPipeline - INFO - USER_ACCOUNT_HISTORY extraído: 4 registros -> TEMP_LOGS_USUARIOS/********/USER_ACCOUNT_HISTORY.parquet
2025-06-06 22:14:21,212 - LogUsuariosPipeline - INFO - USER-MODIFY: OK
2025-06-06 22:14:21,212 - LogUsuariosPipeline - INFO - PRE-LOG-USR: INICIANDO
2025-06-06 22:14:21,212 - LogUsuariosPipeline - INFO - Ejecutando SP_PRE_LOG_USR CORREGIDO (incluye USER_IDs múltiples) para fecha: 2025-06-05
2025-06-06 22:14:28,744 - LogUsuariosPipeline - INFO - SP_PRE_LOG_USR completado: 1581374 registros -> TEMP_LOGS_USUARIOS/********/USER_DATA_TRX.parquet
2025-06-06 22:14:28,744 - LogUsuariosPipeline - INFO - Ejecutando SP_USER_MODIFICATION para fecha: 2025-06-05
2025-06-06 22:14:31,791 - LogUsuariosPipeline - INFO - SP_USER_MODIFICATION completado: 7438 registros -> TEMP_LOGS_USUARIOS/********/USER_MODIFICATION_DAY.parquet
2025-06-06 22:14:31,791 - LogUsuariosPipeline - INFO - Ejecutando SP_USER_AUTH_DAY para fecha: 2025-06-05
2025-06-06 22:14:32,439 - LogUsuariosPipeline - INFO - SP_USER_AUTH_DAY completado: 5804 registros -> TEMP_LOGS_USUARIOS/********/USER_AUTH_CHANGE_HISTORY.parquet
2025-06-06 22:14:32,439 - LogUsuariosPipeline - INFO - PRE-LOG-USR: OK
2025-06-06 22:14:32,439 - LogUsuariosPipeline - INFO - LOG-USR: INICIANDO
2025-06-06 22:14:32,439 - LogUsuariosPipeline - INFO - Ejecutando SP_LOG_USR EXACTO (igual a Oracle) para fecha: 2025-06-05
2025-06-06 22:14:34,374 - LogUsuariosPipeline - INFO - SP_LOG_USR EXACTO completado: 21374 registros -> output/********/LOG_USR.parquet
2025-06-06 22:14:34,374 - LogUsuariosPipeline - INFO - LOG-USR: OK
2025-06-06 22:14:34,374 - LogUsuariosPipeline - INFO - EXPORT-CSV: INICIANDO
2025-06-06 22:14:34,374 - LogUsuariosPipeline - INFO - Iniciando exportación CSV con conversiones de perfiles para fecha: 2025-06-05
2025-06-06 22:14:34,375 - LogUsuariosPipeline - INFO - Aplicando SOLO deduplicación como Oracle original (SIN conversión de tipos)
2025-06-06 22:14:34,626 - LogUsuariosPipeline - INFO - Deduplicación completada (manteniendo REQUESTTYPE originales):
2025-06-06 22:14:34,626 - LogUsuariosPipeline - INFO -   - Registros originales: 21,374
2025-06-06 22:14:34,626 - LogUsuariosPipeline - INFO -   - Registros después de deduplicación: 17,305
2025-06-06 22:14:34,626 - LogUsuariosPipeline - INFO -   - Duplicados eliminados: 4,069
2025-06-06 22:14:34,629 - LogUsuariosPipeline - INFO - Tipos de transacción mantenidos:
2025-06-06 22:14:34,629 - LogUsuariosPipeline - INFO -   - User Modification: 7,193 registros
2025-06-06 22:14:34,629 - LogUsuariosPipeline - INFO -   - CHANGE_AUTH_FACTOR: 5,677 registros
2025-06-06 22:14:34,629 - LogUsuariosPipeline - INFO -   - AfiliaUser: 1,877 registros
2025-06-06 22:14:34,629 - LogUsuariosPipeline - INFO -   - ActivateUser: 1,877 registros
2025-06-06 22:14:34,629 - LogUsuariosPipeline - INFO -   - ClosedUserAccount: 218 registros
2025-06-06 22:14:34,629 - LogUsuariosPipeline - INFO -   - ClosedAccount: 218 registros
2025-06-06 22:14:34,629 - LogUsuariosPipeline - INFO -   - Delete User: 218 registros
2025-06-06 22:14:34,629 - LogUsuariosPipeline - INFO -   - Lock Wallet: 18 registros
2025-06-06 22:14:34,629 - LogUsuariosPipeline - INFO -   - Resume User: 7 registros
2025-06-06 22:14:34,629 - LogUsuariosPipeline - INFO -   - Suspend User: 2 registros
2025-06-06 22:14:34,629 - LogUsuariosPipeline - INFO - Aplicando conversiones de perfiles al archivo: output/********/LOG_USR_DEDUPLICATED.parquet
2025-06-06 22:14:34,629 - LogUsuariosPipeline - INFO - Cargando tabla de conversión de perfiles...
2025-06-06 22:14:34,632 - LogUsuariosPipeline - INFO - Tabla conv_perfil.csv cargada: 796 registros
2025-06-06 22:14:34,636 - LogUsuariosPipeline - INFO - Tabla de conversión de perfiles cargada exitosamente en DuckDB
2025-06-06 22:14:34,638 - LogUsuariosPipeline - ERROR - Error aplicando conversiones al archivo: Binder Error: Values list "cp_a" does not have a column named "PERFIL_CODIGO"

LINE 8: ...            LEFT JOIN conv_perfil_table cp_a ON l.perfilA = cp_a.PERFIL_CODIGO
                                                                       ^
2025-06-06 22:14:34,754 - LogUsuariosPipeline - INFO - CSV completo exportado: 21374 registros -> output/********/csv_exports/LOG-USUARIOS-********.csv
2025-06-06 22:14:34,754 - LogUsuariosPipeline - INFO - Iniciando exportación segmentada por BankDomain
2025-06-06 22:14:34,754 - LogUsuariosPipeline - INFO - Procesando TODOS los bancos del sistema: ['0231FCONFIANZA', 'BNACION', 'CCUSCO', 'CRANDES', 'FCOMPARTAMOS', 'FCONFIANZA', None]
2025-06-06 22:14:34,758 - LogUsuariosPipeline - INFO - Exportado dominio 0231FCONFIANZA: 0 registros (archivo vacío SIN headers) -> output/********/csv_exports/by_bank/LOG-USUARIOS-0231FCONFIANZA-********.csv
2025-06-06 22:14:34,778 - LogUsuariosPipeline - INFO - Exportado dominio BNACION: 87 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-BNACION-********.csv
2025-06-06 22:14:34,785 - LogUsuariosPipeline - INFO - Exportado dominio CCUSCO: 2 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-CCUSCO-********.csv
2025-06-06 22:14:34,801 - LogUsuariosPipeline - INFO - Exportado dominio CRANDES: 10 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-CRANDES-********.csv
2025-06-06 22:14:34,945 - LogUsuariosPipeline - INFO - Exportado dominio FCOMPARTAMOS: 21257 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-FCOMPARTAMOS-********.csv
2025-06-06 22:14:34,949 - LogUsuariosPipeline - INFO - Exportado dominio FCONFIANZA: 0 registros (archivo vacío SIN headers) -> output/********/csv_exports/by_bank/LOG-USUARIOS-FCONFIANZA-********.csv
2025-06-06 22:14:34,978 - LogUsuariosPipeline - INFO - Exportado dominio NULL: 18 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-NULL-********.csv
2025-06-06 22:14:34,978 - LogUsuariosPipeline - INFO - Exportación segmentada completada: 7 archivos generados
2025-06-06 22:14:34,978 - LogUsuariosPipeline - INFO - Estructura EXACTA como flujo original: solo by_bank (sin by_profile)
2025-06-06 22:14:34,978 - LogUsuariosPipeline - INFO - Archivo temporal eliminado: TEMP_LOGS_USUARIOS/********/WALLET_OLD.parquet
2025-06-06 22:14:34,979 - LogUsuariosPipeline - INFO - EXPORT-CSV: OK
2025-06-06 22:14:34,979 - LogUsuariosPipeline - INFO - === ARCHIVOS CSV GENERADOS ===
2025-06-06 22:14:34,979 - LogUsuariosPipeline - INFO - Total de archivos: 8
2025-06-06 22:14:34,979 - LogUsuariosPipeline - INFO - Archivo principal:
2025-06-06 22:14:34,979 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-********.csv
2025-06-06 22:14:34,979 - LogUsuariosPipeline - INFO - Archivos por BankDomain (7) - EXACTO como flujo original:
2025-06-06 22:14:34,979 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-0231FCONFIANZA-********.csv
2025-06-06 22:14:34,979 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-BNACION-********.csv
2025-06-06 22:14:34,979 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-CCUSCO-********.csv
2025-06-06 22:14:34,979 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-CRANDES-********.csv
2025-06-06 22:14:34,979 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-FCOMPARTAMOS-********.csv
2025-06-06 22:14:34,979 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-FCONFIANZA-********.csv
2025-06-06 22:14:34,979 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-NULL-********.csv
2025-06-06 22:14:34,979 - LogUsuariosPipeline - INFO - Estructura IDÉNTICA al flujo original: solo carpeta by_bank
2025-06-06 22:14:34,979 - LogUsuariosPipeline - INFO - === PIPELINE COMPLETADO EXITOSAMENTE ===
2025-06-06 22:14:34,979 - LogUsuariosPipeline - INFO - 
=== ARCHIVOS GENERADOS ===
2025-06-06 22:14:34,979 - LogUsuariosPipeline - INFO - Archivos temporales en TEMP_LOGS_USUARIOS/********:
2025-06-06 22:14:34,979 - LogUsuariosPipeline - INFO -   - USER_ACCOUNT_HISTORY.parquet (0.00 MB)
2025-06-06 22:14:34,979 - LogUsuariosPipeline - INFO -   - USER_DATA_TRX.parquet (126.93 MB)
2025-06-06 22:14:34,979 - LogUsuariosPipeline - INFO -   - USER_MODIFICATION_DAY.parquet (3.68 MB)
2025-06-06 22:14:34,979 - LogUsuariosPipeline - INFO -   - USER_AUTH_CHANGE_HISTORY.parquet (0.13 MB)
2025-06-06 22:14:34,979 - LogUsuariosPipeline - INFO - Archivos de salida en output/********:
2025-06-06 22:14:34,980 - LogUsuariosPipeline - INFO -   - LOG_USR.parquet (4.74 MB)
2025-06-06 22:14:34,980 - LogUsuariosPipeline - INFO -   - LOG_USR_DEDUPLICATED.parquet (4.61 MB)
2025-06-06 22:14:34,980 - LogUsuariosPipeline - INFO -   - csv_exports/LOG-USUARIOS-********.csv (33.71 MB)
2025-06-06 22:14:34,980 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-0231FCONFIANZA-********.csv (0.00 MB)
2025-06-06 22:14:34,980 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-BNACION-********.csv (0.07 MB)
2025-06-06 22:14:34,980 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-CCUSCO-********.csv (0.00 MB)
2025-06-06 22:14:34,980 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-CRANDES-********.csv (0.01 MB)
2025-06-06 22:14:34,980 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-FCOMPARTAMOS-********.csv (33.63 MB)
2025-06-06 22:14:34,980 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-FCONFIANZA-********.csv (0.00 MB)
2025-06-06 22:14:34,980 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-NULL-********.csv (0.00 MB)
2025-06-06 22:19:57,648 - LogUsuariosPipeline - INFO - Configurando credenciales S3...
2025-06-06 22:19:57,660 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-06 22:19:57,711 - LogUsuariosPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-06 22:19:57,712 - LogUsuariosPipeline - INFO - === INICIANDO PIPELINE LOG_USUARIOS MODERNIZADO ===
2025-06-06 22:19:57,712 - LogUsuariosPipeline - INFO - Fecha de procesamiento: 2025-06-05 (carpeta: ********)
2025-06-06 22:19:57,712 - LogUsuariosPipeline - INFO - USER-MODIFY: INICIANDO
2025-06-06 22:19:57,712 - LogUsuariosPipeline - INFO - Extrayendo USER_ACCOUNT_HISTORY para fecha: 2025-06-05
2025-06-06 22:19:57,912 - LogUsuariosPipeline - INFO - USER_ACCOUNT_HISTORY extraído: 4 registros -> TEMP_LOGS_USUARIOS/********/USER_ACCOUNT_HISTORY.parquet
2025-06-06 22:19:57,912 - LogUsuariosPipeline - INFO - USER-MODIFY: OK
2025-06-06 22:19:57,912 - LogUsuariosPipeline - INFO - PRE-LOG-USR: INICIANDO
2025-06-06 22:19:57,912 - LogUsuariosPipeline - INFO - Ejecutando SP_PRE_LOG_USR CORREGIDO (incluye USER_IDs múltiples) para fecha: 2025-06-05
2025-06-06 22:20:05,570 - LogUsuariosPipeline - INFO - SP_PRE_LOG_USR completado: 1581374 registros -> TEMP_LOGS_USUARIOS/********/USER_DATA_TRX.parquet
2025-06-06 22:20:05,571 - LogUsuariosPipeline - INFO - Ejecutando SP_USER_MODIFICATION para fecha: 2025-06-05
2025-06-06 22:20:09,168 - LogUsuariosPipeline - INFO - SP_USER_MODIFICATION completado: 7438 registros -> TEMP_LOGS_USUARIOS/********/USER_MODIFICATION_DAY.parquet
2025-06-06 22:20:09,168 - LogUsuariosPipeline - INFO - Ejecutando SP_USER_AUTH_DAY para fecha: 2025-06-05
2025-06-06 22:20:09,820 - LogUsuariosPipeline - INFO - SP_USER_AUTH_DAY completado: 5804 registros -> TEMP_LOGS_USUARIOS/********/USER_AUTH_CHANGE_HISTORY.parquet
2025-06-06 22:20:09,820 - LogUsuariosPipeline - INFO - PRE-LOG-USR: OK
2025-06-06 22:20:09,820 - LogUsuariosPipeline - INFO - LOG-USR: INICIANDO
2025-06-06 22:20:09,820 - LogUsuariosPipeline - INFO - Ejecutando SP_LOG_USR EXACTO (igual a Oracle) para fecha: 2025-06-05
2025-06-06 22:20:11,854 - LogUsuariosPipeline - INFO - SP_LOG_USR EXACTO completado: 21374 registros -> output/********/LOG_USR.parquet
2025-06-06 22:20:11,856 - LogUsuariosPipeline - INFO - LOG-USR: OK
2025-06-06 22:20:11,856 - LogUsuariosPipeline - INFO - EXPORT-CSV: INICIANDO
2025-06-06 22:20:11,856 - LogUsuariosPipeline - INFO - Iniciando exportación CSV con conversiones de perfiles para fecha: 2025-06-05
2025-06-06 22:20:11,856 - LogUsuariosPipeline - INFO - Aplicando SOLO deduplicación como Oracle original (SIN conversión de tipos)
2025-06-06 22:20:12,178 - LogUsuariosPipeline - INFO - Deduplicación completada (manteniendo REQUESTTYPE originales):
2025-06-06 22:20:12,178 - LogUsuariosPipeline - INFO -   - Registros originales: 21,374
2025-06-06 22:20:12,178 - LogUsuariosPipeline - INFO -   - Registros después de deduplicación: 17,305
2025-06-06 22:20:12,178 - LogUsuariosPipeline - INFO -   - Duplicados eliminados: 4,069
2025-06-06 22:20:12,180 - LogUsuariosPipeline - INFO - Tipos de transacción mantenidos:
2025-06-06 22:20:12,181 - LogUsuariosPipeline - INFO -   - User Modification: 7,193 registros
2025-06-06 22:20:12,181 - LogUsuariosPipeline - INFO -   - CHANGE_AUTH_FACTOR: 5,677 registros
2025-06-06 22:20:12,181 - LogUsuariosPipeline - INFO -   - ActivateUser: 1,877 registros
2025-06-06 22:20:12,181 - LogUsuariosPipeline - INFO -   - AfiliaUser: 1,877 registros
2025-06-06 22:20:12,181 - LogUsuariosPipeline - INFO -   - ClosedUserAccount: 218 registros
2025-06-06 22:20:12,181 - LogUsuariosPipeline - INFO -   - ClosedAccount: 218 registros
2025-06-06 22:20:12,181 - LogUsuariosPipeline - INFO -   - Delete User: 218 registros
2025-06-06 22:20:12,181 - LogUsuariosPipeline - INFO -   - Lock Wallet: 18 registros
2025-06-06 22:20:12,181 - LogUsuariosPipeline - INFO -   - Resume User: 7 registros
2025-06-06 22:20:12,181 - LogUsuariosPipeline - INFO -   - Suspend User: 2 registros
2025-06-06 22:20:12,181 - LogUsuariosPipeline - INFO - Aplicando conversiones de perfiles al archivo: output/********/LOG_USR_DEDUPLICATED.parquet
2025-06-06 22:20:12,181 - LogUsuariosPipeline - INFO - Cargando tabla de conversión de perfiles...
2025-06-06 22:20:12,184 - LogUsuariosPipeline - INFO - Tabla conv_perfil.csv cargada: 796 registros
2025-06-06 22:20:12,190 - LogUsuariosPipeline - INFO - Tabla de conversión de perfiles cargada exitosamente en DuckDB
2025-06-06 22:20:12,192 - LogUsuariosPipeline - ERROR - Error aplicando conversiones al archivo: Binder Error: Values list "cp_a" does not have a column named "PERFIL_CODIGO"

LINE 8: ...            LEFT JOIN conv_perfil_table cp_a ON l.perfilA = cp_a.PERFIL_CODIGO
                                                                       ^
2025-06-06 22:20:12,192 - LogUsuariosPipeline - INFO - Omitiendo CSV principal redundante - usando solo archivos Parquet y CSVs segmentados
2025-06-06 22:20:12,192 - LogUsuariosPipeline - INFO - Iniciando exportación segmentada por BankDomain
2025-06-06 22:20:12,192 - LogUsuariosPipeline - INFO - Procesando TODOS los bancos del sistema: ['0231FCONFIANZA', 'BNACION', 'CCUSCO', 'CRANDES', 'FCOMPARTAMOS', 'FCONFIANZA', None]
2025-06-06 22:20:12,196 - LogUsuariosPipeline - INFO - Exportado dominio 0231FCONFIANZA: 0 registros (archivo vacío SIN headers) -> output/********/csv_exports/by_bank/LOG-USUARIOS-0231FCONFIANZA-********.csv
2025-06-06 22:20:12,224 - LogUsuariosPipeline - INFO - Exportado dominio BNACION: 87 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-BNACION-********.csv
2025-06-06 22:20:12,231 - LogUsuariosPipeline - INFO - Exportado dominio CCUSCO: 2 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-CCUSCO-********.csv
2025-06-06 22:20:12,257 - LogUsuariosPipeline - INFO - Exportado dominio CRANDES: 10 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-CRANDES-********.csv
2025-06-06 22:20:12,379 - LogUsuariosPipeline - INFO - Exportado dominio FCOMPARTAMOS: 21257 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-FCOMPARTAMOS-********.csv
2025-06-06 22:20:12,383 - LogUsuariosPipeline - INFO - Exportado dominio FCONFIANZA: 0 registros (archivo vacío SIN headers) -> output/********/csv_exports/by_bank/LOG-USUARIOS-FCONFIANZA-********.csv
2025-06-06 22:20:12,413 - LogUsuariosPipeline - INFO - Exportado dominio NULL: 18 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-NULL-********.csv
2025-06-06 22:20:12,413 - LogUsuariosPipeline - INFO - Exportación segmentada completada: 7 archivos generados
2025-06-06 22:20:12,413 - LogUsuariosPipeline - INFO - Estructura EXACTA como flujo original: solo by_bank (sin by_profile)
2025-06-06 22:20:12,413 - LogUsuariosPipeline - INFO - Archivo temporal eliminado: TEMP_LOGS_USUARIOS/********/WALLET_OLD.parquet
2025-06-06 22:20:12,413 - LogUsuariosPipeline - INFO - EXPORT-CSV: OK
2025-06-06 22:20:12,413 - LogUsuariosPipeline - INFO - === ARCHIVOS CSV GENERADOS ===
2025-06-06 22:20:12,413 - LogUsuariosPipeline - INFO - Total de archivos CSV segmentados: 7
2025-06-06 22:20:12,413 - LogUsuariosPipeline - INFO - Archivos por BankDomain (7) - EXACTO como flujo original:
2025-06-06 22:20:12,413 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-0231FCONFIANZA-********.csv
2025-06-06 22:20:12,413 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-BNACION-********.csv
2025-06-06 22:20:12,413 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-CCUSCO-********.csv
2025-06-06 22:20:12,413 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-CRANDES-********.csv
2025-06-06 22:20:12,413 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-FCOMPARTAMOS-********.csv
2025-06-06 22:20:12,414 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-FCONFIANZA-********.csv
2025-06-06 22:20:12,414 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-NULL-********.csv
2025-06-06 22:20:12,414 - LogUsuariosPipeline - INFO - Estructura OPTIMIZADA: solo archivos segmentados (datos completos en Parquet)
2025-06-06 22:20:12,414 - LogUsuariosPipeline - INFO - === PIPELINE COMPLETADO EXITOSAMENTE ===
2025-06-06 22:20:12,414 - LogUsuariosPipeline - INFO - 
=== ARCHIVOS GENERADOS ===
2025-06-06 22:20:12,414 - LogUsuariosPipeline - INFO - Archivos temporales en TEMP_LOGS_USUARIOS/********:
2025-06-06 22:20:12,414 - LogUsuariosPipeline - INFO -   - USER_ACCOUNT_HISTORY.parquet (0.00 MB)
2025-06-06 22:20:12,414 - LogUsuariosPipeline - INFO -   - USER_DATA_TRX.parquet (126.93 MB)
2025-06-06 22:20:12,414 - LogUsuariosPipeline - INFO -   - USER_MODIFICATION_DAY.parquet (3.68 MB)
2025-06-06 22:20:12,414 - LogUsuariosPipeline - INFO -   - USER_AUTH_CHANGE_HISTORY.parquet (0.13 MB)
2025-06-06 22:20:12,414 - LogUsuariosPipeline - INFO - Archivos de salida en output/********:
2025-06-06 22:20:12,414 - LogUsuariosPipeline - INFO -   - LOG_USR.parquet (4.74 MB)
2025-06-06 22:20:12,414 - LogUsuariosPipeline - INFO -   - LOG_USR_DEDUPLICATED.parquet (4.61 MB)
2025-06-06 22:20:12,414 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-0231FCONFIANZA-********.csv (0.00 MB)
2025-06-06 22:20:12,414 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-BNACION-********.csv (0.07 MB)
2025-06-06 22:20:12,414 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-CCUSCO-********.csv (0.00 MB)
2025-06-06 22:20:12,414 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-CRANDES-********.csv (0.01 MB)
2025-06-06 22:20:12,414 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-FCOMPARTAMOS-********.csv (33.63 MB)
2025-06-06 22:20:12,414 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-FCONFIANZA-********.csv (0.00 MB)
2025-06-06 22:20:12,414 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-NULL-********.csv (0.00 MB)
2025-06-06 22:29:13,569 - LogUsuariosPipeline - INFO - Configurando credenciales S3...
2025-06-06 22:29:13,582 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-06 22:29:13,635 - LogUsuariosPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-06 22:29:13,636 - LogUsuariosPipeline - INFO - === INICIANDO PIPELINE LOG_USUARIOS MODERNIZADO ===
2025-06-06 22:29:13,636 - LogUsuariosPipeline - INFO - Fecha de procesamiento: 2025-06-05 (carpeta: ********)
2025-06-06 22:29:13,636 - LogUsuariosPipeline - INFO - USER-MODIFY: INICIANDO
2025-06-06 22:29:13,636 - LogUsuariosPipeline - INFO - Extrayendo USER_ACCOUNT_HISTORY para fecha: 2025-06-05
2025-06-06 22:29:13,761 - LogUsuariosPipeline - INFO - USER_ACCOUNT_HISTORY extraído: 4 registros -> TEMP_LOGS_USUARIOS/********/USER_ACCOUNT_HISTORY.parquet
2025-06-06 22:29:13,761 - LogUsuariosPipeline - INFO - USER-MODIFY: OK
2025-06-06 22:29:13,761 - LogUsuariosPipeline - INFO - PRE-LOG-USR: INICIANDO
2025-06-06 22:29:13,761 - LogUsuariosPipeline - INFO - Ejecutando SP_PRE_LOG_USR CORREGIDO (incluye USER_IDs múltiples) para fecha: 2025-06-05
2025-06-06 22:29:21,318 - LogUsuariosPipeline - INFO - SP_PRE_LOG_USR completado: 1581374 registros -> TEMP_LOGS_USUARIOS/********/USER_DATA_TRX.parquet
2025-06-06 22:29:21,318 - LogUsuariosPipeline - INFO - Ejecutando SP_USER_MODIFICATION para fecha: 2025-06-05
2025-06-06 22:29:23,914 - LogUsuariosPipeline - INFO - SP_USER_MODIFICATION completado: 7438 registros -> TEMP_LOGS_USUARIOS/********/USER_MODIFICATION_DAY.parquet
2025-06-06 22:29:23,914 - LogUsuariosPipeline - INFO - Ejecutando SP_USER_AUTH_DAY para fecha: 2025-06-05
2025-06-06 22:29:24,536 - LogUsuariosPipeline - INFO - SP_USER_AUTH_DAY completado: 5804 registros -> TEMP_LOGS_USUARIOS/********/USER_AUTH_CHANGE_HISTORY.parquet
2025-06-06 22:29:24,537 - LogUsuariosPipeline - INFO - PRE-LOG-USR: OK
2025-06-06 22:29:24,537 - LogUsuariosPipeline - INFO - LOG-USR: INICIANDO
2025-06-06 22:29:24,537 - LogUsuariosPipeline - INFO - Ejecutando SP_LOG_USR EXACTO (igual a Oracle) para fecha: 2025-06-05
2025-06-06 22:29:26,729 - LogUsuariosPipeline - INFO - SP_LOG_USR EXACTO completado: 21374 registros -> output/********/LOG_USR.parquet
2025-06-06 22:29:26,730 - LogUsuariosPipeline - INFO - LOG-USR: OK
2025-06-06 22:29:26,730 - LogUsuariosPipeline - INFO - EXPORT-CSV: INICIANDO
2025-06-06 22:29:26,730 - LogUsuariosPipeline - INFO - Iniciando exportación CSV con conversiones de perfiles para fecha: 2025-06-05
2025-06-06 22:29:26,730 - LogUsuariosPipeline - INFO - Aplicando SOLO deduplicación como Oracle original (SIN conversión de tipos)
2025-06-06 22:29:26,993 - LogUsuariosPipeline - INFO - Deduplicación completada (manteniendo REQUESTTYPE originales):
2025-06-06 22:29:26,993 - LogUsuariosPipeline - INFO -   - Registros originales: 21,374
2025-06-06 22:29:26,993 - LogUsuariosPipeline - INFO -   - Registros después de deduplicación: 17,305
2025-06-06 22:29:26,993 - LogUsuariosPipeline - INFO -   - Duplicados eliminados: 4,069
2025-06-06 22:29:26,996 - LogUsuariosPipeline - INFO - Tipos de transacción mantenidos:
2025-06-06 22:29:26,996 - LogUsuariosPipeline - INFO -   - User Modification: 7,193 registros
2025-06-06 22:29:26,996 - LogUsuariosPipeline - INFO -   - CHANGE_AUTH_FACTOR: 5,677 registros
2025-06-06 22:29:26,996 - LogUsuariosPipeline - INFO -   - ActivateUser: 1,877 registros
2025-06-06 22:29:26,996 - LogUsuariosPipeline - INFO -   - AfiliaUser: 1,877 registros
2025-06-06 22:29:26,996 - LogUsuariosPipeline - INFO -   - Delete User: 218 registros
2025-06-06 22:29:26,996 - LogUsuariosPipeline - INFO -   - ClosedUserAccount: 218 registros
2025-06-06 22:29:26,996 - LogUsuariosPipeline - INFO -   - ClosedAccount: 218 registros
2025-06-06 22:29:26,996 - LogUsuariosPipeline - INFO -   - Lock Wallet: 18 registros
2025-06-06 22:29:26,996 - LogUsuariosPipeline - INFO -   - Resume User: 7 registros
2025-06-06 22:29:26,996 - LogUsuariosPipeline - INFO -   - Suspend User: 2 registros
2025-06-06 22:29:26,996 - LogUsuariosPipeline - INFO - Aplicando conversiones de perfiles al archivo: output/********/LOG_USR_DEDUPLICATED.parquet
2025-06-06 22:29:26,996 - LogUsuariosPipeline - INFO - Cargando tabla de conversión de perfiles...
2025-06-06 22:29:26,999 - LogUsuariosPipeline - INFO - Tabla conv_perfil.csv cargada: 796 registros
2025-06-06 22:29:27,003 - LogUsuariosPipeline - INFO - Tabla de conversión de perfiles cargada exitosamente en DuckDB
2025-06-06 22:29:27,004 - LogUsuariosPipeline - ERROR - Error aplicando conversiones al archivo: Binder Error: Values list "cp_a" does not have a column named "PERFIL_CODIGO"

LINE 8: ...            LEFT JOIN conv_perfil_table cp_a ON l.perfilA = cp_a.PERFIL_CODIGO
                                                                       ^
2025-06-06 22:29:27,005 - LogUsuariosPipeline - INFO - Omitiendo CSV principal redundante - usando solo archivos Parquet y CSVs segmentados
2025-06-06 22:29:27,005 - LogUsuariosPipeline - INFO - Iniciando exportación segmentada por BankDomain
2025-06-06 22:29:27,005 - LogUsuariosPipeline - INFO - Procesando TODOS los bancos del sistema: ['0231FCONFIANZA', 'BNACION', 'CCUSCO', 'CRANDES', 'FCOMPARTAMOS', 'FCONFIANZA', None]
2025-06-06 22:29:27,008 - LogUsuariosPipeline - INFO - Exportado dominio 0231FCONFIANZA: 0 registros (archivo vacío SIN headers) -> output/********/csv_exports/by_bank/LOG-USUARIOS-0231FCONFIANZA-********.csv
2025-06-06 22:29:27,028 - LogUsuariosPipeline - INFO - Exportado dominio BNACION: 87 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-BNACION-********.csv
2025-06-06 22:29:27,034 - LogUsuariosPipeline - INFO - Exportado dominio CCUSCO: 2 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-CCUSCO-********.csv
2025-06-06 22:29:27,061 - LogUsuariosPipeline - INFO - Exportado dominio CRANDES: 10 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-CRANDES-********.csv
2025-06-06 22:29:27,171 - LogUsuariosPipeline - INFO - Exportado dominio FCOMPARTAMOS: 21257 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-FCOMPARTAMOS-********.csv
2025-06-06 22:29:27,175 - LogUsuariosPipeline - INFO - Exportado dominio FCONFIANZA: 0 registros (archivo vacío SIN headers) -> output/********/csv_exports/by_bank/LOG-USUARIOS-FCONFIANZA-********.csv
2025-06-06 22:29:27,223 - LogUsuariosPipeline - INFO - Exportado dominio NULL: 18 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-NULL-********.csv
2025-06-06 22:29:27,223 - LogUsuariosPipeline - INFO - Exportación segmentada completada: 7 archivos generados
2025-06-06 22:29:27,223 - LogUsuariosPipeline - INFO - Estructura EXACTA como flujo original: solo by_bank (sin by_profile)
2025-06-06 22:29:27,224 - LogUsuariosPipeline - INFO - Archivo temporal eliminado: TEMP_LOGS_USUARIOS/********/WALLET_OLD.parquet
2025-06-06 22:29:27,224 - LogUsuariosPipeline - INFO - EXPORT-CSV: OK
2025-06-06 22:29:27,224 - LogUsuariosPipeline - INFO - === ARCHIVOS CSV GENERADOS ===
2025-06-06 22:29:27,224 - LogUsuariosPipeline - INFO - Total de archivos CSV segmentados: 7
2025-06-06 22:29:27,224 - LogUsuariosPipeline - INFO - Archivos por BankDomain (7) - EXACTO como flujo original:
2025-06-06 22:29:27,224 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-0231FCONFIANZA-********.csv
2025-06-06 22:29:27,224 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-BNACION-********.csv
2025-06-06 22:29:27,224 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-CCUSCO-********.csv
2025-06-06 22:29:27,224 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-CRANDES-********.csv
2025-06-06 22:29:27,224 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-FCOMPARTAMOS-********.csv
2025-06-06 22:29:27,224 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-FCONFIANZA-********.csv
2025-06-06 22:29:27,224 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-NULL-********.csv
2025-06-06 22:29:27,224 - LogUsuariosPipeline - INFO - Estructura OPTIMIZADA: solo archivos segmentados (datos completos en Parquet)
2025-06-06 22:29:27,224 - LogUsuariosPipeline - INFO - === PIPELINE COMPLETADO EXITOSAMENTE ===
2025-06-06 22:29:27,224 - LogUsuariosPipeline - INFO - 
=== ARCHIVOS GENERADOS ===
2025-06-06 22:29:27,224 - LogUsuariosPipeline - INFO - Archivos temporales en TEMP_LOGS_USUARIOS/********:
2025-06-06 22:29:27,224 - LogUsuariosPipeline - INFO -   - USER_ACCOUNT_HISTORY.parquet (0.00 MB)
2025-06-06 22:29:27,224 - LogUsuariosPipeline - INFO -   - USER_DATA_TRX.parquet (126.93 MB)
2025-06-06 22:29:27,224 - LogUsuariosPipeline - INFO -   - USER_MODIFICATION_DAY.parquet (3.68 MB)
2025-06-06 22:29:27,224 - LogUsuariosPipeline - INFO -   - USER_AUTH_CHANGE_HISTORY.parquet (0.13 MB)
2025-06-06 22:29:27,224 - LogUsuariosPipeline - INFO - Archivos de salida en output/********:
2025-06-06 22:29:27,224 - LogUsuariosPipeline - INFO -   - LOG_USR.parquet (4.74 MB)
2025-06-06 22:29:27,224 - LogUsuariosPipeline - INFO -   - LOG_USR_DEDUPLICATED.parquet (4.61 MB)
2025-06-06 22:29:27,225 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-0231FCONFIANZA-********.csv (0.00 MB)
2025-06-06 22:29:27,225 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-BNACION-********.csv (0.07 MB)
2025-06-06 22:29:27,225 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-CCUSCO-********.csv (0.00 MB)
2025-06-06 22:29:27,225 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-CRANDES-********.csv (0.01 MB)
2025-06-06 22:29:27,225 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-FCOMPARTAMOS-********.csv (33.63 MB)
2025-06-06 22:29:27,225 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-FCONFIANZA-********.csv (0.00 MB)
2025-06-06 22:29:27,225 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-NULL-********.csv (0.00 MB)
2025-06-06 22:40:18,118 - LogUsuariosPipeline - INFO - Configurando credenciales S3...
2025-06-06 22:40:18,131 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-06 22:40:18,188 - LogUsuariosPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-06 22:40:18,189 - LogUsuariosPipeline - INFO - === INICIANDO PIPELINE LOG_USUARIOS MODERNIZADO ===
2025-06-06 22:40:18,189 - LogUsuariosPipeline - INFO - Fecha de procesamiento: 2025-06-05 (carpeta: ********)
2025-06-06 22:40:18,189 - LogUsuariosPipeline - INFO - USER-MODIFY: INICIANDO
2025-06-06 22:40:18,189 - LogUsuariosPipeline - INFO - Extrayendo USER_ACCOUNT_HISTORY para fecha: 2025-06-05
2025-06-06 22:40:18,321 - LogUsuariosPipeline - INFO - USER_ACCOUNT_HISTORY extraído: 4 registros -> TEMP_LOGS_USUARIOS/********/USER_ACCOUNT_HISTORY.parquet
2025-06-06 22:40:18,321 - LogUsuariosPipeline - INFO - USER-MODIFY: OK
2025-06-06 22:40:18,321 - LogUsuariosPipeline - INFO - PRE-LOG-USR: INICIANDO
2025-06-06 22:40:18,321 - LogUsuariosPipeline - INFO - Ejecutando SP_PRE_LOG_USR CORREGIDO (incluye USER_IDs múltiples) para fecha: 2025-06-05
2025-06-06 22:40:26,072 - LogUsuariosPipeline - INFO - SP_PRE_LOG_USR completado: 1581374 registros -> TEMP_LOGS_USUARIOS/********/USER_DATA_TRX.parquet
2025-06-06 22:40:26,072 - LogUsuariosPipeline - INFO - Ejecutando SP_USER_MODIFICATION para fecha: 2025-06-05
2025-06-06 22:40:28,993 - LogUsuariosPipeline - INFO - SP_USER_MODIFICATION completado: 7438 registros -> TEMP_LOGS_USUARIOS/********/USER_MODIFICATION_DAY.parquet
2025-06-06 22:40:28,994 - LogUsuariosPipeline - INFO - Ejecutando SP_USER_AUTH_DAY para fecha: 2025-06-05
2025-06-06 22:40:29,727 - LogUsuariosPipeline - INFO - SP_USER_AUTH_DAY completado: 5804 registros -> TEMP_LOGS_USUARIOS/********/USER_AUTH_CHANGE_HISTORY.parquet
2025-06-06 22:40:29,727 - LogUsuariosPipeline - INFO - PRE-LOG-USR: OK
2025-06-06 22:40:29,727 - LogUsuariosPipeline - INFO - LOG-USR: INICIANDO
2025-06-06 22:40:29,728 - LogUsuariosPipeline - INFO - Ejecutando SP_LOG_USR EXACTO (igual a Oracle) para fecha: 2025-06-05
2025-06-06 22:40:31,640 - LogUsuariosPipeline - INFO - SP_LOG_USR EXACTO completado: 21374 registros -> output/********/LOG_USR.parquet
2025-06-06 22:40:31,641 - LogUsuariosPipeline - INFO - LOG-USR: OK
2025-06-06 22:40:31,641 - LogUsuariosPipeline - INFO - EXPORT-CSV: INICIANDO
2025-06-06 22:40:31,641 - LogUsuariosPipeline - INFO - Iniciando exportación CSV con conversiones de perfiles para fecha: 2025-06-05
2025-06-06 22:40:31,641 - LogUsuariosPipeline - INFO - Aplicando SOLO deduplicación como Oracle original (SIN conversión de tipos)
2025-06-06 22:40:31,891 - LogUsuariosPipeline - INFO - Deduplicación completada (manteniendo REQUESTTYPE originales):
2025-06-06 22:40:31,891 - LogUsuariosPipeline - INFO -   - Registros originales: 21,374
2025-06-06 22:40:31,891 - LogUsuariosPipeline - INFO -   - Registros después de deduplicación: 17,305
2025-06-06 22:40:31,891 - LogUsuariosPipeline - INFO -   - Duplicados eliminados: 4,069
2025-06-06 22:40:31,893 - LogUsuariosPipeline - INFO - Tipos de transacción mantenidos:
2025-06-06 22:40:31,893 - LogUsuariosPipeline - INFO -   - User Modification: 7,193 registros
2025-06-06 22:40:31,893 - LogUsuariosPipeline - INFO -   - CHANGE_AUTH_FACTOR: 5,677 registros
2025-06-06 22:40:31,893 - LogUsuariosPipeline - INFO -   - ActivateUser: 1,877 registros
2025-06-06 22:40:31,893 - LogUsuariosPipeline - INFO -   - AfiliaUser: 1,877 registros
2025-06-06 22:40:31,893 - LogUsuariosPipeline - INFO -   - ClosedUserAccount: 218 registros
2025-06-06 22:40:31,893 - LogUsuariosPipeline - INFO -   - Delete User: 218 registros
2025-06-06 22:40:31,893 - LogUsuariosPipeline - INFO -   - ClosedAccount: 218 registros
2025-06-06 22:40:31,893 - LogUsuariosPipeline - INFO -   - Lock Wallet: 18 registros
2025-06-06 22:40:31,893 - LogUsuariosPipeline - INFO -   - Resume User: 7 registros
2025-06-06 22:40:31,894 - LogUsuariosPipeline - INFO -   - Suspend User: 2 registros
2025-06-06 22:40:31,894 - LogUsuariosPipeline - INFO - Aplicando conversiones de perfiles al archivo: output/********/LOG_USR_DEDUPLICATED.parquet
2025-06-06 22:40:31,894 - LogUsuariosPipeline - INFO - Cargando tabla de conversión de perfiles...
2025-06-06 22:40:31,896 - LogUsuariosPipeline - INFO - Tabla conv_perfil.csv cargada: 796 registros
2025-06-06 22:40:31,900 - LogUsuariosPipeline - INFO - Tabla de conversión de perfiles cargada exitosamente en DuckDB
2025-06-06 22:40:31,902 - LogUsuariosPipeline - ERROR - Error aplicando conversiones al archivo: Binder Error: Values list "cp_a" does not have a column named "PERFIL_CODIGO"

LINE 8: ...            LEFT JOIN conv_perfil_table cp_a ON l.perfilA = cp_a.PERFIL_CODIGO
                                                                       ^
2025-06-06 22:40:31,902 - LogUsuariosPipeline - INFO - Omitiendo CSV principal redundante - usando solo archivos Parquet y CSVs segmentados
2025-06-06 22:40:31,902 - LogUsuariosPipeline - INFO - Iniciando exportación segmentada por BankDomain
2025-06-06 22:40:31,902 - LogUsuariosPipeline - INFO - Procesando TODOS los bancos del sistema: ['0231FCONFIANZA', 'BNACION', 'CCUSCO', 'CRANDES', 'FCOMPARTAMOS', 'FCONFIANZA', None]
2025-06-06 22:40:31,906 - LogUsuariosPipeline - INFO - Exportado dominio 0231FCONFIANZA: 0 registros (archivo vacío SIN headers) -> output/********/csv_exports/by_bank/LOG-USUARIOS-0231FCONFIANZA-********.csv
2025-06-06 22:40:31,927 - LogUsuariosPipeline - INFO - Exportado dominio BNACION: 87 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-BNACION-********.csv
2025-06-06 22:40:31,934 - LogUsuariosPipeline - INFO - Exportado dominio CCUSCO: 2 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-CCUSCO-********.csv
2025-06-06 22:40:31,955 - LogUsuariosPipeline - INFO - Exportado dominio CRANDES: 10 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-CRANDES-********.csv
2025-06-06 22:40:32,078 - LogUsuariosPipeline - INFO - Exportado dominio FCOMPARTAMOS: 21257 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-FCOMPARTAMOS-********.csv
2025-06-06 22:40:32,082 - LogUsuariosPipeline - INFO - Exportado dominio FCONFIANZA: 0 registros (archivo vacío SIN headers) -> output/********/csv_exports/by_bank/LOG-USUARIOS-FCONFIANZA-********.csv
2025-06-06 22:40:32,115 - LogUsuariosPipeline - INFO - Exportado dominio NULL: 18 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-NULL-********.csv
2025-06-06 22:40:32,115 - LogUsuariosPipeline - INFO - Exportación segmentada completada: 7 archivos generados
2025-06-06 22:40:32,115 - LogUsuariosPipeline - INFO - Estructura EXACTA como flujo original: solo by_bank (sin by_profile)
2025-06-06 22:40:32,115 - LogUsuariosPipeline - INFO - Archivo temporal eliminado: TEMP_LOGS_USUARIOS/********/WALLET_OLD.parquet
2025-06-06 22:40:32,115 - LogUsuariosPipeline - INFO - EXPORT-CSV: OK
2025-06-06 22:40:32,115 - LogUsuariosPipeline - INFO - === ARCHIVOS CSV GENERADOS ===
2025-06-06 22:40:32,115 - LogUsuariosPipeline - INFO - Total de archivos CSV segmentados: 7
2025-06-06 22:40:32,115 - LogUsuariosPipeline - INFO - Archivos por BankDomain (7) - EXACTO como flujo original:
2025-06-06 22:40:32,115 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-0231FCONFIANZA-********.csv
2025-06-06 22:40:32,115 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-BNACION-********.csv
2025-06-06 22:40:32,115 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-CCUSCO-********.csv
2025-06-06 22:40:32,115 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-CRANDES-********.csv
2025-06-06 22:40:32,115 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-FCOMPARTAMOS-********.csv
2025-06-06 22:40:32,115 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-FCONFIANZA-********.csv
2025-06-06 22:40:32,115 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-NULL-********.csv
2025-06-06 22:40:32,115 - LogUsuariosPipeline - INFO - Estructura OPTIMIZADA: solo archivos segmentados (datos completos en Parquet)
2025-06-06 22:40:32,115 - LogUsuariosPipeline - INFO - === PIPELINE COMPLETADO EXITOSAMENTE ===
2025-06-06 22:40:32,115 - LogUsuariosPipeline - INFO - 
=== ARCHIVOS GENERADOS ===
2025-06-06 22:40:32,115 - LogUsuariosPipeline - INFO - Archivos temporales en TEMP_LOGS_USUARIOS/********:
2025-06-06 22:40:32,116 - LogUsuariosPipeline - INFO -   - USER_ACCOUNT_HISTORY.parquet (0.00 MB)
2025-06-06 22:40:32,116 - LogUsuariosPipeline - INFO -   - USER_DATA_TRX.parquet (126.93 MB)
2025-06-06 22:40:32,116 - LogUsuariosPipeline - INFO -   - USER_MODIFICATION_DAY.parquet (3.68 MB)
2025-06-06 22:40:32,116 - LogUsuariosPipeline - INFO -   - USER_AUTH_CHANGE_HISTORY.parquet (0.13 MB)
2025-06-06 22:40:32,116 - LogUsuariosPipeline - INFO - Archivos de salida en output/********:
2025-06-06 22:40:32,116 - LogUsuariosPipeline - INFO -   - LOG_USR.parquet (4.74 MB)
2025-06-06 22:40:32,116 - LogUsuariosPipeline - INFO -   - LOG_USR_DEDUPLICATED.parquet (4.61 MB)
2025-06-06 22:40:32,116 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-0231FCONFIANZA-********.csv (0.00 MB)
2025-06-06 22:40:32,116 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-BNACION-********.csv (0.07 MB)
2025-06-06 22:40:32,116 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-CCUSCO-********.csv (0.00 MB)
2025-06-06 22:40:32,116 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-CRANDES-********.csv (0.01 MB)
2025-06-06 22:40:32,116 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-FCOMPARTAMOS-********.csv (33.63 MB)
2025-06-06 22:40:32,116 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-FCONFIANZA-********.csv (0.00 MB)
2025-06-06 22:40:32,116 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-NULL-********.csv (0.00 MB)
2025-06-06 22:49:26,801 - LogUsuariosPipeline - INFO - Configurando credenciales S3...
2025-06-06 22:49:26,815 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-06 22:49:26,865 - LogUsuariosPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-06 22:49:26,866 - LogUsuariosPipeline - INFO - === INICIANDO PIPELINE LOG_USUARIOS MODERNIZADO ===
2025-06-06 22:49:26,866 - LogUsuariosPipeline - INFO - Fecha de procesamiento: 2025-06-05 (carpeta: ********)
2025-06-06 22:49:26,866 - LogUsuariosPipeline - INFO - USER-MODIFY: INICIANDO
2025-06-06 22:49:26,866 - LogUsuariosPipeline - INFO - Extrayendo USER_ACCOUNT_HISTORY para fecha: 2025-06-05
2025-06-06 22:49:27,021 - LogUsuariosPipeline - INFO - USER_ACCOUNT_HISTORY extraído: 4 registros -> TEMP_LOGS_USUARIOS/********/USER_ACCOUNT_HISTORY.parquet
2025-06-06 22:49:27,021 - LogUsuariosPipeline - INFO - USER-MODIFY: OK
2025-06-06 22:49:27,021 - LogUsuariosPipeline - INFO - PRE-LOG-USR: INICIANDO
2025-06-06 22:49:27,021 - LogUsuariosPipeline - INFO - Ejecutando SP_PRE_LOG_USR CORREGIDO (incluye USER_IDs múltiples) para fecha: 2025-06-05
2025-06-06 22:49:34,533 - LogUsuariosPipeline - INFO - SP_PRE_LOG_USR completado: 1581374 registros -> TEMP_LOGS_USUARIOS/********/USER_DATA_TRX.parquet
2025-06-06 22:49:34,534 - LogUsuariosPipeline - INFO - Ejecutando SP_USER_MODIFICATION para fecha: 2025-06-05
2025-06-06 22:49:37,932 - LogUsuariosPipeline - INFO - SP_USER_MODIFICATION completado: 7438 registros -> TEMP_LOGS_USUARIOS/********/USER_MODIFICATION_DAY.parquet
2025-06-06 22:49:37,933 - LogUsuariosPipeline - INFO - Ejecutando SP_USER_AUTH_DAY para fecha: 2025-06-05
2025-06-06 22:49:38,646 - LogUsuariosPipeline - INFO - SP_USER_AUTH_DAY completado: 5804 registros -> TEMP_LOGS_USUARIOS/********/USER_AUTH_CHANGE_HISTORY.parquet
2025-06-06 22:49:38,646 - LogUsuariosPipeline - INFO - PRE-LOG-USR: OK
2025-06-06 22:49:38,646 - LogUsuariosPipeline - INFO - LOG-USR: INICIANDO
2025-06-06 22:49:38,646 - LogUsuariosPipeline - INFO - Ejecutando SP_LOG_USR EXACTO (igual a Oracle) para fecha: 2025-06-05
2025-06-06 22:49:40,654 - LogUsuariosPipeline - INFO - SP_LOG_USR EXACTO completado: 21374 registros -> output/********/LOG_USR.parquet
2025-06-06 22:49:40,655 - LogUsuariosPipeline - INFO - LOG-USR: OK
2025-06-06 22:49:40,655 - LogUsuariosPipeline - INFO - EXPORT-CSV: INICIANDO
2025-06-06 22:49:40,655 - LogUsuariosPipeline - INFO - Iniciando exportación CSV con conversiones de perfiles para fecha: 2025-06-05
2025-06-06 22:49:40,655 - LogUsuariosPipeline - INFO - Aplicando SOLO deduplicación como Oracle original (SIN conversión de tipos)
2025-06-06 22:49:40,893 - LogUsuariosPipeline - INFO - Deduplicación completada (manteniendo REQUESTTYPE originales):
2025-06-06 22:49:40,893 - LogUsuariosPipeline - INFO -   - Registros originales: 21,374
2025-06-06 22:49:40,893 - LogUsuariosPipeline - INFO -   - Registros después de deduplicación: 17,305
2025-06-06 22:49:40,893 - LogUsuariosPipeline - INFO -   - Duplicados eliminados: 4,069
2025-06-06 22:49:40,895 - LogUsuariosPipeline - INFO - Tipos de transacción mantenidos:
2025-06-06 22:49:40,895 - LogUsuariosPipeline - INFO -   - User Modification: 7,193 registros
2025-06-06 22:49:40,895 - LogUsuariosPipeline - INFO -   - CHANGE_AUTH_FACTOR: 5,677 registros
2025-06-06 22:49:40,895 - LogUsuariosPipeline - INFO -   - AfiliaUser: 1,877 registros
2025-06-06 22:49:40,895 - LogUsuariosPipeline - INFO -   - ActivateUser: 1,877 registros
2025-06-06 22:49:40,895 - LogUsuariosPipeline - INFO -   - ClosedUserAccount: 218 registros
2025-06-06 22:49:40,895 - LogUsuariosPipeline - INFO -   - ClosedAccount: 218 registros
2025-06-06 22:49:40,895 - LogUsuariosPipeline - INFO -   - Delete User: 218 registros
2025-06-06 22:49:40,895 - LogUsuariosPipeline - INFO -   - Lock Wallet: 18 registros
2025-06-06 22:49:40,895 - LogUsuariosPipeline - INFO -   - Resume User: 7 registros
2025-06-06 22:49:40,895 - LogUsuariosPipeline - INFO -   - Suspend User: 2 registros
2025-06-06 22:49:40,895 - LogUsuariosPipeline - INFO - Aplicando conversiones de perfiles al archivo: output/********/LOG_USR_DEDUPLICATED.parquet
2025-06-06 22:49:40,895 - LogUsuariosPipeline - INFO - Cargando tabla de conversión de perfiles...
2025-06-06 22:49:40,898 - LogUsuariosPipeline - INFO - Tabla conv_perfil.csv cargada: 796 registros
2025-06-06 22:49:40,902 - LogUsuariosPipeline - INFO - Tabla de conversión de perfiles cargada exitosamente en DuckDB
2025-06-06 22:49:40,903 - LogUsuariosPipeline - ERROR - Error aplicando conversiones al archivo: Binder Error: Values list "cp_a" does not have a column named "PERFIL_CODIGO"

LINE 8: ...            LEFT JOIN conv_perfil_table cp_a ON l.perfilA = cp_a.PERFIL_CODIGO
                                                                       ^
2025-06-06 22:49:40,904 - LogUsuariosPipeline - INFO - Omitiendo CSV principal redundante - usando solo archivos Parquet y CSVs segmentados
2025-06-06 22:49:40,904 - LogUsuariosPipeline - INFO - Iniciando exportación segmentada por BankDomain
2025-06-06 22:49:40,904 - LogUsuariosPipeline - INFO - Procesando TODOS los bancos del sistema: ['0231FCONFIANZA', 'BNACION', 'CCUSCO', 'CRANDES', 'FCOMPARTAMOS', 'FCONFIANZA', None]
2025-06-06 22:49:40,909 - LogUsuariosPipeline - INFO - Exportado dominio 0231FCONFIANZA: 0 registros (archivo vacío SIN headers) -> output/********/csv_exports/by_bank/LOG-USUARIOS-0231FCONFIANZA-********.csv
2025-06-06 22:49:40,928 - LogUsuariosPipeline - INFO - Exportado dominio BNACION: 87 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-BNACION-********.csv
2025-06-06 22:49:40,934 - LogUsuariosPipeline - INFO - Exportado dominio CCUSCO: 2 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-CCUSCO-********.csv
2025-06-06 22:49:40,951 - LogUsuariosPipeline - INFO - Exportado dominio CRANDES: 10 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-CRANDES-********.csv
2025-06-06 22:49:41,075 - LogUsuariosPipeline - INFO - Exportado dominio FCOMPARTAMOS: 21257 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-FCOMPARTAMOS-********.csv
2025-06-06 22:49:41,081 - LogUsuariosPipeline - INFO - Exportado dominio FCONFIANZA: 0 registros (archivo vacío SIN headers) -> output/********/csv_exports/by_bank/LOG-USUARIOS-FCONFIANZA-********.csv
2025-06-06 22:49:41,111 - LogUsuariosPipeline - INFO - Exportado dominio NULL: 18 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-NULL-********.csv
2025-06-06 22:49:41,111 - LogUsuariosPipeline - INFO - Exportación segmentada completada: 7 archivos generados
2025-06-06 22:49:41,111 - LogUsuariosPipeline - INFO - Estructura EXACTA como flujo original: solo by_bank (sin by_profile)
2025-06-06 22:49:41,111 - LogUsuariosPipeline - INFO - 🔄 APLICANDO PROCESAMIENTO EXACTO como procesar.py...
2025-06-06 22:49:41,114 - LogUsuariosPipeline - INFO - 🔄 INICIANDO PROCESAMIENTO EXACTO como procesar.py
2025-06-06 22:49:41,114 - LogUsuariosPipeline - INFO - 📁 Input: output/********/LOG_USR.parquet
2025-06-06 22:49:41,114 - LogUsuariosPipeline - INFO - 📅 Fecha: 2025-06-05
2025-06-06 22:49:41,114 - LogUsuariosPipeline - INFO - 📁 Output: output/********/csv_exports/csv_final_procesado
2025-06-06 22:49:41,114 - LogUsuariosPipeline - INFO - 📊 Cargando datos desde LOG_USR.parquet...
2025-06-06 22:49:41,182 - LogUsuariosPipeline - INFO - 📊 Registros cargados: 21,374
2025-06-06 22:49:41,182 - LogUsuariosPipeline - INFO - 🔄 Procesando grupos por USERHISTID...
2025-06-06 22:49:42,662 - LogUsuariosPipeline - INFO - 📊 Convirtiendo a DataFrame...
2025-06-06 22:49:42,697 - LogUsuariosPipeline - INFO - 📊 Registros después del procesamiento inicial: 13,963
2025-06-06 22:49:42,697 - LogUsuariosPipeline - INFO - 🔍 Aplicando filtro de valores válidos...
2025-06-06 22:49:42,700 - LogUsuariosPipeline - INFO - 📊 Registros después del filtro: 13,963
2025-06-06 22:49:42,700 - LogUsuariosPipeline - INFO - 🔄 Agrupando y procesando JSON...
2025-06-06 22:49:44,852 - LogUsuariosPipeline - INFO - 📊 Registros después de eliminar duplicados: 9,894
2025-06-06 22:49:44,860 - LogUsuariosPipeline - INFO - 🔄 Aplicando asignaciones a columnas...
2025-06-06 22:49:45,543 - LogUsuariosPipeline - INFO - 📊 Cargando tabla de conversión de perfiles...
2025-06-06 22:49:45,545 - LogUsuariosPipeline - INFO - 📊 Tabla de conversión cargada: 796 registros
2025-06-06 22:49:45,645 - LogUsuariosPipeline - INFO - 🔄 Procesando tipos de transacciones válidas...
2025-06-06 22:49:45,666 - LogUsuariosPipeline - INFO - 📊 Registros después de concatenar: 9,894
2025-06-06 22:49:45,717 - LogUsuariosPipeline - INFO - 📊 Registros finales para exportar: 9,894
2025-06-06 22:49:45,717 - LogUsuariosPipeline - INFO - 📁 Generando archivos CSV por BankDomain...
2025-06-06 22:49:45,794 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-FCOMPARTAMOS-********224945.csv (9,833 registros)
2025-06-06 22:49:45,796 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-BNACION-********224945.csv (38 registros)
2025-06-06 22:49:45,798 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-CRANDES-********224945.csv (4 registros)
2025-06-06 22:49:45,799 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-CCUSCO-********224945.csv (1 registros)
2025-06-06 22:49:45,799 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-None-********224945.csv (0 registros)
2025-06-06 22:49:45,800 - LogUsuariosPipeline - INFO - ✅ Procesamiento completado: 5 archivos generados
2025-06-06 22:49:45,800 - LogUsuariosPipeline - INFO - 📊 Total registros procesados: 9,894
2025-06-06 22:49:45,815 - LogUsuariosPipeline - INFO - ✅ Procesamiento exacto completado: 5 archivos
2025-06-06 22:49:45,815 - LogUsuariosPipeline - INFO -   📁 LOGUSR-FCOMPARTAMOS-********224945.csv
2025-06-06 22:49:45,815 - LogUsuariosPipeline - INFO -   📁 LOGUSR-BNACION-********224945.csv
2025-06-06 22:49:45,815 - LogUsuariosPipeline - INFO -   📁 LOGUSR-CRANDES-********224945.csv
2025-06-06 22:49:45,816 - LogUsuariosPipeline - INFO -   📁 LOGUSR-CCUSCO-********224945.csv
2025-06-06 22:49:45,816 - LogUsuariosPipeline - INFO -   📁 LOGUSR-None-********224945.csv
2025-06-06 22:49:45,816 - LogUsuariosPipeline - INFO - Archivo temporal eliminado: TEMP_LOGS_USUARIOS/********/WALLET_OLD.parquet
2025-06-06 22:49:45,816 - LogUsuariosPipeline - INFO - EXPORT-CSV: OK
2025-06-06 22:49:45,816 - LogUsuariosPipeline - INFO - === ARCHIVOS CSV GENERADOS ===
2025-06-06 22:49:45,816 - LogUsuariosPipeline - INFO - Total de archivos CSV segmentados: 12
2025-06-06 22:49:45,816 - LogUsuariosPipeline - INFO - Archivos por BankDomain (7) - EXACTO como flujo original:
2025-06-06 22:49:45,816 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-0231FCONFIANZA-********.csv
2025-06-06 22:49:45,816 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-BNACION-********.csv
2025-06-06 22:49:45,816 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-CCUSCO-********.csv
2025-06-06 22:49:45,816 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-CRANDES-********.csv
2025-06-06 22:49:45,816 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-FCOMPARTAMOS-********.csv
2025-06-06 22:49:45,816 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-FCONFIANZA-********.csv
2025-06-06 22:49:45,816 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-NULL-********.csv
2025-06-06 22:49:45,816 - LogUsuariosPipeline - INFO - Estructura OPTIMIZADA: solo archivos segmentados (datos completos en Parquet)
2025-06-06 22:49:45,816 - LogUsuariosPipeline - INFO - === PIPELINE COMPLETADO EXITOSAMENTE ===
2025-06-06 22:49:45,816 - LogUsuariosPipeline - INFO - 
=== ARCHIVOS GENERADOS ===
2025-06-06 22:49:45,816 - LogUsuariosPipeline - INFO - Archivos temporales en TEMP_LOGS_USUARIOS/********:
2025-06-06 22:49:45,816 - LogUsuariosPipeline - INFO -   - USER_ACCOUNT_HISTORY.parquet (0.00 MB)
2025-06-06 22:49:45,816 - LogUsuariosPipeline - INFO -   - USER_DATA_TRX.parquet (126.93 MB)
2025-06-06 22:49:45,817 - LogUsuariosPipeline - INFO -   - USER_MODIFICATION_DAY.parquet (3.68 MB)
2025-06-06 22:49:45,817 - LogUsuariosPipeline - INFO -   - USER_AUTH_CHANGE_HISTORY.parquet (0.13 MB)
2025-06-06 22:49:45,817 - LogUsuariosPipeline - INFO - Archivos de salida en output/********:
2025-06-06 22:49:45,817 - LogUsuariosPipeline - INFO -   - LOG_USR.parquet (4.74 MB)
2025-06-06 22:49:45,817 - LogUsuariosPipeline - INFO -   - LOG_USR_DEDUPLICATED.parquet (4.61 MB)
2025-06-06 22:49:45,817 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-0231FCONFIANZA-********.csv (0.00 MB)
2025-06-06 22:49:45,817 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-BNACION-********.csv (0.07 MB)
2025-06-06 22:49:45,817 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-CCUSCO-********.csv (0.00 MB)
2025-06-06 22:49:45,817 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-CRANDES-********.csv (0.01 MB)
2025-06-06 22:49:45,817 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-FCOMPARTAMOS-********.csv (33.63 MB)
2025-06-06 22:49:45,817 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-FCONFIANZA-********.csv (0.00 MB)
2025-06-06 22:49:45,817 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-NULL-********.csv (0.00 MB)
2025-06-06 22:49:45,817 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-FCOMPARTAMOS-********224945.csv (2.77 MB)
2025-06-06 22:49:45,817 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-BNACION-********224945.csv (0.01 MB)
2025-06-06 22:49:45,817 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-CRANDES-********224945.csv (0.00 MB)
2025-06-06 22:49:45,817 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-CCUSCO-********224945.csv (0.00 MB)
2025-06-06 22:49:45,817 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-None-********224945.csv (0.00 MB)
2025-06-06 22:49:45,818 - LogUsuariosPipeline - INFO -   - csv_final/LOGUSR-FCOMPARTAMOS-********224850.csv (2.77 MB)
2025-06-06 22:49:45,818 - LogUsuariosPipeline - INFO -   - csv_final/LOGUSR-BNACION-********224850.csv (0.01 MB)
2025-06-06 22:49:45,818 - LogUsuariosPipeline - INFO -   - csv_final/LOGUSR-CRANDES-********224850.csv (0.00 MB)
2025-06-06 22:49:45,818 - LogUsuariosPipeline - INFO -   - csv_final/LOGUSR-CCUSCO-********224850.csv (0.00 MB)
2025-06-06 22:49:45,818 - LogUsuariosPipeline - INFO -   - csv_final/LOGUSR-None-********224850.csv (0.00 MB)
2025-06-06 23:13:03,158 - LogUsuariosPipeline - INFO - Configurando credenciales S3...
2025-06-06 23:13:03,172 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-06 23:13:03,222 - LogUsuariosPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-06 23:13:03,223 - LogUsuariosPipeline - INFO - === INICIANDO PIPELINE LOG_USUARIOS MODERNIZADO ===
2025-06-06 23:13:03,223 - LogUsuariosPipeline - INFO - Fecha de procesamiento: 2025-06-04 (carpeta: ********)
2025-06-06 23:13:03,223 - LogUsuariosPipeline - INFO - USER-MODIFY: INICIANDO
2025-06-06 23:13:03,223 - LogUsuariosPipeline - INFO - Extrayendo USER_ACCOUNT_HISTORY para fecha: 2025-06-04
2025-06-06 23:13:03,363 - LogUsuariosPipeline - INFO - USER_ACCOUNT_HISTORY extraído: 8 registros -> TEMP_LOGS_USUARIOS/********/USER_ACCOUNT_HISTORY.parquet
2025-06-06 23:13:03,363 - LogUsuariosPipeline - INFO - USER-MODIFY: OK
2025-06-06 23:13:03,364 - LogUsuariosPipeline - INFO - PRE-LOG-USR: INICIANDO
2025-06-06 23:13:03,364 - LogUsuariosPipeline - INFO - Ejecutando SP_PRE_LOG_USR CORREGIDO (incluye USER_IDs múltiples) para fecha: 2025-06-04
2025-06-06 23:13:10,895 - LogUsuariosPipeline - INFO - SP_PRE_LOG_USR completado: 1579497 registros -> TEMP_LOGS_USUARIOS/********/USER_DATA_TRX.parquet
2025-06-06 23:13:10,895 - LogUsuariosPipeline - INFO - Ejecutando SP_USER_MODIFICATION para fecha: 2025-06-04
2025-06-06 23:13:14,523 - LogUsuariosPipeline - INFO - SP_USER_MODIFICATION completado: 7118 registros -> TEMP_LOGS_USUARIOS/********/USER_MODIFICATION_DAY.parquet
2025-06-06 23:13:14,523 - LogUsuariosPipeline - INFO - Ejecutando SP_USER_AUTH_DAY para fecha: 2025-06-04
2025-06-06 23:13:15,213 - LogUsuariosPipeline - INFO - SP_USER_AUTH_DAY completado: 5412 registros -> TEMP_LOGS_USUARIOS/********/USER_AUTH_CHANGE_HISTORY.parquet
2025-06-06 23:13:15,214 - LogUsuariosPipeline - INFO - PRE-LOG-USR: OK
2025-06-06 23:13:15,214 - LogUsuariosPipeline - INFO - LOG-USR: INICIANDO
2025-06-06 23:13:15,214 - LogUsuariosPipeline - INFO - Ejecutando SP_LOG_USR EXACTO (igual a Oracle) para fecha: 2025-06-04
2025-06-06 23:13:17,167 - LogUsuariosPipeline - INFO - SP_LOG_USR EXACTO completado: 20226 registros -> output/********/LOG_USR.parquet
2025-06-06 23:13:17,168 - LogUsuariosPipeline - INFO - LOG-USR: OK
2025-06-06 23:13:17,168 - LogUsuariosPipeline - INFO - EXPORT-CSV: INICIANDO
2025-06-06 23:13:17,168 - LogUsuariosPipeline - INFO - Iniciando exportación CSV con conversiones de perfiles para fecha: 2025-06-04
2025-06-06 23:13:17,168 - LogUsuariosPipeline - INFO - Aplicando SOLO deduplicación como Oracle original (SIN conversión de tipos)
2025-06-06 23:13:17,420 - LogUsuariosPipeline - INFO - Deduplicación completada (manteniendo REQUESTTYPE originales):
2025-06-06 23:13:17,420 - LogUsuariosPipeline - INFO -   - Registros originales: 20,226
2025-06-06 23:13:17,420 - LogUsuariosPipeline - INFO -   - Registros después de deduplicación: 16,430
2025-06-06 23:13:17,420 - LogUsuariosPipeline - INFO -   - Duplicados eliminados: 3,796
2025-06-06 23:13:17,422 - LogUsuariosPipeline - INFO - Tipos de transacción mantenidos:
2025-06-06 23:13:17,423 - LogUsuariosPipeline - INFO -   - User Modification: 6,876 registros
2025-06-06 23:13:17,423 - LogUsuariosPipeline - INFO -   - CHANGE_AUTH_FACTOR: 5,299 registros
2025-06-06 23:13:17,423 - LogUsuariosPipeline - INFO -   - ActivateUser: 1,774 registros
2025-06-06 23:13:17,423 - LogUsuariosPipeline - INFO -   - AfiliaUser: 1,774 registros
2025-06-06 23:13:17,423 - LogUsuariosPipeline - INFO -   - ClosedUserAccount: 233 registros
2025-06-06 23:13:17,423 - LogUsuariosPipeline - INFO -   - Delete User: 233 registros
2025-06-06 23:13:17,423 - LogUsuariosPipeline - INFO -   - ClosedAccount: 233 registros
2025-06-06 23:13:17,423 - LogUsuariosPipeline - INFO -   - Suspend User: 3 registros
2025-06-06 23:13:17,423 - LogUsuariosPipeline - INFO -   - Resume User: 3 registros
2025-06-06 23:13:17,423 - LogUsuariosPipeline - INFO -   - Lock Wallet: 1 registros
2025-06-06 23:13:17,423 - LogUsuariosPipeline - INFO - Aplicando conversiones de perfiles al archivo: output/********/LOG_USR_DEDUPLICATED.parquet
2025-06-06 23:13:17,423 - LogUsuariosPipeline - INFO - Cargando tabla de conversión de perfiles...
2025-06-06 23:13:17,425 - LogUsuariosPipeline - INFO - Tabla conv_perfil.csv cargada: 796 registros
2025-06-06 23:13:17,430 - LogUsuariosPipeline - INFO - Tabla de conversión de perfiles cargada exitosamente en DuckDB
2025-06-06 23:13:17,431 - LogUsuariosPipeline - ERROR - Error aplicando conversiones al archivo: Binder Error: Values list "cp_a" does not have a column named "PERFIL_CODIGO"

LINE 8: ...            LEFT JOIN conv_perfil_table cp_a ON l.perfilA = cp_a.PERFIL_CODIGO
                                                                       ^
2025-06-06 23:13:17,432 - LogUsuariosPipeline - INFO - Omitiendo CSV principal redundante - usando solo archivos Parquet y CSVs segmentados
2025-06-06 23:13:17,432 - LogUsuariosPipeline - INFO - Iniciando exportación segmentada por BankDomain
2025-06-06 23:13:17,432 - LogUsuariosPipeline - INFO - Procesando TODOS los bancos del sistema: ['0231FCONFIANZA', 'BNACION', 'CCUSCO', 'CRANDES', 'FCOMPARTAMOS', 'FCONFIANZA', None]
2025-06-06 23:13:17,440 - LogUsuariosPipeline - INFO - Exportado dominio 0231FCONFIANZA: 4 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-0231FCONFIANZA-********.csv
2025-06-06 23:13:17,465 - LogUsuariosPipeline - INFO - Exportado dominio BNACION: 67 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-BNACION-********.csv
2025-06-06 23:13:17,482 - LogUsuariosPipeline - INFO - Exportado dominio CCUSCO: 4 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-CCUSCO-********.csv
2025-06-06 23:13:17,489 - LogUsuariosPipeline - INFO - Exportado dominio CRANDES: 0 registros (archivo vacío SIN headers) -> output/********/csv_exports/by_bank/LOG-USUARIOS-CRANDES-********.csv
2025-06-06 23:13:17,598 - LogUsuariosPipeline - INFO - Exportado dominio FCOMPARTAMOS: 20151 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-FCOMPARTAMOS-********.csv
2025-06-06 23:13:17,603 - LogUsuariosPipeline - INFO - Exportado dominio FCONFIANZA: 0 registros (archivo vacío SIN headers) -> output/********/csv_exports/by_bank/LOG-USUARIOS-FCONFIANZA-********.csv
2025-06-06 23:13:17,607 - LogUsuariosPipeline - INFO - Exportado dominio NULL: 0 registros (archivo vacío SIN headers) -> output/********/csv_exports/by_bank/LOG-USUARIOS-NULL-********.csv
2025-06-06 23:13:17,607 - LogUsuariosPipeline - INFO - Exportación segmentada completada: 7 archivos generados
2025-06-06 23:13:17,607 - LogUsuariosPipeline - INFO - Estructura EXACTA como flujo original: solo by_bank (sin by_profile)
2025-06-06 23:13:17,607 - LogUsuariosPipeline - INFO - 🔄 APLICANDO PROCESAMIENTO EXACTO como procesar.py...
2025-06-06 23:13:17,611 - LogUsuariosPipeline - INFO - 🔄 INICIANDO PROCESAMIENTO EXACTO como procesar.py
2025-06-06 23:13:17,611 - LogUsuariosPipeline - INFO - 📁 Input: output/********/LOG_USR.parquet
2025-06-06 23:13:17,611 - LogUsuariosPipeline - INFO - 📅 Fecha: 2025-06-04
2025-06-06 23:13:17,611 - LogUsuariosPipeline - INFO - 📁 Output: output/********/csv_exports/csv_final_procesado
2025-06-06 23:13:17,611 - LogUsuariosPipeline - INFO - 📊 Cargando datos desde LOG_USR.parquet...
2025-06-06 23:13:17,681 - LogUsuariosPipeline - INFO - 📊 Registros cargados: 20,226
2025-06-06 23:13:17,682 - LogUsuariosPipeline - INFO - 🔄 Procesando grupos por USERHISTID...
2025-06-06 23:13:21,063 - LogUsuariosPipeline - INFO - 📊 Convirtiendo a DataFrame...
2025-06-06 23:13:21,193 - LogUsuariosPipeline - INFO - 📊 Registros después del procesamiento inicial: 50,010
2025-06-06 23:13:21,194 - LogUsuariosPipeline - INFO - 🔍 Aplicando filtro de valores válidos...
2025-06-06 23:13:21,202 - LogUsuariosPipeline - INFO - 📊 Registros después del filtro: 18,480
2025-06-06 23:13:21,202 - LogUsuariosPipeline - INFO - 🔄 Agrupando y procesando JSON...
2025-06-06 23:13:24,390 - LogUsuariosPipeline - INFO - 📊 Registros después de eliminar duplicados: 13,305
2025-06-06 23:13:24,401 - LogUsuariosPipeline - INFO - 🔄 Aplicando asignaciones a columnas...
2025-06-06 23:13:25,466 - LogUsuariosPipeline - INFO - 📊 Cargando tabla de conversión de perfiles...
2025-06-06 23:13:25,467 - LogUsuariosPipeline - INFO - 📊 Tabla de conversión cargada: 796 registros
2025-06-06 23:13:25,805 - LogUsuariosPipeline - INFO - 🔄 Aplicando función crítica de expansión de registros...
2025-06-06 23:13:25,805 - LogUsuariosPipeline - INFO - 📊 Registros antes de expansión: 13,305
2025-06-06 23:13:26,392 - LogUsuariosPipeline - INFO - 📊 Registros después de expansión: 13,321
2025-06-06 23:13:26,393 - LogUsuariosPipeline - INFO - ✅ Función crítica de expansión aplicada exitosamente
2025-06-06 23:13:26,393 - LogUsuariosPipeline - INFO - 🔄 Aplicando mapeo de columnas...
2025-06-06 23:13:26,395 - LogUsuariosPipeline - INFO - 🔄 Procesando tipos de transacciones válidas...
2025-06-06 23:13:26,440 - LogUsuariosPipeline - INFO - 📊 Registros después de concatenar: 13,321
2025-06-06 23:13:26,491 - LogUsuariosPipeline - ERROR - ❌ Error en finalización del procesamiento: "['PerfilB'] not in index"
2025-06-06 23:13:26,491 - LogUsuariosPipeline - ERROR - ❌ Error en continuación del procesamiento: "['PerfilB'] not in index"
2025-06-06 23:13:26,491 - LogUsuariosPipeline - ERROR - ❌ Error en procesamiento: "['PerfilB'] not in index"
2025-06-06 23:13:26,491 - LogUsuariosPipeline - ERROR - ❌ Error en procesamiento exacto: "['PerfilB'] not in index"
2025-06-06 23:13:26,491 - LogUsuariosPipeline - INFO - ⚠️ Continuando con archivos estándar...
2025-06-06 23:13:26,546 - LogUsuariosPipeline - INFO - Archivo temporal eliminado: TEMP_LOGS_USUARIOS/********/WALLET_OLD.parquet
2025-06-06 23:13:26,546 - LogUsuariosPipeline - INFO - EXPORT-CSV: OK
2025-06-06 23:13:26,547 - LogUsuariosPipeline - INFO - === ARCHIVOS CSV GENERADOS ===
2025-06-06 23:13:26,547 - LogUsuariosPipeline - INFO - Total de archivos CSV segmentados: 7
2025-06-06 23:13:26,547 - LogUsuariosPipeline - INFO - Archivos por BankDomain (7) - EXACTO como flujo original:
2025-06-06 23:13:26,547 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-0231FCONFIANZA-********.csv
2025-06-06 23:13:26,547 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-BNACION-********.csv
2025-06-06 23:13:26,547 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-CCUSCO-********.csv
2025-06-06 23:13:26,547 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-CRANDES-********.csv
2025-06-06 23:13:26,547 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-FCOMPARTAMOS-********.csv
2025-06-06 23:13:26,547 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-FCONFIANZA-********.csv
2025-06-06 23:13:26,547 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-NULL-********.csv
2025-06-06 23:13:26,547 - LogUsuariosPipeline - INFO - Estructura OPTIMIZADA: solo archivos segmentados (datos completos en Parquet)
2025-06-06 23:13:26,547 - LogUsuariosPipeline - INFO - === PIPELINE COMPLETADO EXITOSAMENTE ===
2025-06-06 23:13:26,547 - LogUsuariosPipeline - INFO - 
=== ARCHIVOS GENERADOS ===
2025-06-06 23:13:26,547 - LogUsuariosPipeline - INFO - Archivos temporales en TEMP_LOGS_USUARIOS/********:
2025-06-06 23:13:26,547 - LogUsuariosPipeline - INFO -   - USER_ACCOUNT_HISTORY.parquet (0.00 MB)
2025-06-06 23:13:26,547 - LogUsuariosPipeline - INFO -   - USER_DATA_TRX.parquet (126.71 MB)
2025-06-06 23:13:26,547 - LogUsuariosPipeline - INFO -   - USER_MODIFICATION_DAY.parquet (3.52 MB)
2025-06-06 23:13:26,547 - LogUsuariosPipeline - INFO -   - USER_AUTH_CHANGE_HISTORY.parquet (0.12 MB)
2025-06-06 23:13:26,547 - LogUsuariosPipeline - INFO - Archivos de salida en output/********:
2025-06-06 23:13:26,548 - LogUsuariosPipeline - INFO -   - LOG_USR.parquet (4.57 MB)
2025-06-06 23:13:26,548 - LogUsuariosPipeline - INFO -   - LOG_USR_DEDUPLICATED.parquet (4.41 MB)
2025-06-06 23:13:26,548 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-0231FCONFIANZA-********.csv (0.00 MB)
2025-06-06 23:13:26,548 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-BNACION-********.csv (0.04 MB)
2025-06-06 23:13:26,548 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-CCUSCO-********.csv (0.01 MB)
2025-06-06 23:13:26,548 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-CRANDES-********.csv (0.00 MB)
2025-06-06 23:13:26,548 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-FCOMPARTAMOS-********.csv (32.23 MB)
2025-06-06 23:13:26,548 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-FCONFIANZA-********.csv (0.00 MB)
2025-06-06 23:13:26,548 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-NULL-********.csv (0.00 MB)
2025-06-06 23:30:21,339 - LogUsuariosPipeline - INFO - Configurando credenciales S3...
2025-06-06 23:30:21,352 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-06 23:30:21,403 - LogUsuariosPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-06 23:30:21,404 - LogUsuariosPipeline - INFO - === INICIANDO PIPELINE LOG_USUARIOS MODERNIZADO ===
2025-06-06 23:30:21,404 - LogUsuariosPipeline - INFO - Fecha de procesamiento: 2025-06-06 (carpeta: ********)
2025-06-06 23:30:21,404 - LogUsuariosPipeline - INFO - USER-MODIFY: INICIANDO
2025-06-06 23:30:21,404 - LogUsuariosPipeline - INFO - Extrayendo USER_ACCOUNT_HISTORY para fecha: 2025-06-06
2025-06-06 23:30:21,568 - LogUsuariosPipeline - INFO - USER_ACCOUNT_HISTORY extraído: 0 registros -> TEMP_LOGS_USUARIOS/********/USER_ACCOUNT_HISTORY.parquet
2025-06-06 23:30:21,568 - LogUsuariosPipeline - INFO - USER-MODIFY: OK
2025-06-06 23:30:21,568 - LogUsuariosPipeline - INFO - PRE-LOG-USR: INICIANDO
2025-06-06 23:30:21,568 - LogUsuariosPipeline - INFO - Ejecutando SP_PRE_LOG_USR CORREGIDO (incluye USER_IDs múltiples) para fecha: 2025-06-06
2025-06-06 23:30:28,931 - LogUsuariosPipeline - INFO - SP_PRE_LOG_USR completado: 1581435 registros -> TEMP_LOGS_USUARIOS/********/USER_DATA_TRX.parquet
2025-06-06 23:30:28,931 - LogUsuariosPipeline - INFO - Ejecutando SP_USER_MODIFICATION para fecha: 2025-06-06
2025-06-06 23:30:29,112 - LogUsuariosPipeline - INFO - SP_USER_MODIFICATION completado: 0 registros -> TEMP_LOGS_USUARIOS/********/USER_MODIFICATION_DAY.parquet
2025-06-06 23:30:29,112 - LogUsuariosPipeline - INFO - Ejecutando SP_USER_AUTH_DAY para fecha: 2025-06-06
2025-06-06 23:30:29,350 - LogUsuariosPipeline - INFO - SP_USER_AUTH_DAY completado: 0 registros -> TEMP_LOGS_USUARIOS/********/USER_AUTH_CHANGE_HISTORY.parquet
2025-06-06 23:30:29,350 - LogUsuariosPipeline - INFO - PRE-LOG-USR: OK
2025-06-06 23:30:29,350 - LogUsuariosPipeline - INFO - LOG-USR: INICIANDO
2025-06-06 23:30:29,350 - LogUsuariosPipeline - INFO - Ejecutando SP_LOG_USR EXACTO (igual a Oracle) para fecha: 2025-06-06
2025-06-06 23:30:31,049 - LogUsuariosPipeline - INFO - SP_LOG_USR EXACTO completado: 300 registros -> output/********/LOG_USR.parquet
2025-06-06 23:30:31,050 - LogUsuariosPipeline - INFO - LOG-USR: OK
2025-06-06 23:30:31,050 - LogUsuariosPipeline - INFO - EXPORT-CSV: INICIANDO
2025-06-06 23:30:31,050 - LogUsuariosPipeline - INFO - Iniciando exportación CSV con conversiones de perfiles para fecha: 2025-06-06
2025-06-06 23:30:31,050 - LogUsuariosPipeline - INFO - Aplicando SOLO deduplicación como Oracle original (SIN conversión de tipos)
2025-06-06 23:30:31,067 - LogUsuariosPipeline - INFO - Deduplicación completada (manteniendo REQUESTTYPE originales):
2025-06-06 23:30:31,067 - LogUsuariosPipeline - INFO -   - Registros originales: 300
2025-06-06 23:30:31,067 - LogUsuariosPipeline - INFO -   - Registros después de deduplicación: 300
2025-06-06 23:30:31,067 - LogUsuariosPipeline - INFO -   - Duplicados eliminados: 0
2025-06-06 23:30:31,069 - LogUsuariosPipeline - INFO - Tipos de transacción mantenidos:
2025-06-06 23:30:31,069 - LogUsuariosPipeline - INFO -   - ClosedUserAccount: 89 registros
2025-06-06 23:30:31,069 - LogUsuariosPipeline - INFO -   - ClosedAccount: 89 registros
2025-06-06 23:30:31,069 - LogUsuariosPipeline - INFO -   - AfiliaUser: 61 registros
2025-06-06 23:30:31,069 - LogUsuariosPipeline - INFO -   - ActivateUser: 61 registros
2025-06-06 23:30:31,069 - LogUsuariosPipeline - INFO - Aplicando conversiones de perfiles al archivo: output/********/LOG_USR_DEDUPLICATED.parquet
2025-06-06 23:30:31,069 - LogUsuariosPipeline - INFO - Cargando tabla de conversión de perfiles...
2025-06-06 23:30:31,072 - LogUsuariosPipeline - INFO - Tabla conv_perfil.csv cargada: 796 registros
2025-06-06 23:30:31,076 - LogUsuariosPipeline - INFO - Tabla de conversión de perfiles cargada exitosamente en DuckDB
2025-06-06 23:30:31,078 - LogUsuariosPipeline - ERROR - Error aplicando conversiones al archivo: Binder Error: Values list "cp_a" does not have a column named "PERFIL_CODIGO"

LINE 8: ...            LEFT JOIN conv_perfil_table cp_a ON l.perfilA = cp_a.PERFIL_CODIGO
                                                                       ^
2025-06-06 23:30:31,078 - LogUsuariosPipeline - INFO - Omitiendo CSV principal redundante - usando solo archivos Parquet y CSVs segmentados
2025-06-06 23:30:31,078 - LogUsuariosPipeline - INFO - Iniciando exportación segmentada por BankDomain
2025-06-06 23:30:31,078 - LogUsuariosPipeline - INFO - Procesando TODOS los bancos del sistema: ['0231FCONFIANZA', 'BNACION', 'CCUSCO', 'CRANDES', 'FCOMPARTAMOS', 'FCONFIANZA', None]
2025-06-06 23:30:31,084 - LogUsuariosPipeline - INFO - Exportado dominio 0231FCONFIANZA: 0 registros (archivo vacío SIN headers) -> output/********/csv_exports/by_bank/LOG-USUARIOS-0231FCONFIANZA-********.csv
2025-06-06 23:30:31,090 - LogUsuariosPipeline - INFO - Exportado dominio BNACION: 0 registros (archivo vacío SIN headers) -> output/********/csv_exports/by_bank/LOG-USUARIOS-BNACION-********.csv
2025-06-06 23:30:31,095 - LogUsuariosPipeline - INFO - Exportado dominio CCUSCO: 0 registros (archivo vacío SIN headers) -> output/********/csv_exports/by_bank/LOG-USUARIOS-CCUSCO-********.csv
2025-06-06 23:30:31,100 - LogUsuariosPipeline - INFO - Exportado dominio CRANDES: 0 registros (archivo vacío SIN headers) -> output/********/csv_exports/by_bank/LOG-USUARIOS-CRANDES-********.csv
2025-06-06 23:30:31,108 - LogUsuariosPipeline - INFO - Exportado dominio FCOMPARTAMOS: 172 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-FCOMPARTAMOS-********.csv
2025-06-06 23:30:31,113 - LogUsuariosPipeline - INFO - Exportado dominio FCONFIANZA: 0 registros (archivo vacío SIN headers) -> output/********/csv_exports/by_bank/LOG-USUARIOS-FCONFIANZA-********.csv
2025-06-06 23:30:31,119 - LogUsuariosPipeline - INFO - Exportado dominio NULL: 128 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-NULL-********.csv
2025-06-06 23:30:31,120 - LogUsuariosPipeline - INFO - Exportación segmentada completada: 7 archivos generados
2025-06-06 23:30:31,120 - LogUsuariosPipeline - INFO - Estructura EXACTA como flujo original: solo by_bank (sin by_profile)
2025-06-06 23:30:31,120 - LogUsuariosPipeline - INFO - 🔄 APLICANDO PROCESAMIENTO EXACTO como procesar.py...
2025-06-06 23:30:31,124 - LogUsuariosPipeline - INFO - 🔄 INICIANDO PROCESAMIENTO EXACTO como procesar.py
2025-06-06 23:30:31,124 - LogUsuariosPipeline - INFO - 📁 Input: output/********/LOG_USR.parquet
2025-06-06 23:30:31,124 - LogUsuariosPipeline - INFO - 📅 Fecha: 2025-06-06
2025-06-06 23:30:31,124 - LogUsuariosPipeline - INFO - 📁 Output: output/********/csv_exports/csv_final_procesado
2025-06-06 23:30:31,124 - LogUsuariosPipeline - INFO - 📊 Cargando datos desde LOG_USR.parquet...
2025-06-06 23:30:31,139 - LogUsuariosPipeline - INFO - 📊 Registros cargados: 300
2025-06-06 23:30:31,139 - LogUsuariosPipeline - INFO - 🔄 Procesando grupos por USERHISTID...
2025-06-06 23:30:31,166 - LogUsuariosPipeline - INFO - 📊 Convirtiendo a DataFrame...
2025-06-06 23:30:31,167 - LogUsuariosPipeline - INFO - 📊 Registros después del procesamiento inicial: 300
2025-06-06 23:30:31,167 - LogUsuariosPipeline - INFO - 🔍 Aplicando filtro de valores válidos...
2025-06-06 23:30:31,168 - LogUsuariosPipeline - INFO - 📊 Registros después del filtro: 300
2025-06-06 23:30:31,168 - LogUsuariosPipeline - INFO - 🥷 Aplicando filtros adicionales de calidad de datos...
2025-06-06 23:30:31,169 - LogUsuariosPipeline - INFO -   🔍 Eliminados 128 registros con BANKDOMAIN inválido
2025-06-06 23:30:31,169 - LogUsuariosPipeline - INFO - 📊 Registros después de filtros de calidad: 172
2025-06-06 23:30:31,169 - LogUsuariosPipeline - INFO - 🔄 Agrupando y procesando JSON...
2025-06-06 23:30:31,207 - LogUsuariosPipeline - INFO - 📊 Registros después de eliminar duplicados: 172
2025-06-06 23:30:31,207 - LogUsuariosPipeline - INFO - 🔄 Aplicando asignaciones a columnas...
2025-06-06 23:30:31,219 - LogUsuariosPipeline - INFO - 📊 Cargando tabla de conversión de perfiles...
2025-06-06 23:30:31,221 - LogUsuariosPipeline - INFO - 📊 Tabla de conversión cargada: 796 registros
2025-06-06 23:30:31,224 - LogUsuariosPipeline - INFO - 🔄 Aplicando función crítica de expansión de registros...
2025-06-06 23:30:31,224 - LogUsuariosPipeline - INFO - 📊 Registros antes de expansión: 172
2025-06-06 23:30:31,230 - LogUsuariosPipeline - INFO - 📊 Registros después de expansión: 172
2025-06-06 23:30:31,230 - LogUsuariosPipeline - INFO - ✅ Función crítica de expansión aplicada exitosamente
2025-06-06 23:30:31,230 - LogUsuariosPipeline - INFO - 🔄 Aplicando mapeo de columnas...
2025-06-06 23:30:31,230 - LogUsuariosPipeline - INFO - 🔄 Procesando tipos de transacciones válidas...
2025-06-06 23:30:31,251 - LogUsuariosPipeline - INFO - 📊 Registros después de concatenar: 172
2025-06-06 23:30:31,254 - LogUsuariosPipeline - INFO - 📊 Registros finales para exportar: 172
2025-06-06 23:30:31,254 - LogUsuariosPipeline - INFO - 📁 Generando archivos CSV por BankDomain...
2025-06-06 23:30:31,256 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-FCOMPARTAMOS-********233031.csv (172 registros)
2025-06-06 23:30:31,256 - LogUsuariosPipeline - INFO - ✅ Procesamiento completado: 1 archivos generados
2025-06-06 23:30:31,256 - LogUsuariosPipeline - INFO - 📊 Total registros procesados: 172
2025-06-06 23:30:31,256 - LogUsuariosPipeline - INFO - ✅ Procesamiento exacto completado: 1 archivos
2025-06-06 23:30:31,257 - LogUsuariosPipeline - INFO -   📁 LOGUSR-FCOMPARTAMOS-********233031.csv
2025-06-06 23:30:31,257 - LogUsuariosPipeline - INFO - Archivo temporal eliminado: TEMP_LOGS_USUARIOS/********/WALLET_OLD.parquet
2025-06-06 23:30:31,257 - LogUsuariosPipeline - INFO - EXPORT-CSV: OK
2025-06-06 23:30:31,257 - LogUsuariosPipeline - INFO - === ARCHIVOS CSV GENERADOS ===
2025-06-06 23:30:31,257 - LogUsuariosPipeline - INFO - Total de archivos CSV segmentados: 8
2025-06-06 23:30:31,257 - LogUsuariosPipeline - INFO - Archivos por BankDomain (7) - EXACTO como flujo original:
2025-06-06 23:30:31,257 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-0231FCONFIANZA-********.csv
2025-06-06 23:30:31,257 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-BNACION-********.csv
2025-06-06 23:30:31,257 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-CCUSCO-********.csv
2025-06-06 23:30:31,257 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-CRANDES-********.csv
2025-06-06 23:30:31,257 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-FCOMPARTAMOS-********.csv
2025-06-06 23:30:31,257 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-FCONFIANZA-********.csv
2025-06-06 23:30:31,257 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-NULL-********.csv
2025-06-06 23:30:31,257 - LogUsuariosPipeline - INFO - Estructura OPTIMIZADA: solo archivos segmentados (datos completos en Parquet)
2025-06-06 23:30:31,257 - LogUsuariosPipeline - INFO - === PIPELINE COMPLETADO EXITOSAMENTE ===
2025-06-06 23:30:31,257 - LogUsuariosPipeline - INFO - 
=== ARCHIVOS GENERADOS ===
2025-06-06 23:30:31,257 - LogUsuariosPipeline - INFO - Archivos temporales en TEMP_LOGS_USUARIOS/********:
2025-06-06 23:30:31,257 - LogUsuariosPipeline - INFO -   - USER_ACCOUNT_HISTORY.parquet (0.00 MB)
2025-06-06 23:30:31,257 - LogUsuariosPipeline - INFO -   - USER_DATA_TRX.parquet (126.94 MB)
2025-06-06 23:30:31,258 - LogUsuariosPipeline - INFO -   - USER_MODIFICATION_DAY.parquet (0.00 MB)
2025-06-06 23:30:31,258 - LogUsuariosPipeline - INFO -   - USER_AUTH_CHANGE_HISTORY.parquet (0.00 MB)
2025-06-06 23:30:31,258 - LogUsuariosPipeline - INFO - Archivos de salida en output/********:
2025-06-06 23:30:31,258 - LogUsuariosPipeline - INFO -   - LOG_USR.parquet (0.07 MB)
2025-06-06 23:30:31,258 - LogUsuariosPipeline - INFO -   - LOG_USR_DEDUPLICATED.parquet (0.02 MB)
2025-06-06 23:30:31,258 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-0231FCONFIANZA-********.csv (0.00 MB)
2025-06-06 23:30:31,258 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-BNACION-********.csv (0.00 MB)
2025-06-06 23:30:31,258 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-CCUSCO-********.csv (0.00 MB)
2025-06-06 23:30:31,258 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-CRANDES-********.csv (0.00 MB)
2025-06-06 23:30:31,258 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-FCOMPARTAMOS-********.csv (0.05 MB)
2025-06-06 23:30:31,258 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-FCONFIANZA-********.csv (0.00 MB)
2025-06-06 23:30:31,258 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-NULL-********.csv (0.03 MB)
2025-06-06 23:30:31,258 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-FCOMPARTAMOS-********233031.csv (0.02 MB)
2025-06-06 23:36:45,737 - LogUsuariosPipeline - INFO - Configurando credenciales S3...
2025-06-06 23:36:45,750 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-06 23:36:45,809 - LogUsuariosPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-06 23:36:45,810 - LogUsuariosPipeline - INFO - === INICIANDO PIPELINE LOG_USUARIOS MODERNIZADO ===
2025-06-06 23:36:45,810 - LogUsuariosPipeline - INFO - Fecha de procesamiento: 2025-06-05 (carpeta: ********)
2025-06-06 23:36:45,810 - LogUsuariosPipeline - INFO - USER-MODIFY: INICIANDO
2025-06-06 23:36:45,810 - LogUsuariosPipeline - INFO - Extrayendo USER_ACCOUNT_HISTORY para fecha: 2025-06-05
2025-06-06 23:36:45,956 - LogUsuariosPipeline - INFO - USER_ACCOUNT_HISTORY extraído: 4 registros -> TEMP_LOGS_USUARIOS/********/USER_ACCOUNT_HISTORY.parquet
2025-06-06 23:36:45,956 - LogUsuariosPipeline - INFO - USER-MODIFY: OK
2025-06-06 23:36:45,956 - LogUsuariosPipeline - INFO - PRE-LOG-USR: INICIANDO
2025-06-06 23:36:45,956 - LogUsuariosPipeline - INFO - Ejecutando SP_PRE_LOG_USR CORREGIDO (incluye USER_IDs múltiples) para fecha: 2025-06-05
2025-06-06 23:36:53,720 - LogUsuariosPipeline - INFO - SP_PRE_LOG_USR completado: 1581374 registros -> TEMP_LOGS_USUARIOS/********/USER_DATA_TRX.parquet
2025-06-06 23:36:53,720 - LogUsuariosPipeline - INFO - Ejecutando SP_USER_MODIFICATION para fecha: 2025-06-05
2025-06-06 23:36:57,047 - LogUsuariosPipeline - INFO - SP_USER_MODIFICATION completado: 7438 registros -> TEMP_LOGS_USUARIOS/********/USER_MODIFICATION_DAY.parquet
2025-06-06 23:36:57,047 - LogUsuariosPipeline - INFO - Ejecutando SP_USER_AUTH_DAY para fecha: 2025-06-05
2025-06-06 23:36:57,791 - LogUsuariosPipeline - INFO - SP_USER_AUTH_DAY completado: 5804 registros -> TEMP_LOGS_USUARIOS/********/USER_AUTH_CHANGE_HISTORY.parquet
2025-06-06 23:36:57,791 - LogUsuariosPipeline - INFO - PRE-LOG-USR: OK
2025-06-06 23:36:57,791 - LogUsuariosPipeline - INFO - LOG-USR: INICIANDO
2025-06-06 23:36:57,791 - LogUsuariosPipeline - INFO - Ejecutando SP_LOG_USR EXACTO (igual a Oracle) para fecha: 2025-06-05
2025-06-06 23:36:59,831 - LogUsuariosPipeline - INFO - SP_LOG_USR EXACTO completado: 21374 registros -> output/********/LOG_USR.parquet
2025-06-06 23:36:59,832 - LogUsuariosPipeline - INFO - LOG-USR: OK
2025-06-06 23:36:59,832 - LogUsuariosPipeline - INFO - EXPORT-CSV: INICIANDO
2025-06-06 23:36:59,832 - LogUsuariosPipeline - INFO - Iniciando exportación CSV con conversiones de perfiles para fecha: 2025-06-05
2025-06-06 23:36:59,832 - LogUsuariosPipeline - INFO - Aplicando SOLO deduplicación como Oracle original (SIN conversión de tipos)
2025-06-06 23:37:00,122 - LogUsuariosPipeline - INFO - Deduplicación completada (manteniendo REQUESTTYPE originales):
2025-06-06 23:37:00,122 - LogUsuariosPipeline - INFO -   - Registros originales: 21,374
2025-06-06 23:37:00,122 - LogUsuariosPipeline - INFO -   - Registros después de deduplicación: 17,305
2025-06-06 23:37:00,122 - LogUsuariosPipeline - INFO -   - Duplicados eliminados: 4,069
2025-06-06 23:37:00,125 - LogUsuariosPipeline - INFO - Tipos de transacción mantenidos:
2025-06-06 23:37:00,125 - LogUsuariosPipeline - INFO -   - User Modification: 7,193 registros
2025-06-06 23:37:00,125 - LogUsuariosPipeline - INFO -   - CHANGE_AUTH_FACTOR: 5,677 registros
2025-06-06 23:37:00,125 - LogUsuariosPipeline - INFO -   - AfiliaUser: 1,877 registros
2025-06-06 23:37:00,125 - LogUsuariosPipeline - INFO -   - ActivateUser: 1,877 registros
2025-06-06 23:37:00,125 - LogUsuariosPipeline - INFO -   - ClosedAccount: 218 registros
2025-06-06 23:37:00,125 - LogUsuariosPipeline - INFO -   - ClosedUserAccount: 218 registros
2025-06-06 23:37:00,125 - LogUsuariosPipeline - INFO -   - Delete User: 218 registros
2025-06-06 23:37:00,125 - LogUsuariosPipeline - INFO -   - Lock Wallet: 18 registros
2025-06-06 23:37:00,125 - LogUsuariosPipeline - INFO -   - Resume User: 7 registros
2025-06-06 23:37:00,125 - LogUsuariosPipeline - INFO -   - Suspend User: 2 registros
2025-06-06 23:37:00,125 - LogUsuariosPipeline - INFO - Aplicando conversiones de perfiles al archivo: output/********/LOG_USR_DEDUPLICATED.parquet
2025-06-06 23:37:00,125 - LogUsuariosPipeline - INFO - Cargando tabla de conversión de perfiles...
2025-06-06 23:37:00,128 - LogUsuariosPipeline - INFO - Tabla conv_perfil.csv cargada: 796 registros
2025-06-06 23:37:00,133 - LogUsuariosPipeline - INFO - Tabla de conversión de perfiles cargada exitosamente en DuckDB
2025-06-06 23:37:00,135 - LogUsuariosPipeline - ERROR - Error aplicando conversiones al archivo: Binder Error: Values list "cp_a" does not have a column named "PERFIL_CODIGO"

LINE 8: ...            LEFT JOIN conv_perfil_table cp_a ON l.perfilA = cp_a.PERFIL_CODIGO
                                                                       ^
2025-06-06 23:37:00,135 - LogUsuariosPipeline - INFO - Omitiendo CSV principal redundante - usando solo archivos Parquet y CSVs segmentados
2025-06-06 23:37:00,135 - LogUsuariosPipeline - INFO - Iniciando exportación segmentada por BankDomain
2025-06-06 23:37:00,135 - LogUsuariosPipeline - INFO - Procesando TODOS los bancos del sistema: ['0231FCONFIANZA', 'BNACION', 'CCUSCO', 'CRANDES', 'FCOMPARTAMOS', 'FCONFIANZA', None]
2025-06-06 23:37:00,139 - LogUsuariosPipeline - INFO - Exportado dominio 0231FCONFIANZA: 0 registros (archivo vacío SIN headers) -> output/********/csv_exports/by_bank/LOG-USUARIOS-0231FCONFIANZA-********.csv
2025-06-06 23:37:00,160 - LogUsuariosPipeline - INFO - Exportado dominio BNACION: 87 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-BNACION-********.csv
2025-06-06 23:37:00,167 - LogUsuariosPipeline - INFO - Exportado dominio CCUSCO: 2 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-CCUSCO-********.csv
2025-06-06 23:37:00,186 - LogUsuariosPipeline - INFO - Exportado dominio CRANDES: 10 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-CRANDES-********.csv
2025-06-06 23:37:00,328 - LogUsuariosPipeline - INFO - Exportado dominio FCOMPARTAMOS: 21257 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-FCOMPARTAMOS-********.csv
2025-06-06 23:37:00,333 - LogUsuariosPipeline - INFO - Exportado dominio FCONFIANZA: 0 registros (archivo vacío SIN headers) -> output/********/csv_exports/by_bank/LOG-USUARIOS-FCONFIANZA-********.csv
2025-06-06 23:37:00,368 - LogUsuariosPipeline - INFO - Exportado dominio NULL: 18 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-NULL-********.csv
2025-06-06 23:37:00,368 - LogUsuariosPipeline - INFO - Exportación segmentada completada: 7 archivos generados
2025-06-06 23:37:00,369 - LogUsuariosPipeline - INFO - Estructura EXACTA como flujo original: solo by_bank (sin by_profile)
2025-06-06 23:37:00,369 - LogUsuariosPipeline - INFO - 🔄 APLICANDO PROCESAMIENTO EXACTO como procesar.py...
2025-06-06 23:37:00,369 - LogUsuariosPipeline - INFO - 🔄 INICIANDO PROCESAMIENTO EXACTO como procesar.py
2025-06-06 23:37:00,369 - LogUsuariosPipeline - INFO - 📁 Input: output/********/LOG_USR.parquet
2025-06-06 23:37:00,369 - LogUsuariosPipeline - INFO - 📅 Fecha: 2025-06-05
2025-06-06 23:37:00,369 - LogUsuariosPipeline - INFO - 📁 Output: output/********/csv_exports/csv_final_procesado
2025-06-06 23:37:00,369 - LogUsuariosPipeline - INFO - 📊 Cargando datos desde LOG_USR.parquet...
2025-06-06 23:37:00,446 - LogUsuariosPipeline - INFO - 📊 Registros cargados: 21,374
2025-06-06 23:37:00,447 - LogUsuariosPipeline - INFO - 🔄 Procesando grupos por USERHISTID...
2025-06-06 23:37:04,126 - LogUsuariosPipeline - INFO - 📊 Convirtiendo a DataFrame...
2025-06-06 23:37:04,271 - LogUsuariosPipeline - INFO - 📊 Registros después del procesamiento inicial: 51,958
2025-06-06 23:37:04,272 - LogUsuariosPipeline - INFO - 🔍 Aplicando filtro de valores válidos...
2025-06-06 23:37:04,281 - LogUsuariosPipeline - INFO - 📊 Registros después del filtro: 19,710
2025-06-06 23:37:04,281 - LogUsuariosPipeline - INFO - 🥷 Aplicando filtros adicionales de calidad de datos...
2025-06-06 23:37:04,291 - LogUsuariosPipeline - INFO -   🔍 Eliminados 18 registros con BANKDOMAIN inválido
2025-06-06 23:37:04,304 - LogUsuariosPipeline - INFO - 📊 Registros después de filtros de calidad: 19,692
2025-06-06 23:37:04,304 - LogUsuariosPipeline - INFO - 🔄 Agrupando y procesando JSON...
2025-06-06 23:37:07,715 - LogUsuariosPipeline - INFO - 📊 Registros después de eliminar duplicados: 14,144
2025-06-06 23:37:07,728 - LogUsuariosPipeline - INFO - 🔄 Aplicando asignaciones a columnas...
2025-06-06 23:37:08,857 - LogUsuariosPipeline - INFO - 📊 Cargando tabla de conversión de perfiles...
2025-06-06 23:37:08,859 - LogUsuariosPipeline - INFO - 📊 Tabla de conversión cargada: 796 registros
2025-06-06 23:37:09,257 - LogUsuariosPipeline - INFO - 🔄 Aplicando función crítica de expansión de registros...
2025-06-06 23:37:09,258 - LogUsuariosPipeline - INFO - 📊 Registros antes de expansión: 14,144
2025-06-06 23:37:09,969 - LogUsuariosPipeline - INFO - 📊 Registros después de expansión: 14,160
2025-06-06 23:37:09,970 - LogUsuariosPipeline - INFO - ✅ Función crítica de expansión aplicada exitosamente
2025-06-06 23:37:09,970 - LogUsuariosPipeline - INFO - 🔄 Aplicando mapeo de columnas...
2025-06-06 23:37:09,972 - LogUsuariosPipeline - INFO - 🔄 Procesando tipos de transacciones válidas...
2025-06-06 23:37:10,017 - LogUsuariosPipeline - INFO - 📊 Registros después de concatenar: 14,160
2025-06-06 23:37:10,058 - LogUsuariosPipeline - INFO - 📊 Registros finales para exportar: 14,160
2025-06-06 23:37:10,058 - LogUsuariosPipeline - INFO - 📁 Generando archivos CSV por BankDomain...
2025-06-06 23:37:10,098 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-FCOMPARTAMOS-********233710.csv (14,098 registros)
2025-06-06 23:37:10,101 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-BNACION-********233710.csv (55 registros)
2025-06-06 23:37:10,103 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-CRANDES-********233710.csv (6 registros)
2025-06-06 23:37:10,104 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-CCUSCO-********233710.csv (1 registros)
2025-06-06 23:37:10,104 - LogUsuariosPipeline - INFO - ✅ Procesamiento completado: 4 archivos generados
2025-06-06 23:37:10,104 - LogUsuariosPipeline - INFO - 📊 Total registros procesados: 14,160
2025-06-06 23:37:10,162 - LogUsuariosPipeline - INFO - ✅ Procesamiento exacto completado: 4 archivos
2025-06-06 23:37:10,162 - LogUsuariosPipeline - INFO -   📁 LOGUSR-FCOMPARTAMOS-********233710.csv
2025-06-06 23:37:10,162 - LogUsuariosPipeline - INFO -   📁 LOGUSR-BNACION-********233710.csv
2025-06-06 23:37:10,162 - LogUsuariosPipeline - INFO -   📁 LOGUSR-CRANDES-********233710.csv
2025-06-06 23:37:10,162 - LogUsuariosPipeline - INFO -   📁 LOGUSR-CCUSCO-********233710.csv
2025-06-06 23:37:10,162 - LogUsuariosPipeline - INFO - Archivo temporal eliminado: TEMP_LOGS_USUARIOS/********/WALLET_OLD.parquet
2025-06-06 23:37:10,162 - LogUsuariosPipeline - INFO - EXPORT-CSV: OK
2025-06-06 23:37:10,163 - LogUsuariosPipeline - INFO - === ARCHIVOS CSV GENERADOS ===
2025-06-06 23:37:10,163 - LogUsuariosPipeline - INFO - Total de archivos CSV segmentados: 11
2025-06-06 23:37:10,163 - LogUsuariosPipeline - INFO - Archivos por BankDomain (7) - EXACTO como flujo original:
2025-06-06 23:37:10,163 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-0231FCONFIANZA-********.csv
2025-06-06 23:37:10,163 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-BNACION-********.csv
2025-06-06 23:37:10,163 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-CCUSCO-********.csv
2025-06-06 23:37:10,163 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-CRANDES-********.csv
2025-06-06 23:37:10,163 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-FCOMPARTAMOS-********.csv
2025-06-06 23:37:10,163 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-FCONFIANZA-********.csv
2025-06-06 23:37:10,163 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-NULL-********.csv
2025-06-06 23:37:10,163 - LogUsuariosPipeline - INFO - Estructura OPTIMIZADA: solo archivos segmentados (datos completos en Parquet)
2025-06-06 23:37:10,163 - LogUsuariosPipeline - INFO - === PIPELINE COMPLETADO EXITOSAMENTE ===
2025-06-06 23:37:10,163 - LogUsuariosPipeline - INFO - 
=== ARCHIVOS GENERADOS ===
2025-06-06 23:37:10,163 - LogUsuariosPipeline - INFO - Archivos temporales en TEMP_LOGS_USUARIOS/********:
2025-06-06 23:37:10,163 - LogUsuariosPipeline - INFO -   - USER_ACCOUNT_HISTORY.parquet (0.00 MB)
2025-06-06 23:37:10,163 - LogUsuariosPipeline - INFO -   - USER_DATA_TRX.parquet (126.93 MB)
2025-06-06 23:37:10,163 - LogUsuariosPipeline - INFO -   - USER_MODIFICATION_DAY.parquet (3.68 MB)
2025-06-06 23:37:10,163 - LogUsuariosPipeline - INFO -   - USER_AUTH_CHANGE_HISTORY.parquet (0.13 MB)
2025-06-06 23:37:10,163 - LogUsuariosPipeline - INFO - Archivos de salida en output/********:
2025-06-06 23:37:10,163 - LogUsuariosPipeline - INFO -   - LOG_USR.parquet (4.74 MB)
2025-06-06 23:37:10,163 - LogUsuariosPipeline - INFO -   - LOG_USR_DEDUPLICATED.parquet (4.61 MB)
2025-06-06 23:37:10,164 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-0231FCONFIANZA-********.csv (0.00 MB)
2025-06-06 23:37:10,164 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-BNACION-********.csv (0.07 MB)
2025-06-06 23:37:10,164 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-CCUSCO-********.csv (0.00 MB)
2025-06-06 23:37:10,164 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-CRANDES-********.csv (0.01 MB)
2025-06-06 23:37:10,164 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-FCOMPARTAMOS-********.csv (33.63 MB)
2025-06-06 23:37:10,164 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-FCONFIANZA-********.csv (0.00 MB)
2025-06-06 23:37:10,164 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-NULL-********.csv (0.00 MB)
2025-06-06 23:37:10,164 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-FCOMPARTAMOS-********233710.csv (1.51 MB)
2025-06-06 23:37:10,164 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-BNACION-********233710.csv (0.00 MB)
2025-06-06 23:37:10,164 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-CRANDES-********233710.csv (0.00 MB)
2025-06-06 23:37:10,164 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-CCUSCO-********233710.csv (0.00 MB)
2025-06-07 00:24:03,392 - LogUsuariosPipeline - INFO - Configurando credenciales S3...
2025-06-07 00:24:03,405 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-07 00:24:03,454 - LogUsuariosPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-07 00:24:03,455 - LogUsuariosPipeline - INFO - === INICIANDO PIPELINE LOG_USUARIOS MODERNIZADO ===
2025-06-07 00:24:03,455 - LogUsuariosPipeline - INFO - Fecha de procesamiento: 2025-06-05 (carpeta: ********)
2025-06-07 00:24:03,455 - LogUsuariosPipeline - INFO - USER-MODIFY: INICIANDO
2025-06-07 00:24:03,455 - LogUsuariosPipeline - INFO - Extrayendo USER_ACCOUNT_HISTORY para fecha: 2025-06-05
2025-06-07 00:24:03,632 - LogUsuariosPipeline - INFO - USER_ACCOUNT_HISTORY extraído: 4 registros -> TEMP_LOGS_USUARIOS/********/USER_ACCOUNT_HISTORY.parquet
2025-06-07 00:24:03,632 - LogUsuariosPipeline - INFO - USER-MODIFY: OK
2025-06-07 00:24:03,632 - LogUsuariosPipeline - INFO - PRE-LOG-USR: INICIANDO
2025-06-07 00:24:03,632 - LogUsuariosPipeline - INFO - Ejecutando SP_PRE_LOG_USR CORREGIDO (incluye USER_IDs múltiples) para fecha: 2025-06-05
2025-06-07 00:24:11,191 - LogUsuariosPipeline - INFO - SP_PRE_LOG_USR completado: 1581374 registros -> TEMP_LOGS_USUARIOS/********/USER_DATA_TRX.parquet
2025-06-07 00:24:11,191 - LogUsuariosPipeline - INFO - Ejecutando SP_USER_MODIFICATION para fecha: 2025-06-05
2025-06-07 00:24:15,172 - LogUsuariosPipeline - INFO - SP_USER_MODIFICATION completado: 7438 registros -> TEMP_LOGS_USUARIOS/********/USER_MODIFICATION_DAY.parquet
2025-06-07 00:24:15,173 - LogUsuariosPipeline - INFO - Ejecutando SP_USER_AUTH_DAY para fecha: 2025-06-05
2025-06-07 00:24:15,898 - LogUsuariosPipeline - INFO - SP_USER_AUTH_DAY completado: 5804 registros -> TEMP_LOGS_USUARIOS/********/USER_AUTH_CHANGE_HISTORY.parquet
2025-06-07 00:24:15,899 - LogUsuariosPipeline - INFO - PRE-LOG-USR: OK
2025-06-07 00:24:15,899 - LogUsuariosPipeline - INFO - LOG-USR: INICIANDO
2025-06-07 00:24:15,899 - LogUsuariosPipeline - INFO - Ejecutando SP_LOG_USR EXACTO (igual a Oracle) para fecha: 2025-06-05
2025-06-07 00:24:18,063 - LogUsuariosPipeline - INFO - SP_LOG_USR EXACTO completado: 21374 registros -> output/********/LOG_USR.parquet
2025-06-07 00:24:18,064 - LogUsuariosPipeline - INFO - LOG-USR: OK
2025-06-07 00:24:18,064 - LogUsuariosPipeline - INFO - EXPORT-CSV: INICIANDO
2025-06-07 00:24:18,064 - LogUsuariosPipeline - INFO - Iniciando exportación CSV con conversiones de perfiles para fecha: 2025-06-05
2025-06-07 00:24:18,064 - LogUsuariosPipeline - INFO - Aplicando SOLO deduplicación como Oracle original (SIN conversión de tipos)
2025-06-07 00:24:18,386 - LogUsuariosPipeline - INFO - Deduplicación completada (manteniendo REQUESTTYPE originales):
2025-06-07 00:24:18,386 - LogUsuariosPipeline - INFO -   - Registros originales: 21,374
2025-06-07 00:24:18,386 - LogUsuariosPipeline - INFO -   - Registros después de deduplicación: 17,305
2025-06-07 00:24:18,386 - LogUsuariosPipeline - INFO -   - Duplicados eliminados: 4,069
2025-06-07 00:24:18,388 - LogUsuariosPipeline - INFO - Tipos de transacción mantenidos:
2025-06-07 00:24:18,388 - LogUsuariosPipeline - INFO -   - User Modification: 7,193 registros
2025-06-07 00:24:18,388 - LogUsuariosPipeline - INFO -   - CHANGE_AUTH_FACTOR: 5,677 registros
2025-06-07 00:24:18,388 - LogUsuariosPipeline - INFO -   - ActivateUser: 1,877 registros
2025-06-07 00:24:18,388 - LogUsuariosPipeline - INFO -   - AfiliaUser: 1,877 registros
2025-06-07 00:24:18,388 - LogUsuariosPipeline - INFO -   - ClosedAccount: 218 registros
2025-06-07 00:24:18,388 - LogUsuariosPipeline - INFO -   - ClosedUserAccount: 218 registros
2025-06-07 00:24:18,388 - LogUsuariosPipeline - INFO -   - Delete User: 218 registros
2025-06-07 00:24:18,389 - LogUsuariosPipeline - INFO -   - Lock Wallet: 18 registros
2025-06-07 00:24:18,389 - LogUsuariosPipeline - INFO -   - Resume User: 7 registros
2025-06-07 00:24:18,389 - LogUsuariosPipeline - INFO -   - Suspend User: 2 registros
2025-06-07 00:24:18,389 - LogUsuariosPipeline - INFO - Aplicando conversiones de perfiles al archivo: output/********/LOG_USR_DEDUPLICATED.parquet
2025-06-07 00:24:18,389 - LogUsuariosPipeline - INFO - Cargando tabla de conversión de perfiles...
2025-06-07 00:24:18,391 - LogUsuariosPipeline - INFO - Tabla conv_perfil.csv cargada: 796 registros
2025-06-07 00:24:18,395 - LogUsuariosPipeline - INFO - Tabla de conversión de perfiles cargada exitosamente en DuckDB
2025-06-07 00:24:18,397 - LogUsuariosPipeline - ERROR - Error aplicando conversiones al archivo: Binder Error: Values list "cp_a" does not have a column named "PERFIL_CODIGO"

LINE 8: ...            LEFT JOIN conv_perfil_table cp_a ON l.perfilA = cp_a.PERFIL_CODIGO
                                                                       ^
2025-06-07 00:24:18,397 - LogUsuariosPipeline - INFO - Omitiendo CSV principal redundante - usando solo archivos Parquet y CSVs segmentados
2025-06-07 00:24:18,397 - LogUsuariosPipeline - INFO - Iniciando exportación segmentada por BankDomain
2025-06-07 00:24:18,397 - LogUsuariosPipeline - INFO - Procesando TODOS los bancos del sistema: ['0231FCONFIANZA', 'BNACION', 'CCUSCO', 'CRANDES', 'FCOMPARTAMOS', 'FCONFIANZA', None]
2025-06-07 00:24:18,402 - LogUsuariosPipeline - INFO - Exportado dominio 0231FCONFIANZA: 0 registros (archivo vacío SIN headers) -> output/********/csv_exports/by_bank/LOG-USUARIOS-0231FCONFIANZA-********.csv
2025-06-07 00:24:18,424 - LogUsuariosPipeline - INFO - Exportado dominio BNACION: 87 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-BNACION-********.csv
2025-06-07 00:24:18,432 - LogUsuariosPipeline - INFO - Exportado dominio CCUSCO: 2 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-CCUSCO-********.csv
2025-06-07 00:24:18,451 - LogUsuariosPipeline - INFO - Exportado dominio CRANDES: 10 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-CRANDES-********.csv
2025-06-07 00:24:18,586 - LogUsuariosPipeline - INFO - Exportado dominio FCOMPARTAMOS: 21257 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-FCOMPARTAMOS-********.csv
2025-06-07 00:24:18,591 - LogUsuariosPipeline - INFO - Exportado dominio FCONFIANZA: 0 registros (archivo vacío SIN headers) -> output/********/csv_exports/by_bank/LOG-USUARIOS-FCONFIANZA-********.csv
2025-06-07 00:24:18,627 - LogUsuariosPipeline - INFO - Exportado dominio NULL: 18 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-NULL-********.csv
2025-06-07 00:24:18,627 - LogUsuariosPipeline - INFO - Exportación segmentada completada: 7 archivos generados
2025-06-07 00:24:18,627 - LogUsuariosPipeline - INFO - Estructura EXACTA como flujo original: solo by_bank (sin by_profile)
2025-06-07 00:24:18,627 - LogUsuariosPipeline - INFO - 🔄 APLICANDO PROCESAMIENTO EXACTO como procesar.py...
2025-06-07 00:24:18,628 - LogUsuariosPipeline - INFO - 🔄 INICIANDO PROCESAMIENTO EXACTO como procesar.py
2025-06-07 00:24:18,628 - LogUsuariosPipeline - INFO - 📁 Input: output/********/LOG_USR.parquet
2025-06-07 00:24:18,628 - LogUsuariosPipeline - INFO - 📅 Fecha: 2025-06-05
2025-06-07 00:24:18,628 - LogUsuariosPipeline - INFO - 📁 Output: output/********/csv_exports/csv_final_procesado
2025-06-07 00:24:18,628 - LogUsuariosPipeline - INFO - 📊 Cargando datos desde LOG_USR.parquet...
2025-06-07 00:24:18,704 - LogUsuariosPipeline - INFO - 📊 Registros cargados: 21,374
2025-06-07 00:24:18,705 - LogUsuariosPipeline - INFO - 🔄 Procesando grupos por USERHISTID...
2025-06-07 00:24:22,183 - LogUsuariosPipeline - INFO - 📊 Convirtiendo a DataFrame...
2025-06-07 00:24:22,316 - LogUsuariosPipeline - INFO - 📊 Registros después del procesamiento inicial: 51,958
2025-06-07 00:24:22,317 - LogUsuariosPipeline - INFO - 🔍 Aplicando filtro de valores válidos...
2025-06-07 00:24:22,325 - LogUsuariosPipeline - INFO - 📊 Registros después del filtro: 19,710
2025-06-07 00:24:22,325 - LogUsuariosPipeline - INFO - 🥷 Aplicando filtros adicionales de calidad de datos...
2025-06-07 00:24:22,335 - LogUsuariosPipeline - INFO -   🔍 Eliminados 18 registros con BANKDOMAIN inválido
2025-06-07 00:24:22,347 - LogUsuariosPipeline - INFO - 📊 Registros después de filtros de calidad: 19,692
2025-06-07 00:24:22,347 - LogUsuariosPipeline - INFO - 🔄 Agrupando y procesando JSON...
2025-06-07 00:24:25,682 - LogUsuariosPipeline - INFO - 📊 Registros después de eliminar duplicados: 14,144
2025-06-07 00:24:25,695 - LogUsuariosPipeline - INFO - 🔄 Aplicando asignaciones a columnas...
2025-06-07 00:24:26,793 - LogUsuariosPipeline - INFO - 📊 Cargando tabla de conversión de perfiles...
2025-06-07 00:24:26,795 - LogUsuariosPipeline - INFO - 📊 Tabla de conversión cargada: 796 registros
2025-06-07 00:24:27,160 - LogUsuariosPipeline - INFO - 🔄 Aplicando función crítica de expansión de registros...
2025-06-07 00:24:27,161 - LogUsuariosPipeline - INFO - 📊 Registros antes de expansión: 14,144
2025-06-07 00:24:27,848 - LogUsuariosPipeline - INFO - 📊 Registros después de expansión: 14,160
2025-06-07 00:24:27,848 - LogUsuariosPipeline - INFO - ✅ Función crítica de expansión aplicada exitosamente
2025-06-07 00:24:27,848 - LogUsuariosPipeline - INFO - 🔄 Aplicando mapeo de columnas...
2025-06-07 00:24:27,851 - LogUsuariosPipeline - INFO - 🔄 Procesando tipos de transacciones válidas...
2025-06-07 00:24:27,894 - LogUsuariosPipeline - INFO - 📊 Registros después de concatenar: 14,160
2025-06-07 00:24:27,934 - LogUsuariosPipeline - INFO - 📊 Registros finales para exportar: 14,160
2025-06-07 00:24:27,934 - LogUsuariosPipeline - INFO - 📁 Generando archivos CSV por BankDomain...
2025-06-07 00:24:27,982 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-FCOMPARTAMOS-********002427.csv (14,098 registros)
2025-06-07 00:24:27,985 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-BNACION-********002427.csv (55 registros)
2025-06-07 00:24:27,986 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-CRANDES-********002427.csv (6 registros)
2025-06-07 00:24:27,987 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-CCUSCO-********002427.csv (1 registros)
2025-06-07 00:24:27,987 - LogUsuariosPipeline - INFO - ✅ Procesamiento completado: 4 archivos generados
2025-06-07 00:24:27,987 - LogUsuariosPipeline - INFO - 📊 Total registros procesados: 14,160
2025-06-07 00:24:28,043 - LogUsuariosPipeline - INFO - ✅ Procesamiento exacto completado: 4 archivos
2025-06-07 00:24:28,044 - LogUsuariosPipeline - INFO -   📁 LOGUSR-FCOMPARTAMOS-********002427.csv
2025-06-07 00:24:28,044 - LogUsuariosPipeline - INFO -   📁 LOGUSR-BNACION-********002427.csv
2025-06-07 00:24:28,044 - LogUsuariosPipeline - INFO -   📁 LOGUSR-CRANDES-********002427.csv
2025-06-07 00:24:28,044 - LogUsuariosPipeline - INFO -   📁 LOGUSR-CCUSCO-********002427.csv
2025-06-07 00:24:28,044 - LogUsuariosPipeline - INFO - Archivo temporal eliminado: TEMP_LOGS_USUARIOS/********/WALLET_OLD.parquet
2025-06-07 00:24:28,044 - LogUsuariosPipeline - INFO - EXPORT-CSV: OK
2025-06-07 00:24:28,044 - LogUsuariosPipeline - INFO - === ARCHIVOS CSV GENERADOS ===
2025-06-07 00:24:28,044 - LogUsuariosPipeline - INFO - Total de archivos CSV segmentados: 11
2025-06-07 00:24:28,044 - LogUsuariosPipeline - INFO - Archivos por BankDomain (7) - EXACTO como flujo original:
2025-06-07 00:24:28,044 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-0231FCONFIANZA-********.csv
2025-06-07 00:24:28,044 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-BNACION-********.csv
2025-06-07 00:24:28,044 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-CCUSCO-********.csv
2025-06-07 00:24:28,044 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-CRANDES-********.csv
2025-06-07 00:24:28,044 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-FCOMPARTAMOS-********.csv
2025-06-07 00:24:28,044 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-FCONFIANZA-********.csv
2025-06-07 00:24:28,044 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-NULL-********.csv
2025-06-07 00:24:28,044 - LogUsuariosPipeline - INFO - Estructura OPTIMIZADA: solo archivos segmentados (datos completos en Parquet)
2025-06-07 00:24:28,045 - LogUsuariosPipeline - INFO - === PIPELINE COMPLETADO EXITOSAMENTE ===
2025-06-07 00:24:28,045 - LogUsuariosPipeline - INFO - 
=== ARCHIVOS GENERADOS ===
2025-06-07 00:24:28,045 - LogUsuariosPipeline - INFO - Archivos temporales en TEMP_LOGS_USUARIOS/********:
2025-06-07 00:24:28,045 - LogUsuariosPipeline - INFO -   - USER_ACCOUNT_HISTORY.parquet (0.00 MB)
2025-06-07 00:24:28,045 - LogUsuariosPipeline - INFO -   - USER_DATA_TRX.parquet (126.93 MB)
2025-06-07 00:24:28,045 - LogUsuariosPipeline - INFO -   - USER_MODIFICATION_DAY.parquet (3.68 MB)
2025-06-07 00:24:28,045 - LogUsuariosPipeline - INFO -   - USER_AUTH_CHANGE_HISTORY.parquet (0.13 MB)
2025-06-07 00:24:28,045 - LogUsuariosPipeline - INFO - Archivos de salida en output/********:
2025-06-07 00:24:28,045 - LogUsuariosPipeline - INFO -   - LOG_USR.parquet (4.74 MB)
2025-06-07 00:24:28,045 - LogUsuariosPipeline - INFO -   - LOG_USR_DEDUPLICATED.parquet (4.61 MB)
2025-06-07 00:24:28,045 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-0231FCONFIANZA-********.csv (0.00 MB)
2025-06-07 00:24:28,045 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-BNACION-********.csv (0.07 MB)
2025-06-07 00:24:28,045 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-CCUSCO-********.csv (0.00 MB)
2025-06-07 00:24:28,045 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-CRANDES-********.csv (0.01 MB)
2025-06-07 00:24:28,046 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-FCOMPARTAMOS-********.csv (33.63 MB)
2025-06-07 00:24:28,046 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-FCONFIANZA-********.csv (0.00 MB)
2025-06-07 00:24:28,046 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-NULL-********.csv (0.00 MB)
2025-06-07 00:24:28,046 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-FCOMPARTAMOS-********002427.csv (1.51 MB)
2025-06-07 00:24:28,046 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-BNACION-********002427.csv (0.00 MB)
2025-06-07 00:24:28,046 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-CRANDES-********002427.csv (0.00 MB)
2025-06-07 00:24:28,046 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-CCUSCO-********002427.csv (0.00 MB)
2025-06-07 01:00:59,849 - LogUsuariosPipeline - INFO - Configurando credenciales S3...
2025-06-07 01:00:59,861 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-07 01:00:59,911 - LogUsuariosPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-07 01:00:59,912 - LogUsuariosPipeline - INFO - === INICIANDO PIPELINE LOG_USUARIOS MODERNIZADO ===
2025-06-07 01:00:59,912 - LogUsuariosPipeline - INFO - Fecha de procesamiento: 2025-06-04 (carpeta: ********)
2025-06-07 01:00:59,912 - LogUsuariosPipeline - INFO - USER-MODIFY: INICIANDO
2025-06-07 01:00:59,912 - LogUsuariosPipeline - INFO - Extrayendo USER_ACCOUNT_HISTORY para fecha: 2025-06-04
2025-06-07 01:01:00,054 - LogUsuariosPipeline - INFO - USER_ACCOUNT_HISTORY extraído: 8 registros -> TEMP_LOGS_USUARIOS/********/USER_ACCOUNT_HISTORY.parquet
2025-06-07 01:01:00,054 - LogUsuariosPipeline - INFO - USER-MODIFY: OK
2025-06-07 01:01:00,054 - LogUsuariosPipeline - INFO - PRE-LOG-USR: INICIANDO
2025-06-07 01:01:00,054 - LogUsuariosPipeline - INFO - Ejecutando SP_PRE_LOG_USR CORREGIDO (incluye USER_IDs múltiples) para fecha: 2025-06-04
2025-06-07 01:01:07,811 - LogUsuariosPipeline - INFO - SP_PRE_LOG_USR completado: 1579497 registros -> TEMP_LOGS_USUARIOS/********/USER_DATA_TRX.parquet
2025-06-07 01:01:07,811 - LogUsuariosPipeline - INFO - Ejecutando SP_USER_MODIFICATION para fecha: 2025-06-04
2025-06-07 01:01:11,367 - LogUsuariosPipeline - INFO - SP_USER_MODIFICATION completado: 7118 registros -> TEMP_LOGS_USUARIOS/********/USER_MODIFICATION_DAY.parquet
2025-06-07 01:01:11,367 - LogUsuariosPipeline - INFO - Ejecutando SP_USER_AUTH_DAY para fecha: 2025-06-04
2025-06-07 01:01:12,209 - LogUsuariosPipeline - INFO - SP_USER_AUTH_DAY completado: 5412 registros -> TEMP_LOGS_USUARIOS/********/USER_AUTH_CHANGE_HISTORY.parquet
2025-06-07 01:01:12,209 - LogUsuariosPipeline - INFO - PRE-LOG-USR: OK
2025-06-07 01:01:12,209 - LogUsuariosPipeline - INFO - LOG-USR: INICIANDO
2025-06-07 01:01:12,209 - LogUsuariosPipeline - INFO - Ejecutando SP_LOG_USR EXACTO (igual a Oracle) para fecha: 2025-06-04
2025-06-07 01:01:14,399 - LogUsuariosPipeline - INFO - SP_LOG_USR EXACTO completado: 20226 registros -> output/********/LOG_USR.parquet
2025-06-07 01:01:14,400 - LogUsuariosPipeline - INFO - LOG-USR: OK
2025-06-07 01:01:14,400 - LogUsuariosPipeline - INFO - EXPORT-CSV: INICIANDO
2025-06-07 01:01:14,400 - LogUsuariosPipeline - INFO - Iniciando exportación CSV con conversiones de perfiles para fecha: 2025-06-04
2025-06-07 01:01:14,400 - LogUsuariosPipeline - INFO - Aplicando SOLO deduplicación como Oracle original (SIN conversión de tipos)
2025-06-07 01:01:14,657 - LogUsuariosPipeline - INFO - Deduplicación completada (manteniendo REQUESTTYPE originales):
2025-06-07 01:01:14,657 - LogUsuariosPipeline - INFO -   - Registros originales: 20,226
2025-06-07 01:01:14,657 - LogUsuariosPipeline - INFO -   - Registros después de deduplicación: 16,430
2025-06-07 01:01:14,657 - LogUsuariosPipeline - INFO -   - Duplicados eliminados: 3,796
2025-06-07 01:01:14,660 - LogUsuariosPipeline - INFO - Tipos de transacción mantenidos:
2025-06-07 01:01:14,660 - LogUsuariosPipeline - INFO -   - User Modification: 6,876 registros
2025-06-07 01:01:14,660 - LogUsuariosPipeline - INFO -   - CHANGE_AUTH_FACTOR: 5,299 registros
2025-06-07 01:01:14,660 - LogUsuariosPipeline - INFO -   - ActivateUser: 1,774 registros
2025-06-07 01:01:14,660 - LogUsuariosPipeline - INFO -   - AfiliaUser: 1,774 registros
2025-06-07 01:01:14,660 - LogUsuariosPipeline - INFO -   - ClosedAccount: 233 registros
2025-06-07 01:01:14,660 - LogUsuariosPipeline - INFO -   - ClosedUserAccount: 233 registros
2025-06-07 01:01:14,661 - LogUsuariosPipeline - INFO -   - Delete User: 233 registros
2025-06-07 01:01:14,661 - LogUsuariosPipeline - INFO -   - Resume User: 3 registros
2025-06-07 01:01:14,661 - LogUsuariosPipeline - INFO -   - Suspend User: 3 registros
2025-06-07 01:01:14,661 - LogUsuariosPipeline - INFO -   - RESET_AUTH_VALUE: 1 registros
2025-06-07 01:01:14,661 - LogUsuariosPipeline - INFO - Aplicando conversiones de perfiles al archivo: output/********/LOG_USR_DEDUPLICATED.parquet
2025-06-07 01:01:14,661 - LogUsuariosPipeline - INFO - Cargando tabla de conversión de perfiles...
2025-06-07 01:01:14,663 - LogUsuariosPipeline - INFO - Tabla conv_perfil.csv cargada: 796 registros
2025-06-07 01:01:14,667 - LogUsuariosPipeline - INFO - Tabla de conversión de perfiles cargada exitosamente en DuckDB
2025-06-07 01:01:14,669 - LogUsuariosPipeline - ERROR - Error aplicando conversiones al archivo: Binder Error: Values list "cp_a" does not have a column named "PERFIL_CODIGO"

LINE 8: ...            LEFT JOIN conv_perfil_table cp_a ON l.perfilA = cp_a.PERFIL_CODIGO
                                                                       ^
2025-06-07 01:01:14,669 - LogUsuariosPipeline - INFO - Omitiendo CSV principal redundante - usando solo archivos Parquet y CSVs segmentados
2025-06-07 01:01:14,669 - LogUsuariosPipeline - INFO - Iniciando exportación segmentada por BankDomain
2025-06-07 01:01:14,669 - LogUsuariosPipeline - INFO - Procesando TODOS los bancos del sistema: ['0231FCONFIANZA', 'BNACION', 'CCUSCO', 'CRANDES', 'FCOMPARTAMOS', 'FCONFIANZA', None]
2025-06-07 01:01:14,676 - LogUsuariosPipeline - INFO - Exportado dominio 0231FCONFIANZA: 4 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-0231FCONFIANZA-********.csv
2025-06-07 01:01:14,703 - LogUsuariosPipeline - INFO - Exportado dominio BNACION: 67 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-BNACION-********.csv
2025-06-07 01:01:14,725 - LogUsuariosPipeline - INFO - Exportado dominio CCUSCO: 4 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-CCUSCO-********.csv
2025-06-07 01:01:14,732 - LogUsuariosPipeline - INFO - Exportado dominio CRANDES: 0 registros (archivo vacío SIN headers) -> output/********/csv_exports/by_bank/LOG-USUARIOS-CRANDES-********.csv
2025-06-07 01:01:14,835 - LogUsuariosPipeline - INFO - Exportado dominio FCOMPARTAMOS: 20151 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-FCOMPARTAMOS-********.csv
2025-06-07 01:01:14,839 - LogUsuariosPipeline - INFO - Exportado dominio FCONFIANZA: 0 registros (archivo vacío SIN headers) -> output/********/csv_exports/by_bank/LOG-USUARIOS-FCONFIANZA-********.csv
2025-06-07 01:01:14,844 - LogUsuariosPipeline - INFO - Exportado dominio NULL: 0 registros (archivo vacío SIN headers) -> output/********/csv_exports/by_bank/LOG-USUARIOS-NULL-********.csv
2025-06-07 01:01:14,844 - LogUsuariosPipeline - INFO - Exportación segmentada completada: 7 archivos generados
2025-06-07 01:01:14,844 - LogUsuariosPipeline - INFO - Estructura EXACTA como flujo original: solo by_bank (sin by_profile)
2025-06-07 01:01:14,844 - LogUsuariosPipeline - INFO - 🔄 APLICANDO PROCESAMIENTO EXACTO como procesar.py...
2025-06-07 01:01:14,844 - LogUsuariosPipeline - INFO - 🔄 INICIANDO PROCESAMIENTO EXACTO como procesar.py
2025-06-07 01:01:14,844 - LogUsuariosPipeline - INFO - 📁 Input: output/********/LOG_USR.parquet
2025-06-07 01:01:14,844 - LogUsuariosPipeline - INFO - 📅 Fecha: 2025-06-04
2025-06-07 01:01:14,844 - LogUsuariosPipeline - INFO - 📁 Output: output/********/csv_exports/csv_final_procesado
2025-06-07 01:01:14,844 - LogUsuariosPipeline - INFO - 📊 Cargando datos desde LOG_USR.parquet...
2025-06-07 01:01:14,914 - LogUsuariosPipeline - INFO - 📊 Registros cargados: 20,226
2025-06-07 01:01:14,914 - LogUsuariosPipeline - INFO - 🔄 Procesando grupos por USERHISTID...
2025-06-07 01:01:18,255 - LogUsuariosPipeline - INFO - 📊 Convirtiendo a DataFrame...
2025-06-07 01:01:18,382 - LogUsuariosPipeline - INFO - 📊 Registros después del procesamiento inicial: 50,010
2025-06-07 01:01:18,382 - LogUsuariosPipeline - INFO - 🔍 Aplicando filtro de valores válidos...
2025-06-07 01:01:18,390 - LogUsuariosPipeline - INFO - 📊 Registros después del filtro: 18,480
2025-06-07 01:01:18,390 - LogUsuariosPipeline - INFO - 🥷 Aplicando filtros adicionales de calidad de datos...
2025-06-07 01:01:18,406 - LogUsuariosPipeline - INFO - 📊 Registros después de filtros de calidad: 18,480
2025-06-07 01:01:18,406 - LogUsuariosPipeline - INFO - 🔄 Agrupando y procesando JSON...
2025-06-07 01:01:21,598 - LogUsuariosPipeline - INFO - 📊 Registros después de eliminar duplicados: 13,305
2025-06-07 01:01:21,609 - LogUsuariosPipeline - INFO - 🔄 Aplicando asignaciones a columnas...
2025-06-07 01:01:22,671 - LogUsuariosPipeline - INFO - 📊 Cargando tabla de conversión de perfiles...
2025-06-07 01:01:22,672 - LogUsuariosPipeline - INFO - 📊 Tabla de conversión cargada: 796 registros
2025-06-07 01:01:23,005 - LogUsuariosPipeline - INFO - 🔄 Aplicando función crítica de expansión de registros...
2025-06-07 01:01:23,005 - LogUsuariosPipeline - INFO - 📊 Registros antes de expansión: 13,305
2025-06-07 01:01:23,622 - LogUsuariosPipeline - INFO - 📊 Registros después de expansión: 13,321
2025-06-07 01:01:23,622 - LogUsuariosPipeline - INFO - ✅ Función crítica de expansión aplicada exitosamente
2025-06-07 01:01:23,622 - LogUsuariosPipeline - INFO - 🔄 Aplicando mapeo de columnas...
2025-06-07 01:01:23,624 - LogUsuariosPipeline - INFO - 🔄 Procesando tipos de transacciones válidas...
2025-06-07 01:01:23,667 - LogUsuariosPipeline - INFO - 📊 Registros después de concatenar: 13,321
2025-06-07 01:01:23,706 - LogUsuariosPipeline - INFO - 📊 Registros finales para exportar: 13,321
2025-06-07 01:01:23,706 - LogUsuariosPipeline - INFO - 📁 Generando archivos CSV por BankDomain...
2025-06-07 01:01:23,746 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-FCOMPARTAMOS-********010123.csv (13,275 registros)
2025-06-07 01:01:23,748 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-0231FCONFIANZA-********010123.csv (2 registros)
2025-06-07 01:01:23,750 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-BNACION-********010123.csv (41 registros)
2025-06-07 01:01:23,751 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-CCUSCO-********010123.csv (3 registros)
2025-06-07 01:01:23,751 - LogUsuariosPipeline - INFO - ✅ Procesamiento completado: 4 archivos generados
2025-06-07 01:01:23,751 - LogUsuariosPipeline - INFO - 📊 Total registros procesados: 13,321
2025-06-07 01:01:23,807 - LogUsuariosPipeline - INFO - ✅ Procesamiento exacto completado: 4 archivos
2025-06-07 01:01:23,807 - LogUsuariosPipeline - INFO -   📁 LOGUSR-FCOMPARTAMOS-********010123.csv
2025-06-07 01:01:23,807 - LogUsuariosPipeline - INFO -   📁 LOGUSR-0231FCONFIANZA-********010123.csv
2025-06-07 01:01:23,807 - LogUsuariosPipeline - INFO -   📁 LOGUSR-BNACION-********010123.csv
2025-06-07 01:01:23,807 - LogUsuariosPipeline - INFO -   📁 LOGUSR-CCUSCO-********010123.csv
2025-06-07 01:01:23,807 - LogUsuariosPipeline - INFO - Archivo temporal eliminado: TEMP_LOGS_USUARIOS/********/WALLET_OLD.parquet
2025-06-07 01:01:23,808 - LogUsuariosPipeline - INFO - EXPORT-CSV: OK
2025-06-07 01:01:23,808 - LogUsuariosPipeline - INFO - === ARCHIVOS CSV GENERADOS ===
2025-06-07 01:01:23,808 - LogUsuariosPipeline - INFO - Total de archivos CSV segmentados: 11
2025-06-07 01:01:23,808 - LogUsuariosPipeline - INFO - Archivos por BankDomain (7) - EXACTO como flujo original:
2025-06-07 01:01:23,808 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-0231FCONFIANZA-********.csv
2025-06-07 01:01:23,808 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-BNACION-********.csv
2025-06-07 01:01:23,808 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-CCUSCO-********.csv
2025-06-07 01:01:23,808 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-CRANDES-********.csv
2025-06-07 01:01:23,808 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-FCOMPARTAMOS-********.csv
2025-06-07 01:01:23,808 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-FCONFIANZA-********.csv
2025-06-07 01:01:23,808 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-NULL-********.csv
2025-06-07 01:01:23,808 - LogUsuariosPipeline - INFO - Estructura OPTIMIZADA: solo archivos segmentados (datos completos en Parquet)
2025-06-07 01:01:23,808 - LogUsuariosPipeline - INFO - === PIPELINE COMPLETADO EXITOSAMENTE ===
2025-06-07 01:01:23,808 - LogUsuariosPipeline - INFO - 
=== ARCHIVOS GENERADOS ===
2025-06-07 01:01:23,808 - LogUsuariosPipeline - INFO - Archivos temporales en TEMP_LOGS_USUARIOS/********:
2025-06-07 01:01:23,808 - LogUsuariosPipeline - INFO -   - USER_ACCOUNT_HISTORY.parquet (0.00 MB)
2025-06-07 01:01:23,808 - LogUsuariosPipeline - INFO -   - USER_DATA_TRX.parquet (126.71 MB)
2025-06-07 01:01:23,808 - LogUsuariosPipeline - INFO -   - USER_MODIFICATION_DAY.parquet (3.52 MB)
2025-06-07 01:01:23,808 - LogUsuariosPipeline - INFO -   - USER_AUTH_CHANGE_HISTORY.parquet (0.12 MB)
2025-06-07 01:01:23,808 - LogUsuariosPipeline - INFO - Archivos de salida en output/********:
2025-06-07 01:01:23,808 - LogUsuariosPipeline - INFO -   - LOG_USR.parquet (4.57 MB)
2025-06-07 01:01:23,808 - LogUsuariosPipeline - INFO -   - LOG_USR_DEDUPLICATED.parquet (4.41 MB)
2025-06-07 01:01:23,809 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-0231FCONFIANZA-********.csv (0.00 MB)
2025-06-07 01:01:23,809 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-BNACION-********.csv (0.04 MB)
2025-06-07 01:01:23,809 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-CCUSCO-********.csv (0.01 MB)
2025-06-07 01:01:23,809 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-CRANDES-********.csv (0.00 MB)
2025-06-07 01:01:23,809 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-FCOMPARTAMOS-********.csv (32.23 MB)
2025-06-07 01:01:23,809 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-FCONFIANZA-********.csv (0.00 MB)
2025-06-07 01:01:23,809 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-NULL-********.csv (0.00 MB)
2025-06-07 01:01:23,809 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-FCOMPARTAMOS-********010123.csv (1.42 MB)
2025-06-07 01:01:23,809 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-0231FCONFIANZA-********010123.csv (0.00 MB)
2025-06-07 01:01:23,809 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-BNACION-********010123.csv (0.00 MB)
2025-06-07 01:01:23,809 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-CCUSCO-********010123.csv (0.00 MB)
2025-06-07 07:09:52,266 - LogUsuariosPipeline - INFO - Configurando credenciales S3...
2025-06-07 07:09:52,279 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-07 07:09:52,327 - LogUsuariosPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-07 07:09:52,328 - LogUsuariosPipeline - INFO - === INICIANDO PIPELINE LOG_USUARIOS MODERNIZADO ===
2025-06-07 07:09:52,328 - LogUsuariosPipeline - INFO - Fecha de procesamiento: 2025-06-05 (carpeta: ********)
2025-06-07 07:09:52,328 - LogUsuariosPipeline - INFO - USER-MODIFY: INICIANDO
2025-06-07 07:09:52,328 - LogUsuariosPipeline - INFO - Extrayendo USER_ACCOUNT_HISTORY para fecha: 2025-06-05
2025-06-07 07:09:52,483 - LogUsuariosPipeline - INFO - USER_ACCOUNT_HISTORY extraído: 11 registros -> TEMP_LOGS_USUARIOS/********/USER_ACCOUNT_HISTORY.parquet
2025-06-07 07:09:52,483 - LogUsuariosPipeline - INFO - USER-MODIFY: OK
2025-06-07 07:09:52,483 - LogUsuariosPipeline - INFO - PRE-LOG-USR: INICIANDO
2025-06-07 07:09:52,483 - LogUsuariosPipeline - INFO - Ejecutando SP_PRE_LOG_USR CORREGIDO (incluye USER_IDs múltiples) para fecha: 2025-06-05
2025-06-07 07:10:01,590 - LogUsuariosPipeline - INFO - SP_PRE_LOG_USR completado: 1581374 registros -> TEMP_LOGS_USUARIOS/********/USER_DATA_TRX.parquet
2025-06-07 07:10:01,591 - LogUsuariosPipeline - INFO - Ejecutando SP_USER_MODIFICATION para fecha: 2025-06-05
2025-06-07 07:10:05,033 - LogUsuariosPipeline - INFO - SP_USER_MODIFICATION completado: 7438 registros -> TEMP_LOGS_USUARIOS/********/USER_MODIFICATION_DAY.parquet
2025-06-07 07:10:05,033 - LogUsuariosPipeline - INFO - Ejecutando SP_USER_AUTH_DAY para fecha: 2025-06-05
2025-06-07 07:10:05,813 - LogUsuariosPipeline - INFO - SP_USER_AUTH_DAY completado: 5804 registros -> TEMP_LOGS_USUARIOS/********/USER_AUTH_CHANGE_HISTORY.parquet
2025-06-07 07:10:05,813 - LogUsuariosPipeline - INFO - PRE-LOG-USR: OK
2025-06-07 07:10:05,813 - LogUsuariosPipeline - INFO - LOG-USR: INICIANDO
2025-06-07 07:10:05,813 - LogUsuariosPipeline - INFO - Ejecutando SP_LOG_USR EXACTO (igual a Oracle) para fecha: 2025-06-05
2025-06-07 07:10:07,794 - LogUsuariosPipeline - INFO - SP_LOG_USR EXACTO completado: 21392 registros -> output/********/LOG_USR.parquet
2025-06-07 07:10:07,795 - LogUsuariosPipeline - INFO - LOG-USR: OK
2025-06-07 07:10:07,795 - LogUsuariosPipeline - INFO - EXPORT-CSV: INICIANDO
2025-06-07 07:10:07,795 - LogUsuariosPipeline - INFO - Iniciando exportación CSV con conversiones de perfiles para fecha: 2025-06-05
2025-06-07 07:10:07,795 - LogUsuariosPipeline - INFO - Aplicando SOLO deduplicación como Oracle original (SIN conversión de tipos)
2025-06-07 07:10:08,099 - LogUsuariosPipeline - INFO - Deduplicación completada (manteniendo REQUESTTYPE originales):
2025-06-07 07:10:08,099 - LogUsuariosPipeline - INFO -   - Registros originales: 21,392
2025-06-07 07:10:08,099 - LogUsuariosPipeline - INFO -   - Registros después de deduplicación: 17,305
2025-06-07 07:10:08,099 - LogUsuariosPipeline - INFO -   - Duplicados eliminados: 4,087
2025-06-07 07:10:08,101 - LogUsuariosPipeline - INFO - Tipos de transacción mantenidos:
2025-06-07 07:10:08,101 - LogUsuariosPipeline - INFO -   - User Modification: 7,193 registros
2025-06-07 07:10:08,101 - LogUsuariosPipeline - INFO -   - CHANGE_AUTH_FACTOR: 5,677 registros
2025-06-07 07:10:08,102 - LogUsuariosPipeline - INFO -   - AfiliaUser: 1,877 registros
2025-06-07 07:10:08,102 - LogUsuariosPipeline - INFO -   - ActivateUser: 1,877 registros
2025-06-07 07:10:08,102 - LogUsuariosPipeline - INFO -   - ClosedAccount: 218 registros
2025-06-07 07:10:08,102 - LogUsuariosPipeline - INFO -   - Delete User: 218 registros
2025-06-07 07:10:08,102 - LogUsuariosPipeline - INFO -   - ClosedUserAccount: 218 registros
2025-06-07 07:10:08,102 - LogUsuariosPipeline - INFO -   - Lock Wallet: 18 registros
2025-06-07 07:10:08,102 - LogUsuariosPipeline - INFO -   - Resume User: 7 registros
2025-06-07 07:10:08,102 - LogUsuariosPipeline - INFO -   - Suspend User: 2 registros
2025-06-07 07:10:08,102 - LogUsuariosPipeline - INFO - Aplicando conversiones de perfiles al archivo: output/********/LOG_USR_DEDUPLICATED.parquet
2025-06-07 07:10:08,102 - LogUsuariosPipeline - INFO - Cargando tabla de conversión de perfiles...
2025-06-07 07:10:08,104 - LogUsuariosPipeline - INFO - Tabla conv_perfil.csv cargada: 796 registros
2025-06-07 07:10:08,109 - LogUsuariosPipeline - INFO - Tabla de conversión de perfiles cargada exitosamente en DuckDB
2025-06-07 07:10:08,110 - LogUsuariosPipeline - ERROR - Error aplicando conversiones al archivo: Binder Error: Values list "cp_a" does not have a column named "PERFIL_CODIGO"

LINE 8: ...            LEFT JOIN conv_perfil_table cp_a ON l.perfilA = cp_a.PERFIL_CODIGO
                                                                       ^
2025-06-07 07:10:08,111 - LogUsuariosPipeline - INFO - Omitiendo CSV principal redundante - usando solo archivos Parquet y CSVs segmentados
2025-06-07 07:10:08,111 - LogUsuariosPipeline - INFO - Iniciando exportación segmentada por BankDomain
2025-06-07 07:10:08,111 - LogUsuariosPipeline - INFO - Procesando TODOS los bancos del sistema: ['0231FCONFIANZA', 'BNACION', 'CCUSCO', 'CRANDES', 'FCOMPARTAMOS', 'FCONFIANZA', None]
2025-06-07 07:10:08,115 - LogUsuariosPipeline - INFO - Exportado dominio 0231FCONFIANZA: 0 registros (archivo vacío SIN headers) -> output/********/csv_exports/by_bank/LOG-USUARIOS-0231FCONFIANZA-********.csv
2025-06-07 07:10:08,141 - LogUsuariosPipeline - INFO - Exportado dominio BNACION: 87 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-BNACION-********.csv
2025-06-07 07:10:08,150 - LogUsuariosPipeline - INFO - Exportado dominio CCUSCO: 2 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-CCUSCO-********.csv
2025-06-07 07:10:08,168 - LogUsuariosPipeline - INFO - Exportado dominio CRANDES: 10 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-CRANDES-********.csv
2025-06-07 07:10:08,298 - LogUsuariosPipeline - INFO - Exportado dominio FCOMPARTAMOS: 21275 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-FCOMPARTAMOS-********.csv
2025-06-07 07:10:08,305 - LogUsuariosPipeline - INFO - Exportado dominio FCONFIANZA: 0 registros (archivo vacío SIN headers) -> output/********/csv_exports/by_bank/LOG-USUARIOS-FCONFIANZA-********.csv
2025-06-07 07:10:08,356 - LogUsuariosPipeline - INFO - Exportado dominio NULL: 18 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-NULL-********.csv
2025-06-07 07:10:08,356 - LogUsuariosPipeline - INFO - Exportación segmentada completada: 7 archivos generados
2025-06-07 07:10:08,356 - LogUsuariosPipeline - INFO - Estructura EXACTA como flujo original: solo by_bank (sin by_profile)
2025-06-07 07:10:08,356 - LogUsuariosPipeline - INFO - 🔄 APLICANDO PROCESAMIENTO EXACTO como procesar.py...
2025-06-07 07:10:08,360 - LogUsuariosPipeline - INFO - 🔄 INICIANDO PROCESAMIENTO EXACTO como procesar.py
2025-06-07 07:10:08,360 - LogUsuariosPipeline - INFO - 📁 Input: output/********/LOG_USR.parquet
2025-06-07 07:10:08,360 - LogUsuariosPipeline - INFO - 📅 Fecha: 2025-06-05
2025-06-07 07:10:08,361 - LogUsuariosPipeline - INFO - 📁 Output: output/********/csv_exports/csv_final_procesado
2025-06-07 07:10:08,361 - LogUsuariosPipeline - INFO - 📊 Cargando datos desde LOG_USR.parquet...
2025-06-07 07:10:08,434 - LogUsuariosPipeline - INFO - 📊 Registros cargados: 21,392
2025-06-07 07:10:08,434 - LogUsuariosPipeline - INFO - 🔄 Procesando grupos por USERHISTID...
2025-06-07 07:10:11,949 - LogUsuariosPipeline - INFO - 📊 Convirtiendo a DataFrame...
2025-06-07 07:10:12,080 - LogUsuariosPipeline - INFO - 📊 Registros después del procesamiento inicial: 51,976
2025-06-07 07:10:12,081 - LogUsuariosPipeline - INFO - 🔍 Aplicando filtro de valores válidos...
2025-06-07 07:10:12,089 - LogUsuariosPipeline - INFO - 📊 Registros después del filtro: 19,728
2025-06-07 07:10:12,089 - LogUsuariosPipeline - INFO - 🥷 Aplicando filtros adicionales de calidad de datos...
2025-06-07 07:10:12,098 - LogUsuariosPipeline - INFO -   🔍 Eliminados 18 registros con BANKDOMAIN inválido
2025-06-07 07:10:12,111 - LogUsuariosPipeline - INFO - 📊 Registros después de filtros de calidad: 19,710
2025-06-07 07:10:12,111 - LogUsuariosPipeline - INFO - 🔄 Agrupando y procesando JSON...
2025-06-07 07:10:15,412 - LogUsuariosPipeline - INFO - 📊 Registros después de eliminar duplicados: 14,144
2025-06-07 07:10:15,425 - LogUsuariosPipeline - INFO - 🔄 Aplicando asignaciones a columnas...
2025-06-07 07:10:16,522 - LogUsuariosPipeline - INFO - 📊 Cargando tabla de conversión de perfiles...
2025-06-07 07:10:16,524 - LogUsuariosPipeline - INFO - 📊 Tabla de conversión cargada: 796 registros
2025-06-07 07:10:16,884 - LogUsuariosPipeline - INFO - 🔄 Aplicando función crítica de expansión de registros...
2025-06-07 07:10:16,884 - LogUsuariosPipeline - INFO - 📊 Registros antes de expansión: 14,144
2025-06-07 07:10:17,484 - LogUsuariosPipeline - INFO - 📊 Registros después de expansión: 14,160
2025-06-07 07:10:17,484 - LogUsuariosPipeline - INFO - ✅ Función crítica de expansión aplicada exitosamente
2025-06-07 07:10:17,484 - LogUsuariosPipeline - INFO - 🔄 Aplicando mapeo de columnas...
2025-06-07 07:10:17,486 - LogUsuariosPipeline - INFO - 🔄 Procesando tipos de transacciones válidas...
2025-06-07 07:10:17,507 - LogUsuariosPipeline - INFO - 📊 Registros después de concatenar: 14,160
2025-06-07 07:10:34,332 - LogUsuariosPipeline - INFO - 📊 Registros finales para exportar: 14,160
2025-06-07 07:10:34,332 - LogUsuariosPipeline - INFO - 📁 Generando archivos CSV por BankDomain...
2025-06-07 07:10:34,431 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-FCOMPARTAMOS-********071034.csv (14,098 registros)
2025-06-07 07:10:34,434 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-BNACION-********071034.csv (55 registros)
2025-06-07 07:10:34,436 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-CRANDES-********071034.csv (6 registros)
2025-06-07 07:10:34,437 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-CCUSCO-********071034.csv (1 registros)
2025-06-07 07:10:34,437 - LogUsuariosPipeline - INFO - ✅ Procesamiento completado: 4 archivos generados
2025-06-07 07:10:34,438 - LogUsuariosPipeline - INFO - 📊 Total registros procesados: 14,160
2025-06-07 07:10:34,495 - LogUsuariosPipeline - INFO - ✅ Procesamiento exacto completado: 4 archivos
2025-06-07 07:10:34,495 - LogUsuariosPipeline - INFO -   📁 LOGUSR-FCOMPARTAMOS-********071034.csv
2025-06-07 07:10:34,495 - LogUsuariosPipeline - INFO -   📁 LOGUSR-BNACION-********071034.csv
2025-06-07 07:10:34,495 - LogUsuariosPipeline - INFO -   📁 LOGUSR-CRANDES-********071034.csv
2025-06-07 07:10:34,495 - LogUsuariosPipeline - INFO -   📁 LOGUSR-CCUSCO-********071034.csv
2025-06-07 07:10:34,496 - LogUsuariosPipeline - INFO - Archivo temporal eliminado: TEMP_LOGS_USUARIOS/********/WALLET_OLD.parquet
2025-06-07 07:10:34,496 - LogUsuariosPipeline - INFO - EXPORT-CSV: OK
2025-06-07 07:10:34,496 - LogUsuariosPipeline - INFO - === ARCHIVOS CSV GENERADOS ===
2025-06-07 07:10:34,496 - LogUsuariosPipeline - INFO - Total de archivos CSV segmentados: 11
2025-06-07 07:10:34,496 - LogUsuariosPipeline - INFO - Archivos por BankDomain (7) - EXACTO como flujo original:
2025-06-07 07:10:34,496 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-0231FCONFIANZA-********.csv
2025-06-07 07:10:34,496 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-BNACION-********.csv
2025-06-07 07:10:34,496 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-CCUSCO-********.csv
2025-06-07 07:10:34,496 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-CRANDES-********.csv
2025-06-07 07:10:34,496 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-FCOMPARTAMOS-********.csv
2025-06-07 07:10:34,496 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-FCONFIANZA-********.csv
2025-06-07 07:10:34,496 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-NULL-********.csv
2025-06-07 07:10:34,496 - LogUsuariosPipeline - INFO - Estructura OPTIMIZADA: solo archivos segmentados (datos completos en Parquet)
2025-06-07 07:10:34,496 - LogUsuariosPipeline - INFO - === PIPELINE COMPLETADO EXITOSAMENTE ===
2025-06-07 07:10:34,496 - LogUsuariosPipeline - INFO - 
=== ARCHIVOS GENERADOS ===
2025-06-07 07:10:34,496 - LogUsuariosPipeline - INFO - Archivos temporales en TEMP_LOGS_USUARIOS/********:
2025-06-07 07:10:34,496 - LogUsuariosPipeline - INFO -   - USER_ACCOUNT_HISTORY.parquet (0.00 MB)
2025-06-07 07:10:34,496 - LogUsuariosPipeline - INFO -   - USER_DATA_TRX.parquet (126.94 MB)
2025-06-07 07:10:34,496 - LogUsuariosPipeline - INFO -   - USER_MODIFICATION_DAY.parquet (3.68 MB)
2025-06-07 07:10:34,496 - LogUsuariosPipeline - INFO -   - USER_AUTH_CHANGE_HISTORY.parquet (0.13 MB)
2025-06-07 07:10:34,496 - LogUsuariosPipeline - INFO - Archivos de salida en output/********:
2025-06-07 07:10:34,496 - LogUsuariosPipeline - INFO -   - LOG_USR.parquet (4.73 MB)
2025-06-07 07:10:34,496 - LogUsuariosPipeline - INFO -   - LOG_USR_DEDUPLICATED.parquet (4.61 MB)
2025-06-07 07:10:34,497 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-0231FCONFIANZA-********.csv (0.00 MB)
2025-06-07 07:10:34,497 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-BNACION-********.csv (0.07 MB)
2025-06-07 07:10:34,497 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-CCUSCO-********.csv (0.00 MB)
2025-06-07 07:10:34,497 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-CRANDES-********.csv (0.01 MB)
2025-06-07 07:10:34,497 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-FCOMPARTAMOS-********.csv (33.63 MB)
2025-06-07 07:10:34,497 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-FCONFIANZA-********.csv (0.00 MB)
2025-06-07 07:10:34,497 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-NULL-********.csv (0.00 MB)
2025-06-07 07:10:34,497 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-FCOMPARTAMOS-********071034.csv (4.19 MB)
2025-06-07 07:10:34,497 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-BNACION-********071034.csv (0.01 MB)
2025-06-07 07:10:34,497 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-CRANDES-********071034.csv (0.00 MB)
2025-06-07 07:10:34,497 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-CCUSCO-********071034.csv (0.00 MB)
2025-06-07 07:28:48,741 - LogUsuariosPipeline - INFO - Configurando credenciales S3...
2025-06-07 07:28:48,753 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-07 07:28:48,807 - LogUsuariosPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-07 07:28:48,807 - LogUsuariosPipeline - INFO - === INICIANDO PIPELINE LOG_USUARIOS MODERNIZADO ===
2025-06-07 07:28:48,808 - LogUsuariosPipeline - INFO - Fecha de procesamiento: 2025-06-05 (carpeta: ********)
2025-06-07 07:28:48,808 - LogUsuariosPipeline - INFO - USER-MODIFY: INICIANDO
2025-06-07 07:28:48,808 - LogUsuariosPipeline - INFO - Extrayendo USER_ACCOUNT_HISTORY para fecha: 2025-06-05
2025-06-07 07:28:49,026 - LogUsuariosPipeline - INFO - USER_ACCOUNT_HISTORY extraído: 11 registros -> TEMP_LOGS_USUARIOS/********/USER_ACCOUNT_HISTORY.parquet
2025-06-07 07:28:49,026 - LogUsuariosPipeline - INFO - USER-MODIFY: OK
2025-06-07 07:28:49,026 - LogUsuariosPipeline - INFO - PRE-LOG-USR: INICIANDO
2025-06-07 07:28:49,026 - LogUsuariosPipeline - INFO - Ejecutando SP_PRE_LOG_USR CORREGIDO (incluye USER_IDs múltiples) para fecha: 2025-06-05
2025-06-07 07:28:58,010 - LogUsuariosPipeline - INFO - SP_PRE_LOG_USR completado: 1581374 registros -> TEMP_LOGS_USUARIOS/********/USER_DATA_TRX.parquet
2025-06-07 07:28:58,010 - LogUsuariosPipeline - INFO - Ejecutando SP_USER_MODIFICATION para fecha: 2025-06-05
2025-06-07 07:29:01,230 - LogUsuariosPipeline - INFO - SP_USER_MODIFICATION completado: 7438 registros -> TEMP_LOGS_USUARIOS/********/USER_MODIFICATION_DAY.parquet
2025-06-07 07:29:01,230 - LogUsuariosPipeline - INFO - Ejecutando SP_USER_AUTH_DAY para fecha: 2025-06-05
2025-06-07 07:29:01,984 - LogUsuariosPipeline - INFO - SP_USER_AUTH_DAY completado: 5804 registros -> TEMP_LOGS_USUARIOS/********/USER_AUTH_CHANGE_HISTORY.parquet
2025-06-07 07:29:01,984 - LogUsuariosPipeline - INFO - PRE-LOG-USR: OK
2025-06-07 07:29:01,984 - LogUsuariosPipeline - INFO - LOG-USR: INICIANDO
2025-06-07 07:29:01,984 - LogUsuariosPipeline - INFO - Ejecutando SP_LOG_USR EXACTO (igual a Oracle) para fecha: 2025-06-05
2025-06-07 07:29:03,926 - LogUsuariosPipeline - INFO - SP_LOG_USR EXACTO completado: 21392 registros -> output/********/LOG_USR.parquet
2025-06-07 07:29:03,927 - LogUsuariosPipeline - INFO - LOG-USR: OK
2025-06-07 07:29:03,927 - LogUsuariosPipeline - INFO - EXPORT-CSV: INICIANDO
2025-06-07 07:29:03,927 - LogUsuariosPipeline - INFO - Iniciando exportación CSV con conversiones de perfiles para fecha: 2025-06-05
2025-06-07 07:29:03,927 - LogUsuariosPipeline - INFO - Aplicando SOLO deduplicación como Oracle original (SIN conversión de tipos)
2025-06-07 07:29:04,235 - LogUsuariosPipeline - INFO - Deduplicación completada (manteniendo REQUESTTYPE originales):
2025-06-07 07:29:04,236 - LogUsuariosPipeline - INFO -   - Registros originales: 21,392
2025-06-07 07:29:04,236 - LogUsuariosPipeline - INFO -   - Registros después de deduplicación: 17,305
2025-06-07 07:29:04,236 - LogUsuariosPipeline - INFO -   - Duplicados eliminados: 4,087
2025-06-07 07:29:04,238 - LogUsuariosPipeline - INFO - Tipos de transacción mantenidos:
2025-06-07 07:29:04,238 - LogUsuariosPipeline - INFO -   - User Modification: 7,193 registros
2025-06-07 07:29:04,238 - LogUsuariosPipeline - INFO -   - CHANGE_AUTH_FACTOR: 5,677 registros
2025-06-07 07:29:04,238 - LogUsuariosPipeline - INFO -   - AfiliaUser: 1,877 registros
2025-06-07 07:29:04,238 - LogUsuariosPipeline - INFO -   - ActivateUser: 1,877 registros
2025-06-07 07:29:04,238 - LogUsuariosPipeline - INFO -   - ClosedUserAccount: 218 registros
2025-06-07 07:29:04,238 - LogUsuariosPipeline - INFO -   - ClosedAccount: 218 registros
2025-06-07 07:29:04,238 - LogUsuariosPipeline - INFO -   - Delete User: 218 registros
2025-06-07 07:29:04,238 - LogUsuariosPipeline - INFO -   - Lock Wallet: 18 registros
2025-06-07 07:29:04,238 - LogUsuariosPipeline - INFO -   - Resume User: 7 registros
2025-06-07 07:29:04,238 - LogUsuariosPipeline - INFO -   - Suspend User: 2 registros
2025-06-07 07:29:04,238 - LogUsuariosPipeline - INFO - Aplicando conversiones de perfiles al archivo: output/********/LOG_USR_DEDUPLICATED.parquet
2025-06-07 07:29:04,238 - LogUsuariosPipeline - INFO - Cargando tabla de conversión de perfiles...
2025-06-07 07:29:04,241 - LogUsuariosPipeline - INFO - Tabla conv_perfil.csv cargada: 796 registros
2025-06-07 07:29:04,245 - LogUsuariosPipeline - INFO - Tabla de conversión de perfiles cargada exitosamente en DuckDB
2025-06-07 07:29:04,247 - LogUsuariosPipeline - ERROR - Error aplicando conversiones al archivo: Binder Error: Values list "cp_a" does not have a column named "PERFIL_CODIGO"

LINE 8: ...            LEFT JOIN conv_perfil_table cp_a ON l.perfilA = cp_a.PERFIL_CODIGO
                                                                       ^
2025-06-07 07:29:04,247 - LogUsuariosPipeline - INFO - Omitiendo CSV principal redundante - usando solo archivos Parquet y CSVs segmentados
2025-06-07 07:29:04,247 - LogUsuariosPipeline - INFO - Iniciando exportación segmentada por BankDomain
2025-06-07 07:29:04,247 - LogUsuariosPipeline - INFO - Procesando TODOS los bancos del sistema: ['0231FCONFIANZA', 'BNACION', 'CCUSCO', 'CRANDES', 'FCOMPARTAMOS', 'FCONFIANZA', None]
2025-06-07 07:29:04,251 - LogUsuariosPipeline - INFO - Exportado dominio 0231FCONFIANZA: 0 registros (archivo vacío SIN headers) -> output/********/csv_exports/by_bank/LOG-USUARIOS-0231FCONFIANZA-********.csv
2025-06-07 07:29:04,274 - LogUsuariosPipeline - INFO - Exportado dominio BNACION: 87 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-BNACION-********.csv
2025-06-07 07:29:04,283 - LogUsuariosPipeline - INFO - Exportado dominio CCUSCO: 2 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-CCUSCO-********.csv
2025-06-07 07:29:04,312 - LogUsuariosPipeline - INFO - Exportado dominio CRANDES: 10 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-CRANDES-********.csv
2025-06-07 07:29:04,451 - LogUsuariosPipeline - INFO - Exportado dominio FCOMPARTAMOS: 21275 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-FCOMPARTAMOS-********.csv
2025-06-07 07:29:04,455 - LogUsuariosPipeline - INFO - Exportado dominio FCONFIANZA: 0 registros (archivo vacío SIN headers) -> output/********/csv_exports/by_bank/LOG-USUARIOS-FCONFIANZA-********.csv
2025-06-07 07:29:04,487 - LogUsuariosPipeline - INFO - Exportado dominio NULL: 18 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-NULL-********.csv
2025-06-07 07:29:04,487 - LogUsuariosPipeline - INFO - Exportación segmentada completada: 7 archivos generados
2025-06-07 07:29:04,487 - LogUsuariosPipeline - INFO - Estructura EXACTA como flujo original: solo by_bank (sin by_profile)
2025-06-07 07:29:04,487 - LogUsuariosPipeline - INFO - 🔄 APLICANDO PROCESAMIENTO EXACTO como procesar.py...
2025-06-07 07:29:04,491 - LogUsuariosPipeline - INFO - 🔄 INICIANDO PROCESAMIENTO EXACTO como procesar.py
2025-06-07 07:29:04,491 - LogUsuariosPipeline - INFO - 📁 Input: output/********/LOG_USR.parquet
2025-06-07 07:29:04,491 - LogUsuariosPipeline - INFO - 📅 Fecha: 2025-06-05
2025-06-07 07:29:04,491 - LogUsuariosPipeline - INFO - 📁 Output: output/********/csv_exports/csv_final_procesado
2025-06-07 07:29:04,491 - LogUsuariosPipeline - INFO - 📊 Cargando datos desde LOG_USR.parquet...
2025-06-07 07:29:04,562 - LogUsuariosPipeline - INFO - 📊 Registros cargados: 21,392
2025-06-07 07:29:04,562 - LogUsuariosPipeline - INFO - 🔄 Procesando grupos por USERHISTID...
2025-06-07 07:29:08,109 - LogUsuariosPipeline - INFO - 📊 Convirtiendo a DataFrame...
2025-06-07 07:29:08,237 - LogUsuariosPipeline - INFO - 📊 Registros después del procesamiento inicial: 51,976
2025-06-07 07:29:08,237 - LogUsuariosPipeline - INFO - 🔍 Aplicando filtro de valores válidos...
2025-06-07 07:29:08,245 - LogUsuariosPipeline - INFO - 📊 Registros después del filtro: 19,728
2025-06-07 07:29:08,245 - LogUsuariosPipeline - INFO - 🥷 Aplicando filtros adicionales de calidad de datos...
2025-06-07 07:29:08,254 - LogUsuariosPipeline - INFO -   🔍 Eliminados 18 registros con BANKDOMAIN inválido
2025-06-07 07:29:08,266 - LogUsuariosPipeline - INFO - 📊 Registros después de filtros de calidad: 19,710
2025-06-07 07:29:08,267 - LogUsuariosPipeline - INFO - 🔄 Agrupando y procesando JSON...
2025-06-07 07:29:11,561 - LogUsuariosPipeline - INFO - 📊 Registros después de eliminar duplicados: 14,144
2025-06-07 07:29:11,574 - LogUsuariosPipeline - INFO - 🔄 Aplicando asignaciones a columnas...
2025-06-07 07:29:12,664 - LogUsuariosPipeline - INFO - 📊 Cargando tabla de conversión de perfiles...
2025-06-07 07:29:12,666 - LogUsuariosPipeline - INFO - 📊 Tabla de conversión cargada: 796 registros
2025-06-07 07:29:13,036 - LogUsuariosPipeline - INFO - 🔄 Aplicando función crítica de expansión de registros...
2025-06-07 07:29:13,036 - LogUsuariosPipeline - INFO - 📊 Registros antes de expansión: 14,144
2025-06-07 07:29:13,655 - LogUsuariosPipeline - INFO - 📊 Registros después de expansión: 14,160
2025-06-07 07:29:13,655 - LogUsuariosPipeline - INFO - ✅ Función crítica de expansión aplicada exitosamente
2025-06-07 07:29:13,655 - LogUsuariosPipeline - INFO - 🔄 Aplicando mapeo de columnas...
2025-06-07 07:29:13,658 - LogUsuariosPipeline - INFO - 🔄 Procesando tipos de transacciones válidas...
2025-06-07 07:29:13,695 - LogUsuariosPipeline - INFO - 📊 Registros después de concatenar: 14,160
2025-06-07 07:29:30,546 - LogUsuariosPipeline - INFO - 📊 Registros finales para exportar: 14,160
2025-06-07 07:29:30,546 - LogUsuariosPipeline - INFO - 📁 Generando archivos CSV por BankDomain...
2025-06-07 07:29:30,609 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-FCOMPARTAMOS-********072930.csv (14,098 registros)
2025-06-07 07:29:30,613 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-BNACION-********072930.csv (55 registros)
2025-06-07 07:29:30,614 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-CRANDES-********072930.csv (6 registros)
2025-06-07 07:29:30,616 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-CCUSCO-********072930.csv (1 registros)
2025-06-07 07:29:30,616 - LogUsuariosPipeline - INFO - ✅ Procesamiento completado: 4 archivos generados
2025-06-07 07:29:30,616 - LogUsuariosPipeline - INFO - 📊 Total registros procesados: 14,160
2025-06-07 07:29:30,677 - LogUsuariosPipeline - INFO - ✅ Procesamiento exacto completado: 4 archivos
2025-06-07 07:29:30,677 - LogUsuariosPipeline - INFO -   📁 LOGUSR-FCOMPARTAMOS-********072930.csv
2025-06-07 07:29:30,677 - LogUsuariosPipeline - INFO -   📁 LOGUSR-BNACION-********072930.csv
2025-06-07 07:29:30,677 - LogUsuariosPipeline - INFO -   📁 LOGUSR-CRANDES-********072930.csv
2025-06-07 07:29:30,677 - LogUsuariosPipeline - INFO -   📁 LOGUSR-CCUSCO-********072930.csv
2025-06-07 07:29:30,677 - LogUsuariosPipeline - INFO - Archivo temporal eliminado: TEMP_LOGS_USUARIOS/********/WALLET_OLD.parquet
2025-06-07 07:29:30,678 - LogUsuariosPipeline - INFO - EXPORT-CSV: OK
2025-06-07 07:29:30,678 - LogUsuariosPipeline - INFO - === ARCHIVOS CSV GENERADOS ===
2025-06-07 07:29:30,678 - LogUsuariosPipeline - INFO - Total de archivos CSV segmentados: 11
2025-06-07 07:29:30,678 - LogUsuariosPipeline - INFO - Archivos por BankDomain (7) - EXACTO como flujo original:
2025-06-07 07:29:30,678 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-0231FCONFIANZA-********.csv
2025-06-07 07:29:30,678 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-BNACION-********.csv
2025-06-07 07:29:30,678 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-CCUSCO-********.csv
2025-06-07 07:29:30,678 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-CRANDES-********.csv
2025-06-07 07:29:30,678 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-FCOMPARTAMOS-********.csv
2025-06-07 07:29:30,678 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-FCONFIANZA-********.csv
2025-06-07 07:29:30,678 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-NULL-********.csv
2025-06-07 07:29:30,678 - LogUsuariosPipeline - INFO - Estructura OPTIMIZADA: solo archivos segmentados (datos completos en Parquet)
2025-06-07 07:29:30,678 - LogUsuariosPipeline - INFO - === PIPELINE COMPLETADO EXITOSAMENTE ===
2025-06-07 07:29:30,678 - LogUsuariosPipeline - INFO - 
=== ARCHIVOS GENERADOS ===
2025-06-07 07:29:30,678 - LogUsuariosPipeline - INFO - Archivos temporales en TEMP_LOGS_USUARIOS/********:
2025-06-07 07:29:30,678 - LogUsuariosPipeline - INFO -   - USER_ACCOUNT_HISTORY.parquet (0.00 MB)
2025-06-07 07:29:30,678 - LogUsuariosPipeline - INFO -   - USER_DATA_TRX.parquet (126.94 MB)
2025-06-07 07:29:30,678 - LogUsuariosPipeline - INFO -   - USER_MODIFICATION_DAY.parquet (3.68 MB)
2025-06-07 07:29:30,678 - LogUsuariosPipeline - INFO -   - USER_AUTH_CHANGE_HISTORY.parquet (0.13 MB)
2025-06-07 07:29:30,678 - LogUsuariosPipeline - INFO - Archivos de salida en output/********:
2025-06-07 07:29:30,678 - LogUsuariosPipeline - INFO -   - LOG_USR.parquet (4.73 MB)
2025-06-07 07:29:30,678 - LogUsuariosPipeline - INFO -   - LOG_USR_DEDUPLICATED.parquet (4.61 MB)
2025-06-07 07:29:30,678 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-0231FCONFIANZA-********.csv (0.00 MB)
2025-06-07 07:29:30,679 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-BNACION-********.csv (0.07 MB)
2025-06-07 07:29:30,679 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-CCUSCO-********.csv (0.00 MB)
2025-06-07 07:29:30,679 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-CRANDES-********.csv (0.01 MB)
2025-06-07 07:29:30,679 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-FCOMPARTAMOS-********.csv (33.63 MB)
2025-06-07 07:29:30,679 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-FCONFIANZA-********.csv (0.00 MB)
2025-06-07 07:29:30,679 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-NULL-********.csv (0.00 MB)
2025-06-07 07:29:30,679 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-FCOMPARTAMOS-********072930.csv (1.78 MB)
2025-06-07 07:29:30,679 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-BNACION-********072930.csv (0.01 MB)
2025-06-07 07:29:30,679 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-CRANDES-********072930.csv (0.00 MB)
2025-06-07 07:29:30,679 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-CCUSCO-********072930.csv (0.00 MB)
2025-06-07 07:40:01,530 - LogUsuariosPipeline - INFO - Configurando credenciales S3...
2025-06-07 07:40:01,542 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-07 07:40:01,592 - LogUsuariosPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-07 07:40:01,593 - LogUsuariosPipeline - INFO - === INICIANDO PIPELINE LOG_USUARIOS MODERNIZADO ===
2025-06-07 07:40:01,593 - LogUsuariosPipeline - INFO - Fecha de procesamiento: 2025-06-05 (carpeta: ********)
2025-06-07 07:40:01,594 - LogUsuariosPipeline - INFO - USER-MODIFY: INICIANDO
2025-06-07 07:40:01,594 - LogUsuariosPipeline - INFO - Extrayendo USER_ACCOUNT_HISTORY para fecha: 2025-06-05
2025-06-07 07:40:01,735 - LogUsuariosPipeline - INFO - USER_ACCOUNT_HISTORY extraído: 11 registros -> TEMP_LOGS_USUARIOS/********/USER_ACCOUNT_HISTORY.parquet
2025-06-07 07:40:01,735 - LogUsuariosPipeline - INFO - USER-MODIFY: OK
2025-06-07 07:40:01,736 - LogUsuariosPipeline - INFO - PRE-LOG-USR: INICIANDO
2025-06-07 07:40:01,736 - LogUsuariosPipeline - INFO - Ejecutando SP_PRE_LOG_USR CORREGIDO (incluye USER_IDs múltiples) para fecha: 2025-06-05
2025-06-07 07:40:10,348 - LogUsuariosPipeline - INFO - SP_PRE_LOG_USR completado: 1581374 registros -> TEMP_LOGS_USUARIOS/********/USER_DATA_TRX.parquet
2025-06-07 07:40:10,348 - LogUsuariosPipeline - INFO - Ejecutando SP_USER_MODIFICATION para fecha: 2025-06-05
2025-06-07 07:40:13,730 - LogUsuariosPipeline - INFO - SP_USER_MODIFICATION completado: 7438 registros -> TEMP_LOGS_USUARIOS/********/USER_MODIFICATION_DAY.parquet
2025-06-07 07:40:13,730 - LogUsuariosPipeline - INFO - Ejecutando SP_USER_AUTH_DAY para fecha: 2025-06-05
2025-06-07 07:40:14,379 - LogUsuariosPipeline - INFO - SP_USER_AUTH_DAY completado: 5804 registros -> TEMP_LOGS_USUARIOS/********/USER_AUTH_CHANGE_HISTORY.parquet
2025-06-07 07:40:14,379 - LogUsuariosPipeline - INFO - PRE-LOG-USR: OK
2025-06-07 07:40:14,379 - LogUsuariosPipeline - INFO - LOG-USR: INICIANDO
2025-06-07 07:40:14,379 - LogUsuariosPipeline - INFO - Ejecutando SP_LOG_USR EXACTO (igual a Oracle) para fecha: 2025-06-05
2025-06-07 07:40:16,181 - LogUsuariosPipeline - INFO - SP_LOG_USR EXACTO completado: 21392 registros -> output/********/LOG_USR.parquet
2025-06-07 07:40:16,182 - LogUsuariosPipeline - INFO - LOG-USR: OK
2025-06-07 07:40:16,182 - LogUsuariosPipeline - INFO - EXPORT-CSV: INICIANDO
2025-06-07 07:40:16,182 - LogUsuariosPipeline - INFO - Iniciando exportación CSV con conversiones de perfiles para fecha: 2025-06-05
2025-06-07 07:40:16,182 - LogUsuariosPipeline - INFO - Aplicando SOLO deduplicación como Oracle original (SIN conversión de tipos)
2025-06-07 07:40:16,471 - LogUsuariosPipeline - INFO - Deduplicación completada (manteniendo REQUESTTYPE originales):
2025-06-07 07:40:16,471 - LogUsuariosPipeline - INFO -   - Registros originales: 21,392
2025-06-07 07:40:16,471 - LogUsuariosPipeline - INFO -   - Registros después de deduplicación: 17,305
2025-06-07 07:40:16,471 - LogUsuariosPipeline - INFO -   - Duplicados eliminados: 4,087
2025-06-07 07:40:16,474 - LogUsuariosPipeline - INFO - Tipos de transacción mantenidos:
2025-06-07 07:40:16,474 - LogUsuariosPipeline - INFO -   - User Modification: 7,193 registros
2025-06-07 07:40:16,474 - LogUsuariosPipeline - INFO -   - CHANGE_AUTH_FACTOR: 5,677 registros
2025-06-07 07:40:16,474 - LogUsuariosPipeline - INFO -   - ActivateUser: 1,877 registros
2025-06-07 07:40:16,474 - LogUsuariosPipeline - INFO -   - AfiliaUser: 1,877 registros
2025-06-07 07:40:16,474 - LogUsuariosPipeline - INFO -   - Delete User: 218 registros
2025-06-07 07:40:16,474 - LogUsuariosPipeline - INFO -   - ClosedUserAccount: 218 registros
2025-06-07 07:40:16,474 - LogUsuariosPipeline - INFO -   - ClosedAccount: 218 registros
2025-06-07 07:40:16,474 - LogUsuariosPipeline - INFO -   - Lock Wallet: 18 registros
2025-06-07 07:40:16,474 - LogUsuariosPipeline - INFO -   - Resume User: 7 registros
2025-06-07 07:40:16,474 - LogUsuariosPipeline - INFO -   - Suspend User: 2 registros
2025-06-07 07:40:16,474 - LogUsuariosPipeline - INFO - Aplicando conversiones de perfiles al archivo: output/********/LOG_USR_DEDUPLICATED.parquet
2025-06-07 07:40:16,474 - LogUsuariosPipeline - INFO - Cargando tabla de conversión de perfiles...
2025-06-07 07:40:16,477 - LogUsuariosPipeline - INFO - Tabla conv_perfil.csv cargada: 796 registros
2025-06-07 07:40:16,481 - LogUsuariosPipeline - INFO - Tabla de conversión de perfiles cargada exitosamente en DuckDB
2025-06-07 07:40:16,483 - LogUsuariosPipeline - ERROR - Error aplicando conversiones al archivo: Binder Error: Values list "cp_a" does not have a column named "PERFIL_CODIGO"

LINE 8: ...            LEFT JOIN conv_perfil_table cp_a ON l.perfilA = cp_a.PERFIL_CODIGO
                                                                       ^
2025-06-07 07:40:16,484 - LogUsuariosPipeline - INFO - Omitiendo CSV principal redundante - usando solo archivos Parquet y CSVs segmentados
2025-06-07 07:40:16,484 - LogUsuariosPipeline - INFO - Iniciando exportación segmentada por BankDomain
2025-06-07 07:40:16,484 - LogUsuariosPipeline - INFO - Procesando TODOS los bancos del sistema: ['0231FCONFIANZA', 'BNACION', 'CCUSCO', 'CRANDES', 'FCOMPARTAMOS', 'FCONFIANZA', None]
2025-06-07 07:40:16,488 - LogUsuariosPipeline - INFO - Exportado dominio 0231FCONFIANZA: 0 registros (archivo vacío SIN headers) -> output/********/csv_exports/by_bank/LOG-USUARIOS-0231FCONFIANZA-********.csv
2025-06-07 07:40:16,516 - LogUsuariosPipeline - INFO - Exportado dominio BNACION: 87 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-BNACION-********.csv
2025-06-07 07:40:16,522 - LogUsuariosPipeline - INFO - Exportado dominio CCUSCO: 2 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-CCUSCO-********.csv
2025-06-07 07:40:16,548 - LogUsuariosPipeline - INFO - Exportado dominio CRANDES: 10 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-CRANDES-********.csv
2025-06-07 07:40:16,677 - LogUsuariosPipeline - INFO - Exportado dominio FCOMPARTAMOS: 21275 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-FCOMPARTAMOS-********.csv
2025-06-07 07:40:16,682 - LogUsuariosPipeline - INFO - Exportado dominio FCONFIANZA: 0 registros (archivo vacío SIN headers) -> output/********/csv_exports/by_bank/LOG-USUARIOS-FCONFIANZA-********.csv
2025-06-07 07:40:16,720 - LogUsuariosPipeline - INFO - Exportado dominio NULL: 18 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-NULL-********.csv
2025-06-07 07:40:16,720 - LogUsuariosPipeline - INFO - Exportación segmentada completada: 7 archivos generados
2025-06-07 07:40:16,720 - LogUsuariosPipeline - INFO - Estructura EXACTA como flujo original: solo by_bank (sin by_profile)
2025-06-07 07:40:16,720 - LogUsuariosPipeline - INFO - 🔄 APLICANDO PROCESAMIENTO EXACTO como procesar.py...
2025-06-07 07:40:16,725 - LogUsuariosPipeline - INFO - 🔄 INICIANDO PROCESAMIENTO EXACTO como procesar.py
2025-06-07 07:40:16,725 - LogUsuariosPipeline - INFO - 📁 Input: output/********/LOG_USR.parquet
2025-06-07 07:40:16,725 - LogUsuariosPipeline - INFO - 📅 Fecha: 2025-06-05
2025-06-07 07:40:16,725 - LogUsuariosPipeline - INFO - 📁 Output: output/********/csv_exports/csv_final_procesado
2025-06-07 07:40:16,725 - LogUsuariosPipeline - INFO - 📊 Cargando datos desde LOG_USR.parquet...
2025-06-07 07:40:16,798 - LogUsuariosPipeline - INFO - 📊 Registros cargados: 21,392
2025-06-07 07:40:16,798 - LogUsuariosPipeline - INFO - 🔄 Procesando grupos por USERHISTID...
2025-06-07 07:40:20,340 - LogUsuariosPipeline - INFO - 📊 Convirtiendo a DataFrame...
2025-06-07 07:40:20,470 - LogUsuariosPipeline - INFO - 📊 Registros después del procesamiento inicial: 51,976
2025-06-07 07:40:20,470 - LogUsuariosPipeline - INFO - 🔍 Aplicando filtro de valores válidos...
2025-06-07 07:40:20,479 - LogUsuariosPipeline - INFO - 📊 Registros después del filtro: 19,728
2025-06-07 07:40:20,479 - LogUsuariosPipeline - INFO - 🥷 Aplicando filtros adicionales de calidad de datos...
2025-06-07 07:40:20,488 - LogUsuariosPipeline - INFO -   🔍 Eliminados 18 registros con BANKDOMAIN inválido
2025-06-07 07:40:20,500 - LogUsuariosPipeline - INFO - 📊 Registros después de filtros de calidad: 19,710
2025-06-07 07:40:20,500 - LogUsuariosPipeline - INFO - 🔄 Agrupando y procesando JSON...
2025-06-07 07:40:23,808 - LogUsuariosPipeline - INFO - 📊 Registros después de eliminar duplicados: 14,144
2025-06-07 07:40:23,821 - LogUsuariosPipeline - INFO - 🔄 Aplicando asignaciones a columnas...
2025-06-07 07:40:24,920 - LogUsuariosPipeline - INFO - 📊 Cargando tabla de conversión de perfiles...
2025-06-07 07:40:24,921 - LogUsuariosPipeline - INFO - 📊 Tabla de conversión cargada: 796 registros
2025-06-07 07:40:25,283 - LogUsuariosPipeline - INFO - 🔄 Aplicando función crítica de expansión de registros...
2025-06-07 07:40:25,283 - LogUsuariosPipeline - INFO - 📊 Registros antes de expansión: 14,144
2025-06-07 07:40:25,914 - LogUsuariosPipeline - INFO - 📊 Registros después de expansión: 14,160
2025-06-07 07:40:25,914 - LogUsuariosPipeline - INFO - ✅ Función crítica de expansión aplicada exitosamente
2025-06-07 07:40:25,914 - LogUsuariosPipeline - INFO - 🔄 Aplicando mapeo de columnas...
2025-06-07 07:40:25,916 - LogUsuariosPipeline - INFO - 🔄 Procesando tipos de transacciones válidas...
2025-06-07 07:40:25,957 - LogUsuariosPipeline - INFO - 📊 Registros después de concatenar: 14,160
2025-06-07 07:40:42,771 - LogUsuariosPipeline - INFO - 📊 Registros finales para exportar: 14,160
2025-06-07 07:40:42,771 - LogUsuariosPipeline - INFO - 📁 Generando archivos CSV por BankDomain...
2025-06-07 07:40:42,847 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-FCOMPARTAMOS-********074042.csv (14,098 registros)
2025-06-07 07:40:42,850 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-BNACION-********074042.csv (55 registros)
2025-06-07 07:40:42,852 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-CRANDES-********074042.csv (6 registros)
2025-06-07 07:40:42,853 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-CCUSCO-********074042.csv (1 registros)
2025-06-07 07:40:42,853 - LogUsuariosPipeline - INFO - ✅ Procesamiento completado: 4 archivos generados
2025-06-07 07:40:42,853 - LogUsuariosPipeline - INFO - 📊 Total registros procesados: 14,160
2025-06-07 07:40:42,909 - LogUsuariosPipeline - INFO - ✅ Procesamiento exacto completado: 4 archivos
2025-06-07 07:40:42,910 - LogUsuariosPipeline - INFO -   📁 LOGUSR-FCOMPARTAMOS-********074042.csv
2025-06-07 07:40:42,910 - LogUsuariosPipeline - INFO -   📁 LOGUSR-BNACION-********074042.csv
2025-06-07 07:40:42,910 - LogUsuariosPipeline - INFO -   📁 LOGUSR-CRANDES-********074042.csv
2025-06-07 07:40:42,910 - LogUsuariosPipeline - INFO -   📁 LOGUSR-CCUSCO-********074042.csv
2025-06-07 07:40:42,910 - LogUsuariosPipeline - INFO - Archivo temporal eliminado: TEMP_LOGS_USUARIOS/********/WALLET_OLD.parquet
2025-06-07 07:40:42,910 - LogUsuariosPipeline - INFO - EXPORT-CSV: OK
2025-06-07 07:40:42,910 - LogUsuariosPipeline - INFO - === ARCHIVOS CSV GENERADOS ===
2025-06-07 07:40:42,910 - LogUsuariosPipeline - INFO - Total de archivos CSV segmentados: 11
2025-06-07 07:40:42,910 - LogUsuariosPipeline - INFO - Archivos por BankDomain (7) - EXACTO como flujo original:
2025-06-07 07:40:42,910 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-0231FCONFIANZA-********.csv
2025-06-07 07:40:42,910 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-BNACION-********.csv
2025-06-07 07:40:42,910 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-CCUSCO-********.csv
2025-06-07 07:40:42,910 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-CRANDES-********.csv
2025-06-07 07:40:42,910 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-FCOMPARTAMOS-********.csv
2025-06-07 07:40:42,910 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-FCONFIANZA-********.csv
2025-06-07 07:40:42,910 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-NULL-********.csv
2025-06-07 07:40:42,910 - LogUsuariosPipeline - INFO - Estructura OPTIMIZADA: solo archivos segmentados (datos completos en Parquet)
2025-06-07 07:40:42,910 - LogUsuariosPipeline - INFO - === PIPELINE COMPLETADO EXITOSAMENTE ===
2025-06-07 07:40:42,910 - LogUsuariosPipeline - INFO - 
=== ARCHIVOS GENERADOS ===
2025-06-07 07:40:42,910 - LogUsuariosPipeline - INFO - Archivos temporales en TEMP_LOGS_USUARIOS/********:
2025-06-07 07:40:42,911 - LogUsuariosPipeline - INFO -   - USER_ACCOUNT_HISTORY.parquet (0.00 MB)
2025-06-07 07:40:42,911 - LogUsuariosPipeline - INFO -   - USER_DATA_TRX.parquet (126.94 MB)
2025-06-07 07:40:42,911 - LogUsuariosPipeline - INFO -   - USER_MODIFICATION_DAY.parquet (3.68 MB)
2025-06-07 07:40:42,911 - LogUsuariosPipeline - INFO -   - USER_AUTH_CHANGE_HISTORY.parquet (0.13 MB)
2025-06-07 07:40:42,911 - LogUsuariosPipeline - INFO - Archivos de salida en output/********:
2025-06-07 07:40:42,911 - LogUsuariosPipeline - INFO -   - LOG_USR.parquet (4.73 MB)
2025-06-07 07:40:42,911 - LogUsuariosPipeline - INFO -   - LOG_USR_DEDUPLICATED.parquet (4.61 MB)
2025-06-07 07:40:42,911 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-0231FCONFIANZA-********.csv (0.00 MB)
2025-06-07 07:40:42,911 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-BNACION-********.csv (0.07 MB)
2025-06-07 07:40:42,911 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-CCUSCO-********.csv (0.00 MB)
2025-06-07 07:40:42,911 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-CRANDES-********.csv (0.01 MB)
2025-06-07 07:40:42,911 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-FCOMPARTAMOS-********.csv (33.63 MB)
2025-06-07 07:40:42,911 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-FCONFIANZA-********.csv (0.00 MB)
2025-06-07 07:40:42,911 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-NULL-********.csv (0.00 MB)
2025-06-07 07:40:42,911 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-FCOMPARTAMOS-********074042.csv (2.18 MB)
2025-06-07 07:40:42,911 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-BNACION-********074042.csv (0.01 MB)
2025-06-07 07:40:42,911 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-CRANDES-********074042.csv (0.00 MB)
2025-06-07 07:40:42,911 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-CCUSCO-********074042.csv (0.00 MB)
2025-06-07 07:49:14,384 - LogUsuariosPipeline - INFO - Configurando credenciales S3...
2025-06-07 07:49:14,396 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-07 07:49:14,445 - LogUsuariosPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-07 07:49:14,446 - LogUsuariosPipeline - INFO - === INICIANDO PIPELINE LOG_USUARIOS MODERNIZADO ===
2025-06-07 07:49:14,446 - LogUsuariosPipeline - INFO - Fecha de procesamiento: 2025-06-05 (carpeta: ********)
2025-06-07 07:49:14,446 - LogUsuariosPipeline - INFO - USER-MODIFY: INICIANDO
2025-06-07 07:49:14,446 - LogUsuariosPipeline - INFO - Extrayendo USER_ACCOUNT_HISTORY para fecha: 2025-06-05
2025-06-07 07:49:14,583 - LogUsuariosPipeline - INFO - USER_ACCOUNT_HISTORY extraído: 11 registros -> TEMP_LOGS_USUARIOS/********/USER_ACCOUNT_HISTORY.parquet
2025-06-07 07:49:14,583 - LogUsuariosPipeline - INFO - USER-MODIFY: OK
2025-06-07 07:49:14,583 - LogUsuariosPipeline - INFO - PRE-LOG-USR: INICIANDO
2025-06-07 07:49:14,583 - LogUsuariosPipeline - INFO - Ejecutando SP_PRE_LOG_USR CORREGIDO (incluye USER_IDs múltiples) para fecha: 2025-06-05
2025-06-07 07:49:23,146 - LogUsuariosPipeline - INFO - SP_PRE_LOG_USR completado: 1581374 registros -> TEMP_LOGS_USUARIOS/********/USER_DATA_TRX.parquet
2025-06-07 07:49:23,146 - LogUsuariosPipeline - INFO - Ejecutando SP_USER_MODIFICATION para fecha: 2025-06-05
2025-06-07 07:49:26,091 - LogUsuariosPipeline - INFO - SP_USER_MODIFICATION completado: 7438 registros -> TEMP_LOGS_USUARIOS/********/USER_MODIFICATION_DAY.parquet
2025-06-07 07:49:26,091 - LogUsuariosPipeline - INFO - Ejecutando SP_USER_AUTH_DAY para fecha: 2025-06-05
2025-06-07 07:49:26,652 - LogUsuariosPipeline - INFO - SP_USER_AUTH_DAY completado: 5804 registros -> TEMP_LOGS_USUARIOS/********/USER_AUTH_CHANGE_HISTORY.parquet
2025-06-07 07:49:26,652 - LogUsuariosPipeline - INFO - PRE-LOG-USR: OK
2025-06-07 07:49:26,652 - LogUsuariosPipeline - INFO - LOG-USR: INICIANDO
2025-06-07 07:49:26,652 - LogUsuariosPipeline - INFO - Ejecutando SP_LOG_USR EXACTO (igual a Oracle) para fecha: 2025-06-05
2025-06-07 07:49:28,547 - LogUsuariosPipeline - INFO - SP_LOG_USR EXACTO completado: 21392 registros -> output/********/LOG_USR.parquet
2025-06-07 07:49:28,548 - LogUsuariosPipeline - INFO - LOG-USR: OK
2025-06-07 07:49:28,548 - LogUsuariosPipeline - INFO - EXPORT-CSV: INICIANDO
2025-06-07 07:49:28,548 - LogUsuariosPipeline - INFO - Iniciando exportación CSV con conversiones de perfiles para fecha: 2025-06-05
2025-06-07 07:49:28,549 - LogUsuariosPipeline - INFO - Aplicando SOLO deduplicación como Oracle original (SIN conversión de tipos)
2025-06-07 07:49:28,856 - LogUsuariosPipeline - INFO - Deduplicación completada (manteniendo REQUESTTYPE originales):
2025-06-07 07:49:28,856 - LogUsuariosPipeline - INFO -   - Registros originales: 21,392
2025-06-07 07:49:28,856 - LogUsuariosPipeline - INFO -   - Registros después de deduplicación: 17,305
2025-06-07 07:49:28,856 - LogUsuariosPipeline - INFO -   - Duplicados eliminados: 4,087
2025-06-07 07:49:28,860 - LogUsuariosPipeline - INFO - Tipos de transacción mantenidos:
2025-06-07 07:49:28,860 - LogUsuariosPipeline - INFO -   - User Modification: 7,193 registros
2025-06-07 07:49:28,860 - LogUsuariosPipeline - INFO -   - CHANGE_AUTH_FACTOR: 5,677 registros
2025-06-07 07:49:28,860 - LogUsuariosPipeline - INFO -   - AfiliaUser: 1,877 registros
2025-06-07 07:49:28,860 - LogUsuariosPipeline - INFO -   - ActivateUser: 1,877 registros
2025-06-07 07:49:28,860 - LogUsuariosPipeline - INFO -   - ClosedAccount: 218 registros
2025-06-07 07:49:28,860 - LogUsuariosPipeline - INFO -   - ClosedUserAccount: 218 registros
2025-06-07 07:49:28,860 - LogUsuariosPipeline - INFO -   - Delete User: 218 registros
2025-06-07 07:49:28,860 - LogUsuariosPipeline - INFO -   - Lock Wallet: 18 registros
2025-06-07 07:49:28,860 - LogUsuariosPipeline - INFO -   - Resume User: 7 registros
2025-06-07 07:49:28,860 - LogUsuariosPipeline - INFO -   - Suspend User: 2 registros
2025-06-07 07:49:28,860 - LogUsuariosPipeline - INFO - Aplicando conversiones de perfiles al archivo: output/********/LOG_USR_DEDUPLICATED.parquet
2025-06-07 07:49:28,860 - LogUsuariosPipeline - INFO - Cargando tabla de conversión de perfiles...
2025-06-07 07:49:28,863 - LogUsuariosPipeline - INFO - Tabla conv_perfil.csv cargada: 796 registros
2025-06-07 07:49:28,867 - LogUsuariosPipeline - INFO - Tabla de conversión de perfiles cargada exitosamente en DuckDB
2025-06-07 07:49:28,869 - LogUsuariosPipeline - ERROR - Error aplicando conversiones al archivo: Binder Error: Values list "cp_a" does not have a column named "PERFIL_CODIGO"

LINE 8: ...            LEFT JOIN conv_perfil_table cp_a ON l.perfilA = cp_a.PERFIL_CODIGO
                                                                       ^
2025-06-07 07:49:28,870 - LogUsuariosPipeline - INFO - Omitiendo CSV principal redundante - usando solo archivos Parquet y CSVs segmentados
2025-06-07 07:49:28,870 - LogUsuariosPipeline - INFO - Iniciando exportación segmentada por BankDomain
2025-06-07 07:49:28,870 - LogUsuariosPipeline - INFO - Procesando TODOS los bancos del sistema: ['0231FCONFIANZA', 'BNACION', 'CCUSCO', 'CRANDES', 'FCOMPARTAMOS', 'FCONFIANZA', None]
2025-06-07 07:49:28,874 - LogUsuariosPipeline - INFO - Exportado dominio 0231FCONFIANZA: 0 registros (archivo vacío SIN headers) -> output/********/csv_exports/by_bank/LOG-USUARIOS-0231FCONFIANZA-********.csv
2025-06-07 07:49:28,899 - LogUsuariosPipeline - INFO - Exportado dominio BNACION: 87 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-BNACION-********.csv
2025-06-07 07:49:28,907 - LogUsuariosPipeline - INFO - Exportado dominio CCUSCO: 2 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-CCUSCO-********.csv
2025-06-07 07:49:28,936 - LogUsuariosPipeline - INFO - Exportado dominio CRANDES: 10 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-CRANDES-********.csv
2025-06-07 07:49:29,070 - LogUsuariosPipeline - INFO - Exportado dominio FCOMPARTAMOS: 21275 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-FCOMPARTAMOS-********.csv
2025-06-07 07:49:29,075 - LogUsuariosPipeline - INFO - Exportado dominio FCONFIANZA: 0 registros (archivo vacío SIN headers) -> output/********/csv_exports/by_bank/LOG-USUARIOS-FCONFIANZA-********.csv
2025-06-07 07:49:29,107 - LogUsuariosPipeline - INFO - Exportado dominio NULL: 18 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-NULL-********.csv
2025-06-07 07:49:29,108 - LogUsuariosPipeline - INFO - Exportación segmentada completada: 7 archivos generados
2025-06-07 07:49:29,108 - LogUsuariosPipeline - INFO - Estructura EXACTA como flujo original: solo by_bank (sin by_profile)
2025-06-07 07:49:29,108 - LogUsuariosPipeline - INFO - 🔄 APLICANDO PROCESAMIENTO EXACTO como procesar.py...
2025-06-07 07:49:29,112 - LogUsuariosPipeline - INFO - 🔄 INICIANDO PROCESAMIENTO EXACTO como procesar.py
2025-06-07 07:49:29,112 - LogUsuariosPipeline - INFO - 📁 Input: output/********/LOG_USR.parquet
2025-06-07 07:49:29,112 - LogUsuariosPipeline - INFO - 📅 Fecha: 2025-06-05
2025-06-07 07:49:29,112 - LogUsuariosPipeline - INFO - 📁 Output: output/********/csv_exports/csv_final_procesado
2025-06-07 07:49:29,112 - LogUsuariosPipeline - INFO - 📊 Cargando datos desde LOG_USR.parquet...
2025-06-07 07:49:29,182 - LogUsuariosPipeline - INFO - 📊 Registros cargados: 21,392
2025-06-07 07:49:29,182 - LogUsuariosPipeline - INFO - 🔄 Procesando grupos por USERHISTID...
2025-06-07 07:49:32,662 - LogUsuariosPipeline - INFO - 📊 Convirtiendo a DataFrame...
2025-06-07 07:49:32,795 - LogUsuariosPipeline - INFO - 📊 Registros después del procesamiento inicial: 51,976
2025-06-07 07:49:32,796 - LogUsuariosPipeline - INFO - 🔍 Aplicando filtro de valores válidos...
2025-06-07 07:49:32,804 - LogUsuariosPipeline - INFO - 📊 Registros después del filtro: 19,728
2025-06-07 07:49:32,805 - LogUsuariosPipeline - INFO - 🥷 Aplicando filtros adicionales de calidad de datos...
2025-06-07 07:49:32,814 - LogUsuariosPipeline - INFO -   🔍 Eliminados 18 registros con BANKDOMAIN inválido
2025-06-07 07:49:32,826 - LogUsuariosPipeline - INFO - 📊 Registros después de filtros de calidad: 19,710
2025-06-07 07:49:32,826 - LogUsuariosPipeline - INFO - 🔄 Agrupando y procesando JSON...
2025-06-07 07:49:36,104 - LogUsuariosPipeline - INFO - 📊 Registros después de eliminar duplicados: 14,144
2025-06-07 07:49:36,116 - LogUsuariosPipeline - INFO - 🔄 Aplicando asignaciones a columnas...
2025-06-07 07:49:37,215 - LogUsuariosPipeline - INFO - 📊 Cargando tabla de conversión de perfiles...
2025-06-07 07:49:37,217 - LogUsuariosPipeline - INFO - 📊 Tabla de conversión cargada: 796 registros
2025-06-07 07:49:37,580 - LogUsuariosPipeline - INFO - 🔄 Aplicando función crítica de expansión de registros...
2025-06-07 07:49:37,580 - LogUsuariosPipeline - INFO - 📊 Registros antes de expansión: 14,144
2025-06-07 07:49:38,175 - LogUsuariosPipeline - INFO - 📊 Registros después de expansión: 14,160
2025-06-07 07:49:38,176 - LogUsuariosPipeline - INFO - ✅ Función crítica de expansión aplicada exitosamente
2025-06-07 07:49:38,176 - LogUsuariosPipeline - INFO - 🔄 Aplicando mapeo de columnas...
2025-06-07 07:49:38,178 - LogUsuariosPipeline - INFO - 🔄 Procesando tipos de transacciones válidas...
2025-06-07 07:49:38,217 - LogUsuariosPipeline - INFO - 📊 Registros después de concatenar: 14,160
2025-06-07 07:49:55,004 - LogUsuariosPipeline - INFO - 📊 Registros finales para exportar: 14,160
2025-06-07 07:49:55,004 - LogUsuariosPipeline - INFO - 📁 Generando archivos CSV por BankDomain...
2025-06-07 07:49:55,077 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-FCOMPARTAMOS-********074955.csv (14,098 registros)
2025-06-07 07:49:55,081 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-BNACION-********074955.csv (55 registros)
2025-06-07 07:49:55,084 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-CRANDES-********074955.csv (6 registros)
2025-06-07 07:49:55,086 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-CCUSCO-********074955.csv (1 registros)
2025-06-07 07:49:55,086 - LogUsuariosPipeline - INFO - ✅ Procesamiento completado: 4 archivos generados
2025-06-07 07:49:55,086 - LogUsuariosPipeline - INFO - 📊 Total registros procesados: 14,160
2025-06-07 07:49:55,168 - LogUsuariosPipeline - INFO - ✅ Procesamiento exacto completado: 4 archivos
2025-06-07 07:49:55,168 - LogUsuariosPipeline - INFO -   📁 LOGUSR-FCOMPARTAMOS-********074955.csv
2025-06-07 07:49:55,168 - LogUsuariosPipeline - INFO -   📁 LOGUSR-BNACION-********074955.csv
2025-06-07 07:49:55,168 - LogUsuariosPipeline - INFO -   📁 LOGUSR-CRANDES-********074955.csv
2025-06-07 07:49:55,168 - LogUsuariosPipeline - INFO -   📁 LOGUSR-CCUSCO-********074955.csv
2025-06-07 07:49:55,168 - LogUsuariosPipeline - INFO - Archivo temporal eliminado: TEMP_LOGS_USUARIOS/********/WALLET_OLD.parquet
2025-06-07 07:49:55,169 - LogUsuariosPipeline - INFO - EXPORT-CSV: OK
2025-06-07 07:49:55,169 - LogUsuariosPipeline - INFO - === ARCHIVOS CSV GENERADOS ===
2025-06-07 07:49:55,169 - LogUsuariosPipeline - INFO - Total de archivos CSV segmentados: 11
2025-06-07 07:49:55,169 - LogUsuariosPipeline - INFO - Archivos por BankDomain (7) - EXACTO como flujo original:
2025-06-07 07:49:55,169 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-0231FCONFIANZA-********.csv
2025-06-07 07:49:55,169 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-BNACION-********.csv
2025-06-07 07:49:55,169 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-CCUSCO-********.csv
2025-06-07 07:49:55,169 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-CRANDES-********.csv
2025-06-07 07:49:55,169 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-FCOMPARTAMOS-********.csv
2025-06-07 07:49:55,169 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-FCONFIANZA-********.csv
2025-06-07 07:49:55,169 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-NULL-********.csv
2025-06-07 07:49:55,169 - LogUsuariosPipeline - INFO - Estructura OPTIMIZADA: solo archivos segmentados (datos completos en Parquet)
2025-06-07 07:49:55,169 - LogUsuariosPipeline - INFO - === PIPELINE COMPLETADO EXITOSAMENTE ===
2025-06-07 07:49:55,169 - LogUsuariosPipeline - INFO - 
=== ARCHIVOS GENERADOS ===
2025-06-07 07:49:55,169 - LogUsuariosPipeline - INFO - Archivos temporales en TEMP_LOGS_USUARIOS/********:
2025-06-07 07:49:55,169 - LogUsuariosPipeline - INFO -   - USER_ACCOUNT_HISTORY.parquet (0.00 MB)
2025-06-07 07:49:55,169 - LogUsuariosPipeline - INFO -   - USER_DATA_TRX.parquet (126.94 MB)
2025-06-07 07:49:55,169 - LogUsuariosPipeline - INFO -   - USER_MODIFICATION_DAY.parquet (3.68 MB)
2025-06-07 07:49:55,169 - LogUsuariosPipeline - INFO -   - USER_AUTH_CHANGE_HISTORY.parquet (0.13 MB)
2025-06-07 07:49:55,169 - LogUsuariosPipeline - INFO - Archivos de salida en output/********:
2025-06-07 07:49:55,170 - LogUsuariosPipeline - INFO -   - LOG_USR.parquet (4.74 MB)
2025-06-07 07:49:55,170 - LogUsuariosPipeline - INFO -   - LOG_USR_DEDUPLICATED.parquet (4.61 MB)
2025-06-07 07:49:55,170 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-0231FCONFIANZA-********.csv (0.00 MB)
2025-06-07 07:49:55,170 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-BNACION-********.csv (0.07 MB)
2025-06-07 07:49:55,170 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-CCUSCO-********.csv (0.00 MB)
2025-06-07 07:49:55,170 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-CRANDES-********.csv (0.01 MB)
2025-06-07 07:49:55,170 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-FCOMPARTAMOS-********.csv (33.63 MB)
2025-06-07 07:49:55,170 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-FCONFIANZA-********.csv (0.00 MB)
2025-06-07 07:49:55,170 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-NULL-********.csv (0.00 MB)
2025-06-07 07:49:55,170 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-FCOMPARTAMOS-********074955.csv (2.16 MB)
2025-06-07 07:49:55,170 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-BNACION-********074955.csv (0.01 MB)
2025-06-07 07:49:55,170 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-CRANDES-********074955.csv (0.00 MB)
2025-06-07 07:49:55,170 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-CCUSCO-********074955.csv (0.00 MB)
2025-06-07 22:15:45,515 - LogUsuariosPipeline - INFO - Configurando credenciales S3...
2025-06-07 22:15:45,530 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-07 22:15:45,581 - LogUsuariosPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-07 22:15:45,582 - LogUsuariosPipeline - INFO - === INICIANDO PIPELINE LOG_USUARIOS MODERNIZADO ===
2025-06-07 22:15:45,582 - LogUsuariosPipeline - INFO - Fecha de procesamiento: 2025-06-06 (carpeta: ********)
2025-06-07 22:15:45,582 - LogUsuariosPipeline - INFO - USER-MODIFY: INICIANDO
2025-06-07 22:15:45,582 - LogUsuariosPipeline - INFO - Extrayendo USER_ACCOUNT_HISTORY para fecha: 2025-06-06
2025-06-07 22:15:45,749 - LogUsuariosPipeline - INFO - USER_ACCOUNT_HISTORY extraído: 7 registros -> TEMP_LOGS_USUARIOS/********/USER_ACCOUNT_HISTORY.parquet
2025-06-07 22:15:45,749 - LogUsuariosPipeline - INFO - USER-MODIFY: OK
2025-06-07 22:15:45,749 - LogUsuariosPipeline - INFO - PRE-LOG-USR: INICIANDO
2025-06-07 22:15:45,749 - LogUsuariosPipeline - INFO - Ejecutando SP_PRE_LOG_USR CORREGIDO (incluye USER_IDs múltiples) para fecha: 2025-06-06
2025-06-07 22:15:54,425 - LogUsuariosPipeline - INFO - SP_PRE_LOG_USR completado: 1583130 registros -> TEMP_LOGS_USUARIOS/********/USER_DATA_TRX.parquet
2025-06-07 22:15:54,426 - LogUsuariosPipeline - INFO - Ejecutando SP_USER_MODIFICATION para fecha: 2025-06-06
2025-06-07 22:15:57,371 - LogUsuariosPipeline - INFO - SP_USER_MODIFICATION completado: 6908 registros -> TEMP_LOGS_USUARIOS/********/USER_MODIFICATION_DAY.parquet
2025-06-07 22:15:57,371 - LogUsuariosPipeline - INFO - Ejecutando SP_USER_AUTH_DAY para fecha: 2025-06-06
2025-06-07 22:15:57,995 - LogUsuariosPipeline - INFO - SP_USER_AUTH_DAY completado: 5189 registros -> TEMP_LOGS_USUARIOS/********/USER_AUTH_CHANGE_HISTORY.parquet
2025-06-07 22:15:57,995 - LogUsuariosPipeline - INFO - PRE-LOG-USR: OK
2025-06-07 22:15:57,995 - LogUsuariosPipeline - INFO - LOG-USR: INICIANDO
2025-06-07 22:15:57,995 - LogUsuariosPipeline - INFO - Ejecutando SP_LOG_USR EXACTO (igual a Oracle) para fecha: 2025-06-06
2025-06-07 22:15:59,791 - LogUsuariosPipeline - INFO - SP_LOG_USR EXACTO completado: 19661 registros -> output/********/LOG_USR.parquet
2025-06-07 22:15:59,791 - LogUsuariosPipeline - INFO - LOG-USR: OK
2025-06-07 22:15:59,791 - LogUsuariosPipeline - INFO - EXPORT-CSV: INICIANDO
2025-06-07 22:15:59,791 - LogUsuariosPipeline - INFO - Iniciando exportación CSV con conversiones de perfiles para fecha: 2025-06-06
2025-06-07 22:15:59,791 - LogUsuariosPipeline - INFO - Aplicando SOLO deduplicación como Oracle original (SIN conversión de tipos)
2025-06-07 22:16:00,021 - LogUsuariosPipeline - INFO - Deduplicación completada (manteniendo REQUESTTYPE originales):
2025-06-07 22:16:00,021 - LogUsuariosPipeline - INFO -   - Registros originales: 19,661
2025-06-07 22:16:00,021 - LogUsuariosPipeline - INFO -   - Registros después de deduplicación: 16,069
2025-06-07 22:16:00,021 - LogUsuariosPipeline - INFO -   - Duplicados eliminados: 3,592
2025-06-07 22:16:00,023 - LogUsuariosPipeline - INFO - Tipos de transacción mantenidos:
2025-06-07 22:16:00,023 - LogUsuariosPipeline - INFO -   - User Modification: 6,579 registros
2025-06-07 22:16:00,023 - LogUsuariosPipeline - INFO -   - CHANGE_AUTH_FACTOR: 5,071 registros
2025-06-07 22:16:00,023 - LogUsuariosPipeline - INFO -   - ActivateUser: 1,756 registros
2025-06-07 22:16:00,023 - LogUsuariosPipeline - INFO -   - AfiliaUser: 1,756 registros
2025-06-07 22:16:00,023 - LogUsuariosPipeline - INFO -   - ClosedUserAccount: 289 registros
2025-06-07 22:16:00,023 - LogUsuariosPipeline - INFO -   - Delete User: 289 registros
2025-06-07 22:16:00,023 - LogUsuariosPipeline - INFO -   - ClosedAccount: 289 registros
2025-06-07 22:16:00,023 - LogUsuariosPipeline - INFO -   - Lock Wallet: 24 registros
2025-06-07 22:16:00,023 - LogUsuariosPipeline - INFO -   - Suspend User: 7 registros
2025-06-07 22:16:00,024 - LogUsuariosPipeline - INFO -   - Resume User: 6 registros
2025-06-07 22:16:00,024 - LogUsuariosPipeline - INFO - Aplicando conversiones de perfiles al archivo: output/********/LOG_USR_DEDUPLICATED.parquet
2025-06-07 22:16:00,024 - LogUsuariosPipeline - INFO - Cargando tabla de conversión de perfiles...
2025-06-07 22:16:00,026 - LogUsuariosPipeline - INFO - Tabla conv_perfil.csv cargada: 796 registros
2025-06-07 22:16:00,030 - LogUsuariosPipeline - INFO - Tabla de conversión de perfiles cargada exitosamente en DuckDB
2025-06-07 22:16:00,032 - LogUsuariosPipeline - ERROR - Error aplicando conversiones al archivo: Binder Error: Values list "cp_a" does not have a column named "PERFIL_CODIGO"

LINE 8: ...            LEFT JOIN conv_perfil_table cp_a ON l.perfilA = cp_a.PERFIL_CODIGO
                                                                       ^
2025-06-07 22:16:00,032 - LogUsuariosPipeline - INFO - Omitiendo CSV principal redundante - usando solo archivos Parquet y CSVs segmentados
2025-06-07 22:16:00,032 - LogUsuariosPipeline - INFO - Iniciando exportación segmentada por BankDomain
2025-06-07 22:16:00,032 - LogUsuariosPipeline - INFO - Procesando TODOS los bancos del sistema: ['0231FCONFIANZA', 'BNACION', 'CCUSCO', 'CRANDES', 'FCOMPARTAMOS', 'FCONFIANZA', None]
2025-06-07 22:16:00,038 - LogUsuariosPipeline - INFO - Exportado dominio 0231FCONFIANZA: 0 registros (archivo vacío SIN headers) -> output/********/csv_exports/by_bank/LOG-USUARIOS-0231FCONFIANZA-********.csv
2025-06-07 22:16:00,059 - LogUsuariosPipeline - INFO - Exportado dominio BNACION: 168 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-BNACION-********.csv
2025-06-07 22:16:00,066 - LogUsuariosPipeline - INFO - Exportado dominio CCUSCO: 0 registros (archivo vacío SIN headers) -> output/********/csv_exports/by_bank/LOG-USUARIOS-CCUSCO-********.csv
2025-06-07 22:16:00,072 - LogUsuariosPipeline - INFO - Exportado dominio CRANDES: 4 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-CRANDES-********.csv
2025-06-07 22:16:00,178 - LogUsuariosPipeline - INFO - Exportado dominio FCOMPARTAMOS: 19489 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-FCOMPARTAMOS-********.csv
2025-06-07 22:16:00,184 - LogUsuariosPipeline - INFO - Exportado dominio FCONFIANZA: 0 registros (archivo vacío SIN headers) -> output/********/csv_exports/by_bank/LOG-USUARIOS-FCONFIANZA-********.csv
2025-06-07 22:16:00,188 - LogUsuariosPipeline - INFO - Exportado dominio NULL: 0 registros (archivo vacío SIN headers) -> output/********/csv_exports/by_bank/LOG-USUARIOS-NULL-********.csv
2025-06-07 22:16:00,188 - LogUsuariosPipeline - INFO - Exportación segmentada completada: 7 archivos generados
2025-06-07 22:16:00,188 - LogUsuariosPipeline - INFO - Estructura EXACTA como flujo original: solo by_bank (sin by_profile)
2025-06-07 22:16:00,188 - LogUsuariosPipeline - INFO - 🔄 APLICANDO PROCESAMIENTO EXACTO como procesar.py...
2025-06-07 22:16:00,189 - LogUsuariosPipeline - INFO - 🔄 INICIANDO PROCESAMIENTO EXACTO como procesar.py
2025-06-07 22:16:00,189 - LogUsuariosPipeline - INFO - 📁 Input: output/********/LOG_USR.parquet
2025-06-07 22:16:00,189 - LogUsuariosPipeline - INFO - 📅 Fecha: 2025-06-06
2025-06-07 22:16:00,189 - LogUsuariosPipeline - INFO - 📁 Output: output/********/csv_exports/csv_final_procesado
2025-06-07 22:16:00,189 - LogUsuariosPipeline - INFO - 📊 Cargando datos desde LOG_USR.parquet...
2025-06-07 22:16:00,255 - LogUsuariosPipeline - INFO - 📊 Registros cargados: 19,661
2025-06-07 22:16:00,256 - LogUsuariosPipeline - INFO - 🔄 Procesando grupos por USERHISTID...
2025-06-07 22:16:03,507 - LogUsuariosPipeline - INFO - 📊 Convirtiendo a DataFrame...
2025-06-07 22:16:03,631 - LogUsuariosPipeline - INFO - 📊 Registros después del procesamiento inicial: 48,470
2025-06-07 22:16:03,632 - LogUsuariosPipeline - INFO - 🔍 Aplicando filtro de valores válidos...
2025-06-07 22:16:03,639 - LogUsuariosPipeline - INFO - 📊 Registros después del filtro: 17,983
2025-06-07 22:16:03,640 - LogUsuariosPipeline - INFO - 🥷 Aplicando filtros adicionales de calidad de datos...
2025-06-07 22:16:03,655 - LogUsuariosPipeline - INFO - 📊 Registros después de filtros de calidad: 17,983
2025-06-07 22:16:03,655 - LogUsuariosPipeline - INFO - 🔄 Agrupando y procesando JSON...
2025-06-07 22:16:06,733 - LogUsuariosPipeline - INFO - 📊 Registros después de eliminar duplicados: 13,049
2025-06-07 22:16:06,743 - LogUsuariosPipeline - INFO - 🔄 Aplicando asignaciones a columnas...
2025-06-07 22:16:07,774 - LogUsuariosPipeline - INFO - 📊 Cargando tabla de conversión de perfiles...
2025-06-07 22:16:07,776 - LogUsuariosPipeline - INFO - 📊 Tabla de conversión cargada: 796 registros
2025-06-07 22:16:08,105 - LogUsuariosPipeline - INFO - 🔄 Aplicando función crítica de expansión de registros...
2025-06-07 22:16:08,106 - LogUsuariosPipeline - INFO - 📊 Registros antes de expansión: 13,049
2025-06-07 22:16:08,685 - LogUsuariosPipeline - INFO - 📊 Registros después de expansión: 13,077
2025-06-07 22:16:08,685 - LogUsuariosPipeline - INFO - ✅ Función crítica de expansión aplicada exitosamente
2025-06-07 22:16:08,685 - LogUsuariosPipeline - INFO - 🔄 Aplicando mapeo de columnas...
2025-06-07 22:16:08,687 - LogUsuariosPipeline - INFO - 🔄 Procesando tipos de transacciones válidas...
2025-06-07 22:16:08,726 - LogUsuariosPipeline - INFO - 📊 Registros después de concatenar: 13,077
2025-06-07 22:16:24,290 - LogUsuariosPipeline - INFO - 📊 Registros finales para exportar: 13,077
2025-06-07 22:16:24,290 - LogUsuariosPipeline - INFO - 📁 Generando archivos CSV por BankDomain...
2025-06-07 22:16:24,356 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-FCOMPARTAMOS-********221624.csv (12,956 registros)
2025-06-07 22:16:24,360 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-BNACION-********221624.csv (111 registros)
2025-06-07 22:16:24,362 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-CRANDES-********221624.csv (2 registros)
2025-06-07 22:16:24,363 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-CCUSCO-********221624.csv (8 registros)
2025-06-07 22:16:24,363 - LogUsuariosPipeline - INFO - ✅ Procesamiento completado: 4 archivos generados
2025-06-07 22:16:24,363 - LogUsuariosPipeline - INFO - 📊 Total registros procesados: 13,077
2025-06-07 22:16:24,419 - LogUsuariosPipeline - INFO - ✅ Procesamiento exacto completado: 4 archivos
2025-06-07 22:16:24,420 - LogUsuariosPipeline - INFO -   📁 LOGUSR-FCOMPARTAMOS-********221624.csv
2025-06-07 22:16:24,420 - LogUsuariosPipeline - INFO -   📁 LOGUSR-BNACION-********221624.csv
2025-06-07 22:16:24,420 - LogUsuariosPipeline - INFO -   📁 LOGUSR-CRANDES-********221624.csv
2025-06-07 22:16:24,420 - LogUsuariosPipeline - INFO -   📁 LOGUSR-CCUSCO-********221624.csv
2025-06-07 22:16:24,420 - LogUsuariosPipeline - INFO - Archivo temporal eliminado: TEMP_LOGS_USUARIOS/********/WALLET_OLD.parquet
2025-06-07 22:16:24,420 - LogUsuariosPipeline - INFO - EXPORT-CSV: OK
2025-06-07 22:16:24,420 - LogUsuariosPipeline - INFO - === ARCHIVOS CSV GENERADOS ===
2025-06-07 22:16:24,420 - LogUsuariosPipeline - INFO - Total de archivos CSV segmentados: 11
2025-06-07 22:16:24,420 - LogUsuariosPipeline - INFO - Archivos por BankDomain (7) - EXACTO como flujo original:
2025-06-07 22:16:24,420 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-0231FCONFIANZA-********.csv
2025-06-07 22:16:24,420 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-BNACION-********.csv
2025-06-07 22:16:24,420 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-CCUSCO-********.csv
2025-06-07 22:16:24,420 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-CRANDES-********.csv
2025-06-07 22:16:24,420 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-FCOMPARTAMOS-********.csv
2025-06-07 22:16:24,420 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-FCONFIANZA-********.csv
2025-06-07 22:16:24,420 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-NULL-********.csv
2025-06-07 22:16:24,420 - LogUsuariosPipeline - INFO - Estructura OPTIMIZADA: solo archivos segmentados (datos completos en Parquet)
2025-06-07 22:16:24,420 - LogUsuariosPipeline - INFO - === PIPELINE COMPLETADO EXITOSAMENTE ===
2025-06-07 22:16:24,420 - LogUsuariosPipeline - INFO - 
=== ARCHIVOS GENERADOS ===
2025-06-07 22:16:24,420 - LogUsuariosPipeline - INFO - Archivos temporales en TEMP_LOGS_USUARIOS/********:
2025-06-07 22:16:24,420 - LogUsuariosPipeline - INFO -   - USER_ACCOUNT_HISTORY.parquet (0.00 MB)
2025-06-07 22:16:24,421 - LogUsuariosPipeline - INFO -   - USER_DATA_TRX.parquet (127.16 MB)
2025-06-07 22:16:24,421 - LogUsuariosPipeline - INFO -   - USER_MODIFICATION_DAY.parquet (3.36 MB)
2025-06-07 22:16:24,421 - LogUsuariosPipeline - INFO -   - USER_AUTH_CHANGE_HISTORY.parquet (0.11 MB)
2025-06-07 22:16:24,421 - LogUsuariosPipeline - INFO - Archivos de salida en output/********:
2025-06-07 22:16:24,421 - LogUsuariosPipeline - INFO -   - LOG_USR.parquet (4.34 MB)
2025-06-07 22:16:24,421 - LogUsuariosPipeline - INFO -   - LOG_USR_DEDUPLICATED.parquet (4.23 MB)
2025-06-07 22:16:24,421 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-0231FCONFIANZA-********.csv (0.00 MB)
2025-06-07 22:16:24,421 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-BNACION-********.csv (0.11 MB)
2025-06-07 22:16:24,421 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-CCUSCO-********.csv (0.00 MB)
2025-06-07 22:16:24,421 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-CRANDES-********.csv (0.00 MB)
2025-06-07 22:16:24,421 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-FCOMPARTAMOS-********.csv (30.92 MB)
2025-06-07 22:16:24,421 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-FCONFIANZA-********.csv (0.00 MB)
2025-06-07 22:16:24,421 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-NULL-********.csv (0.00 MB)
2025-06-07 22:16:24,421 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-FCOMPARTAMOS-********221624.csv (2.00 MB)
2025-06-07 22:16:24,421 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-BNACION-********221624.csv (0.01 MB)
2025-06-07 22:16:24,421 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-CRANDES-********221624.csv (0.00 MB)
2025-06-07 22:16:24,421 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-CCUSCO-********221624.csv (0.00 MB)
2025-06-07 23:15:07,010 - LogUsuariosPipeline - INFO - Configurando credenciales S3...
2025-06-07 23:15:07,023 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-07 23:15:07,074 - LogUsuariosPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-07 23:15:07,074 - LogUsuariosPipeline - INFO - === INICIANDO PIPELINE LOG_USUARIOS MODERNIZADO ===
2025-06-07 23:15:07,074 - LogUsuariosPipeline - INFO - Fecha de procesamiento: 2025-06-06 (carpeta: ********)
2025-06-07 23:15:07,075 - LogUsuariosPipeline - INFO - USER-MODIFY: INICIANDO
2025-06-07 23:15:07,075 - LogUsuariosPipeline - INFO - Extrayendo USER_ACCOUNT_HISTORY para fecha: 2025-06-06
2025-06-07 23:15:07,238 - LogUsuariosPipeline - INFO - USER_ACCOUNT_HISTORY extraído: 7 registros -> TEMP_LOGS_USUARIOS/********/USER_ACCOUNT_HISTORY.parquet
2025-06-07 23:15:07,238 - LogUsuariosPipeline - INFO - USER-MODIFY: OK
2025-06-07 23:15:07,238 - LogUsuariosPipeline - INFO - PRE-LOG-USR: INICIANDO
2025-06-07 23:15:07,238 - LogUsuariosPipeline - INFO - Ejecutando SP_PRE_LOG_USR CORREGIDO (incluye USER_IDs múltiples) para fecha: 2025-06-06
2025-06-07 23:15:16,106 - LogUsuariosPipeline - INFO - SP_PRE_LOG_USR completado: 1583130 registros -> TEMP_LOGS_USUARIOS/********/USER_DATA_TRX.parquet
2025-06-07 23:15:16,106 - LogUsuariosPipeline - INFO - Ejecutando SP_USER_MODIFICATION para fecha: 2025-06-06
2025-06-07 23:15:19,390 - LogUsuariosPipeline - INFO - SP_USER_MODIFICATION completado: 6908 registros -> TEMP_LOGS_USUARIOS/********/USER_MODIFICATION_DAY.parquet
2025-06-07 23:15:19,390 - LogUsuariosPipeline - INFO - Ejecutando SP_USER_AUTH_DAY para fecha: 2025-06-06
2025-06-07 23:15:20,030 - LogUsuariosPipeline - INFO - SP_USER_AUTH_DAY completado: 5189 registros -> TEMP_LOGS_USUARIOS/********/USER_AUTH_CHANGE_HISTORY.parquet
2025-06-07 23:15:20,030 - LogUsuariosPipeline - INFO - PRE-LOG-USR: OK
2025-06-07 23:15:20,031 - LogUsuariosPipeline - INFO - LOG-USR: INICIANDO
2025-06-07 23:15:20,031 - LogUsuariosPipeline - INFO - Ejecutando SP_LOG_USR EXACTO (igual a Oracle) para fecha: 2025-06-06
2025-06-07 23:15:21,709 - LogUsuariosPipeline - INFO - SP_LOG_USR EXACTO completado: 19661 registros -> output/********/LOG_USR.parquet
2025-06-07 23:15:21,709 - LogUsuariosPipeline - INFO - LOG-USR: OK
2025-06-07 23:15:21,709 - LogUsuariosPipeline - INFO - EXPORT-CSV: INICIANDO
2025-06-07 23:15:21,709 - LogUsuariosPipeline - INFO - Iniciando exportación CSV con conversiones de perfiles para fecha: 2025-06-06
2025-06-07 23:15:21,709 - LogUsuariosPipeline - INFO - Aplicando SOLO deduplicación como Oracle original (SIN conversión de tipos)
2025-06-07 23:15:21,943 - LogUsuariosPipeline - INFO - Deduplicación completada (manteniendo REQUESTTYPE originales):
2025-06-07 23:15:21,943 - LogUsuariosPipeline - INFO -   - Registros originales: 19,661
2025-06-07 23:15:21,943 - LogUsuariosPipeline - INFO -   - Registros después de deduplicación: 16,069
2025-06-07 23:15:21,943 - LogUsuariosPipeline - INFO -   - Duplicados eliminados: 3,592
2025-06-07 23:15:21,944 - LogUsuariosPipeline - INFO - Tipos de transacción mantenidos:
2025-06-07 23:15:21,945 - LogUsuariosPipeline - INFO -   - User Modification: 6,579 registros
2025-06-07 23:15:21,945 - LogUsuariosPipeline - INFO -   - CHANGE_AUTH_FACTOR: 5,071 registros
2025-06-07 23:15:21,945 - LogUsuariosPipeline - INFO -   - ActivateUser: 1,756 registros
2025-06-07 23:15:21,945 - LogUsuariosPipeline - INFO -   - AfiliaUser: 1,756 registros
2025-06-07 23:15:21,945 - LogUsuariosPipeline - INFO -   - Delete User: 289 registros
2025-06-07 23:15:21,945 - LogUsuariosPipeline - INFO -   - ClosedAccount: 289 registros
2025-06-07 23:15:21,945 - LogUsuariosPipeline - INFO -   - ClosedUserAccount: 289 registros
2025-06-07 23:15:21,945 - LogUsuariosPipeline - INFO -   - Lock Wallet: 24 registros
2025-06-07 23:15:21,945 - LogUsuariosPipeline - INFO -   - Suspend User: 7 registros
2025-06-07 23:15:21,945 - LogUsuariosPipeline - INFO -   - Resume User: 6 registros
2025-06-07 23:15:21,945 - LogUsuariosPipeline - INFO - Aplicando conversiones de perfiles al archivo: output/********/LOG_USR_DEDUPLICATED.parquet
2025-06-07 23:15:21,945 - LogUsuariosPipeline - INFO - Cargando tabla de conversión de perfiles...
2025-06-07 23:15:21,947 - LogUsuariosPipeline - INFO - Tabla conv_perfil.csv cargada: 796 registros
2025-06-07 23:15:21,952 - LogUsuariosPipeline - INFO - Tabla de conversión de perfiles cargada exitosamente en DuckDB
2025-06-07 23:15:21,954 - LogUsuariosPipeline - ERROR - Error aplicando conversiones al archivo: Binder Error: Values list "cp_a" does not have a column named "PERFIL_CODIGO"

LINE 8: ...            LEFT JOIN conv_perfil_table cp_a ON l.perfilA = cp_a.PERFIL_CODIGO
                                                                       ^
2025-06-07 23:15:21,954 - LogUsuariosPipeline - INFO - Omitiendo CSV principal redundante - usando solo archivos Parquet y CSVs segmentados
2025-06-07 23:15:21,954 - LogUsuariosPipeline - INFO - Iniciando exportación segmentada por BankDomain
2025-06-07 23:15:21,954 - LogUsuariosPipeline - INFO - Procesando TODOS los bancos del sistema: ['0231FCONFIANZA', 'BNACION', 'CCUSCO', 'CRANDES', 'FCOMPARTAMOS', 'FCONFIANZA', None]
2025-06-07 23:15:21,958 - LogUsuariosPipeline - INFO - Exportado dominio 0231FCONFIANZA: 0 registros (archivo vacío SIN headers) -> output/********/csv_exports/by_bank/LOG-USUARIOS-0231FCONFIANZA-********.csv
2025-06-07 23:15:21,977 - LogUsuariosPipeline - INFO - Exportado dominio BNACION: 168 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-BNACION-********.csv
2025-06-07 23:15:21,984 - LogUsuariosPipeline - INFO - Exportado dominio CCUSCO: 0 registros (archivo vacío SIN headers) -> output/********/csv_exports/by_bank/LOG-USUARIOS-CCUSCO-********.csv
2025-06-07 23:15:21,990 - LogUsuariosPipeline - INFO - Exportado dominio CRANDES: 4 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-CRANDES-********.csv
2025-06-07 23:15:22,106 - LogUsuariosPipeline - INFO - Exportado dominio FCOMPARTAMOS: 19489 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-FCOMPARTAMOS-********.csv
2025-06-07 23:15:22,112 - LogUsuariosPipeline - INFO - Exportado dominio FCONFIANZA: 0 registros (archivo vacío SIN headers) -> output/********/csv_exports/by_bank/LOG-USUARIOS-FCONFIANZA-********.csv
2025-06-07 23:15:22,116 - LogUsuariosPipeline - INFO - Exportado dominio NULL: 0 registros (archivo vacío SIN headers) -> output/********/csv_exports/by_bank/LOG-USUARIOS-NULL-********.csv
2025-06-07 23:15:22,116 - LogUsuariosPipeline - INFO - Exportación segmentada completada: 7 archivos generados
2025-06-07 23:15:22,116 - LogUsuariosPipeline - INFO - Estructura EXACTA como flujo original: solo by_bank (sin by_profile)
2025-06-07 23:15:22,116 - LogUsuariosPipeline - INFO - 🔄 APLICANDO PROCESAMIENTO EXACTO como procesar.py...
2025-06-07 23:15:22,121 - LogUsuariosPipeline - INFO - 🔄 INICIANDO PROCESAMIENTO EXACTO como procesar.py
2025-06-07 23:15:22,121 - LogUsuariosPipeline - INFO - 📁 Input: output/********/LOG_USR.parquet
2025-06-07 23:15:22,121 - LogUsuariosPipeline - INFO - 📅 Fecha: 2025-06-06
2025-06-07 23:15:22,121 - LogUsuariosPipeline - INFO - 📁 Output: output/********/csv_exports/csv_final_procesado
2025-06-07 23:15:22,121 - LogUsuariosPipeline - INFO - 📊 Cargando datos desde LOG_USR.parquet...
2025-06-07 23:15:22,192 - LogUsuariosPipeline - INFO - 📊 Registros cargados: 19,661
2025-06-07 23:15:22,193 - LogUsuariosPipeline - INFO - 🔄 Procesando grupos por USERHISTID...
2025-06-07 23:15:25,442 - LogUsuariosPipeline - INFO - 📊 Convirtiendo a DataFrame...
2025-06-07 23:15:25,570 - LogUsuariosPipeline - INFO - 📊 Registros después del procesamiento inicial: 48,470
2025-06-07 23:15:25,570 - LogUsuariosPipeline - INFO - 🔍 Aplicando filtro de valores válidos...
2025-06-07 23:15:25,578 - LogUsuariosPipeline - INFO - 📊 Registros después del filtro: 17,983
2025-06-07 23:15:25,579 - LogUsuariosPipeline - INFO - 🥷 Aplicando filtros adicionales de calidad de datos...
2025-06-07 23:15:25,595 - LogUsuariosPipeline - INFO - 📊 Registros después de filtros de calidad: 17,983
2025-06-07 23:15:25,595 - LogUsuariosPipeline - INFO - 🔄 Agrupando y procesando JSON...
2025-06-07 23:15:28,671 - LogUsuariosPipeline - INFO - 📊 Registros después de eliminar duplicados: 13,049
2025-06-07 23:15:28,681 - LogUsuariosPipeline - INFO - 🔄 Aplicando asignaciones a columnas...
2025-06-07 23:15:29,695 - LogUsuariosPipeline - INFO - 📊 Cargando tabla de conversión de perfiles...
2025-06-07 23:15:29,697 - LogUsuariosPipeline - INFO - 📊 Tabla de conversión cargada: 796 registros
2025-06-07 23:15:30,037 - LogUsuariosPipeline - INFO - 🔄 Aplicando función crítica de expansión de registros...
2025-06-07 23:15:30,037 - LogUsuariosPipeline - INFO - 📊 Registros antes de expansión: 13,049
2025-06-07 23:15:30,612 - LogUsuariosPipeline - INFO - 📊 Registros después de expansión: 13,077
2025-06-07 23:15:30,612 - LogUsuariosPipeline - INFO - ✅ Función crítica de expansión aplicada exitosamente
2025-06-07 23:15:30,612 - LogUsuariosPipeline - INFO - 🔄 Aplicando mapeo de columnas...
2025-06-07 23:15:30,614 - LogUsuariosPipeline - INFO - 🔄 Procesando tipos de transacciones válidas...
2025-06-07 23:15:30,663 - LogUsuariosPipeline - INFO - 📊 Registros después de concatenar: 13,077
2025-06-07 23:15:46,271 - LogUsuariosPipeline - INFO - 📊 Registros finales para exportar: 13,077
2025-06-07 23:15:46,272 - LogUsuariosPipeline - INFO - 📁 Generando archivos CSV por BankDomain...
2025-06-07 23:15:46,337 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-FCOMPARTAMOS-********231546.csv (12,956 registros)
2025-06-07 23:15:46,340 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-BNACION-********231546.csv (111 registros)
2025-06-07 23:15:46,341 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-CRANDES-********231546.csv (2 registros)
2025-06-07 23:15:46,343 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-CCUSCO-********231546.csv (8 registros)
2025-06-07 23:15:46,343 - LogUsuariosPipeline - INFO - ✅ Procesamiento completado: 4 archivos generados
2025-06-07 23:15:46,343 - LogUsuariosPipeline - INFO - 📊 Total registros procesados: 13,077
2025-06-07 23:15:46,396 - LogUsuariosPipeline - INFO - ✅ Procesamiento exacto completado: 4 archivos
2025-06-07 23:15:46,396 - LogUsuariosPipeline - INFO -   📁 LOGUSR-FCOMPARTAMOS-********231546.csv
2025-06-07 23:15:46,396 - LogUsuariosPipeline - INFO -   📁 LOGUSR-BNACION-********231546.csv
2025-06-07 23:15:46,396 - LogUsuariosPipeline - INFO -   📁 LOGUSR-CRANDES-********231546.csv
2025-06-07 23:15:46,396 - LogUsuariosPipeline - INFO -   📁 LOGUSR-CCUSCO-********231546.csv
2025-06-07 23:15:46,396 - LogUsuariosPipeline - INFO - Archivo temporal eliminado: TEMP_LOGS_USUARIOS/********/WALLET_OLD.parquet
2025-06-07 23:15:46,396 - LogUsuariosPipeline - INFO - EXPORT-CSV: OK
2025-06-07 23:15:46,396 - LogUsuariosPipeline - INFO - === ARCHIVOS CSV GENERADOS ===
2025-06-07 23:15:46,396 - LogUsuariosPipeline - INFO - Total de archivos CSV segmentados: 11
2025-06-07 23:15:46,396 - LogUsuariosPipeline - INFO - Archivos por BankDomain (7) - EXACTO como flujo original:
2025-06-07 23:15:46,397 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-0231FCONFIANZA-********.csv
2025-06-07 23:15:46,397 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-BNACION-********.csv
2025-06-07 23:15:46,397 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-CCUSCO-********.csv
2025-06-07 23:15:46,397 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-CRANDES-********.csv
2025-06-07 23:15:46,397 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-FCOMPARTAMOS-********.csv
2025-06-07 23:15:46,397 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-FCONFIANZA-********.csv
2025-06-07 23:15:46,397 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-NULL-********.csv
2025-06-07 23:15:46,397 - LogUsuariosPipeline - INFO - Estructura OPTIMIZADA: solo archivos segmentados (datos completos en Parquet)
2025-06-07 23:15:46,397 - LogUsuariosPipeline - INFO - === PIPELINE COMPLETADO EXITOSAMENTE ===
2025-06-07 23:15:46,397 - LogUsuariosPipeline - INFO - 
=== ARCHIVOS GENERADOS ===
2025-06-07 23:15:46,397 - LogUsuariosPipeline - INFO - Archivos temporales en TEMP_LOGS_USUARIOS/********:
2025-06-07 23:15:46,397 - LogUsuariosPipeline - INFO -   - USER_ACCOUNT_HISTORY.parquet (0.00 MB)
2025-06-07 23:15:46,397 - LogUsuariosPipeline - INFO -   - USER_DATA_TRX.parquet (127.15 MB)
2025-06-07 23:15:46,397 - LogUsuariosPipeline - INFO -   - USER_MODIFICATION_DAY.parquet (3.36 MB)
2025-06-07 23:15:46,397 - LogUsuariosPipeline - INFO -   - USER_AUTH_CHANGE_HISTORY.parquet (0.11 MB)
2025-06-07 23:15:46,397 - LogUsuariosPipeline - INFO - Archivos de salida en output/********:
2025-06-07 23:15:46,397 - LogUsuariosPipeline - INFO -   - LOG_USR.parquet (4.34 MB)
2025-06-07 23:15:46,397 - LogUsuariosPipeline - INFO -   - LOG_USR_DEDUPLICATED.parquet (4.23 MB)
2025-06-07 23:15:46,397 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-0231FCONFIANZA-********.csv (0.00 MB)
2025-06-07 23:15:46,398 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-BNACION-********.csv (0.11 MB)
2025-06-07 23:15:46,398 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-CCUSCO-********.csv (0.00 MB)
2025-06-07 23:15:46,398 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-CRANDES-********.csv (0.00 MB)
2025-06-07 23:15:46,398 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-FCOMPARTAMOS-********.csv (30.92 MB)
2025-06-07 23:15:46,398 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-FCONFIANZA-********.csv (0.00 MB)
2025-06-07 23:15:46,398 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-NULL-********.csv (0.00 MB)
2025-06-07 23:15:46,398 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-FCOMPARTAMOS-********221624.csv (2.00 MB)
2025-06-07 23:15:46,398 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-BNACION-********221624.csv (0.01 MB)
2025-06-07 23:15:46,398 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-CRANDES-********221624.csv (0.00 MB)
2025-06-07 23:15:46,398 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-CCUSCO-********221624.csv (0.00 MB)
2025-06-07 23:15:46,398 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-FCOMPARTAMOS-********231546.csv (2.00 MB)
2025-06-07 23:15:46,398 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-BNACION-********231546.csv (0.01 MB)
2025-06-07 23:15:46,398 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-CRANDES-********231546.csv (0.00 MB)
2025-06-07 23:15:46,398 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-CCUSCO-********231546.csv (0.00 MB)
2025-06-07 23:16:21,443 - LogUsuariosPipeline - INFO - Configurando credenciales S3...
2025-06-07 23:16:21,457 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-07 23:16:21,505 - LogUsuariosPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-07 23:16:21,506 - LogUsuariosPipeline - INFO - === INICIANDO PIPELINE LOG_USUARIOS MODERNIZADO ===
2025-06-07 23:16:21,506 - LogUsuariosPipeline - INFO - Fecha de procesamiento: 2025-06-06 (carpeta: ********)
2025-06-07 23:16:21,506 - LogUsuariosPipeline - INFO - USER-MODIFY: INICIANDO
2025-06-07 23:16:21,506 - LogUsuariosPipeline - INFO - Extrayendo USER_ACCOUNT_HISTORY para fecha: 2025-06-06
2025-06-07 23:16:21,635 - LogUsuariosPipeline - INFO - USER_ACCOUNT_HISTORY extraído: 7 registros -> TEMP_LOGS_USUARIOS/********/USER_ACCOUNT_HISTORY.parquet
2025-06-07 23:16:21,636 - LogUsuariosPipeline - INFO - USER-MODIFY: OK
2025-06-07 23:16:21,636 - LogUsuariosPipeline - INFO - PRE-LOG-USR: INICIANDO
2025-06-07 23:16:21,636 - LogUsuariosPipeline - INFO - Ejecutando SP_PRE_LOG_USR CORREGIDO (incluye USER_IDs múltiples) para fecha: 2025-06-06
2025-06-07 23:16:29,837 - LogUsuariosPipeline - INFO - SP_PRE_LOG_USR completado: 1583130 registros -> TEMP_LOGS_USUARIOS/********/USER_DATA_TRX.parquet
2025-06-07 23:16:29,837 - LogUsuariosPipeline - INFO - Ejecutando SP_USER_MODIFICATION para fecha: 2025-06-06
2025-06-07 23:16:32,198 - LogUsuariosPipeline - INFO - SP_USER_MODIFICATION completado: 6908 registros -> TEMP_LOGS_USUARIOS/********/USER_MODIFICATION_DAY.parquet
2025-06-07 23:16:32,198 - LogUsuariosPipeline - INFO - Ejecutando SP_USER_AUTH_DAY para fecha: 2025-06-06
2025-06-07 23:16:32,669 - LogUsuariosPipeline - INFO - SP_USER_AUTH_DAY completado: 5189 registros -> TEMP_LOGS_USUARIOS/********/USER_AUTH_CHANGE_HISTORY.parquet
2025-06-07 23:16:32,669 - LogUsuariosPipeline - INFO - PRE-LOG-USR: OK
2025-06-07 23:16:32,670 - LogUsuariosPipeline - INFO - LOG-USR: INICIANDO
2025-06-07 23:16:32,670 - LogUsuariosPipeline - INFO - Ejecutando SP_LOG_USR EXACTO (igual a Oracle) para fecha: 2025-06-06
2025-06-07 23:16:34,552 - LogUsuariosPipeline - INFO - SP_LOG_USR EXACTO completado: 19661 registros -> output/********/LOG_USR.parquet
2025-06-07 23:16:34,553 - LogUsuariosPipeline - INFO - LOG-USR: OK
2025-06-07 23:16:34,553 - LogUsuariosPipeline - INFO - EXPORT-CSV: INICIANDO
2025-06-07 23:16:34,553 - LogUsuariosPipeline - INFO - Iniciando exportación CSV con conversiones de perfiles para fecha: 2025-06-06
2025-06-07 23:16:34,553 - LogUsuariosPipeline - INFO - Aplicando SOLO deduplicación como Oracle original (SIN conversión de tipos)
2025-06-07 23:16:34,798 - LogUsuariosPipeline - INFO - Deduplicación completada (manteniendo REQUESTTYPE originales):
2025-06-07 23:16:34,798 - LogUsuariosPipeline - INFO -   - Registros originales: 19,661
2025-06-07 23:16:34,798 - LogUsuariosPipeline - INFO -   - Registros después de deduplicación: 16,069
2025-06-07 23:16:34,798 - LogUsuariosPipeline - INFO -   - Duplicados eliminados: 3,592
2025-06-07 23:16:34,801 - LogUsuariosPipeline - INFO - Tipos de transacción mantenidos:
2025-06-07 23:16:34,802 - LogUsuariosPipeline - INFO -   - User Modification: 6,579 registros
2025-06-07 23:16:34,802 - LogUsuariosPipeline - INFO -   - CHANGE_AUTH_FACTOR: 5,071 registros
2025-06-07 23:16:34,802 - LogUsuariosPipeline - INFO -   - ActivateUser: 1,756 registros
2025-06-07 23:16:34,802 - LogUsuariosPipeline - INFO -   - AfiliaUser: 1,756 registros
2025-06-07 23:16:34,802 - LogUsuariosPipeline - INFO -   - Delete User: 289 registros
2025-06-07 23:16:34,802 - LogUsuariosPipeline - INFO -   - ClosedUserAccount: 289 registros
2025-06-07 23:16:34,802 - LogUsuariosPipeline - INFO -   - ClosedAccount: 289 registros
2025-06-07 23:16:34,802 - LogUsuariosPipeline - INFO -   - Lock Wallet: 24 registros
2025-06-07 23:16:34,802 - LogUsuariosPipeline - INFO -   - Suspend User: 7 registros
2025-06-07 23:16:34,802 - LogUsuariosPipeline - INFO -   - Resume User: 6 registros
2025-06-07 23:16:34,802 - LogUsuariosPipeline - INFO - Aplicando conversiones de perfiles al archivo: output/********/LOG_USR_DEDUPLICATED.parquet
2025-06-07 23:16:34,802 - LogUsuariosPipeline - INFO - Cargando tabla de conversión de perfiles...
2025-06-07 23:16:34,805 - LogUsuariosPipeline - INFO - Tabla conv_perfil.csv cargada: 796 registros
2025-06-07 23:16:34,809 - LogUsuariosPipeline - INFO - Tabla de conversión de perfiles cargada exitosamente en DuckDB
2025-06-07 23:16:34,811 - LogUsuariosPipeline - ERROR - Error aplicando conversiones al archivo: Binder Error: Values list "cp_a" does not have a column named "PERFIL_CODIGO"

LINE 8: ...            LEFT JOIN conv_perfil_table cp_a ON l.perfilA = cp_a.PERFIL_CODIGO
                                                                       ^
2025-06-07 23:16:34,811 - LogUsuariosPipeline - INFO - Omitiendo CSV principal redundante - usando solo archivos Parquet y CSVs segmentados
2025-06-07 23:16:34,811 - LogUsuariosPipeline - INFO - Iniciando exportación segmentada por BankDomain
2025-06-07 23:16:34,811 - LogUsuariosPipeline - INFO - Procesando TODOS los bancos del sistema: ['0231FCONFIANZA', 'BNACION', 'CCUSCO', 'CRANDES', 'FCOMPARTAMOS', 'FCONFIANZA', None]
2025-06-07 23:16:34,815 - LogUsuariosPipeline - INFO - Exportado dominio 0231FCONFIANZA: 0 registros (archivo vacío SIN headers) -> output/********/csv_exports/by_bank/LOG-USUARIOS-0231FCONFIANZA-********.csv
2025-06-07 23:16:34,837 - LogUsuariosPipeline - INFO - Exportado dominio BNACION: 168 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-BNACION-********.csv
2025-06-07 23:16:34,844 - LogUsuariosPipeline - INFO - Exportado dominio CCUSCO: 0 registros (archivo vacío SIN headers) -> output/********/csv_exports/by_bank/LOG-USUARIOS-CCUSCO-********.csv
2025-06-07 23:16:34,851 - LogUsuariosPipeline - INFO - Exportado dominio CRANDES: 4 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-CRANDES-********.csv
2025-06-07 23:16:34,985 - LogUsuariosPipeline - INFO - Exportado dominio FCOMPARTAMOS: 19489 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-FCOMPARTAMOS-********.csv
2025-06-07 23:16:34,989 - LogUsuariosPipeline - INFO - Exportado dominio FCONFIANZA: 0 registros (archivo vacío SIN headers) -> output/********/csv_exports/by_bank/LOG-USUARIOS-FCONFIANZA-********.csv
2025-06-07 23:16:34,994 - LogUsuariosPipeline - INFO - Exportado dominio NULL: 0 registros (archivo vacío SIN headers) -> output/********/csv_exports/by_bank/LOG-USUARIOS-NULL-********.csv
2025-06-07 23:16:34,994 - LogUsuariosPipeline - INFO - Exportación segmentada completada: 7 archivos generados
2025-06-07 23:16:34,994 - LogUsuariosPipeline - INFO - Estructura EXACTA como flujo original: solo by_bank (sin by_profile)
2025-06-07 23:16:34,994 - LogUsuariosPipeline - INFO - 🔄 APLICANDO PROCESAMIENTO EXACTO como procesar.py...
2025-06-07 23:16:34,994 - LogUsuariosPipeline - INFO - 🔄 INICIANDO PROCESAMIENTO EXACTO como procesar.py
2025-06-07 23:16:34,994 - LogUsuariosPipeline - INFO - 📁 Input: output/********/LOG_USR.parquet
2025-06-07 23:16:34,994 - LogUsuariosPipeline - INFO - 📅 Fecha: 2025-06-06
2025-06-07 23:16:34,994 - LogUsuariosPipeline - INFO - 📁 Output: output/********/csv_exports/csv_final_procesado
2025-06-07 23:16:34,994 - LogUsuariosPipeline - INFO - 📊 Cargando datos desde LOG_USR.parquet...
2025-06-07 23:16:35,057 - LogUsuariosPipeline - INFO - 📊 Registros cargados: 19,661
2025-06-07 23:16:35,058 - LogUsuariosPipeline - INFO - 🔄 Procesando grupos por USERHISTID...
2025-06-07 23:16:38,295 - LogUsuariosPipeline - INFO - 📊 Convirtiendo a DataFrame...
2025-06-07 23:16:38,422 - LogUsuariosPipeline - INFO - 📊 Registros después del procesamiento inicial: 48,470
2025-06-07 23:16:38,422 - LogUsuariosPipeline - INFO - 🔍 Aplicando filtro de valores válidos...
2025-06-07 23:16:38,430 - LogUsuariosPipeline - INFO - 📊 Registros después del filtro: 17,983
2025-06-07 23:16:38,431 - LogUsuariosPipeline - INFO - 🥷 Aplicando filtros adicionales de calidad de datos...
2025-06-07 23:16:38,446 - LogUsuariosPipeline - INFO - 📊 Registros después de filtros de calidad: 17,983
2025-06-07 23:16:38,446 - LogUsuariosPipeline - INFO - 🔄 Agrupando y procesando JSON...
2025-06-07 23:16:41,475 - LogUsuariosPipeline - INFO - 📊 Registros después de eliminar duplicados: 13,049
2025-06-07 23:16:41,485 - LogUsuariosPipeline - INFO - 🔄 Aplicando asignaciones a columnas...
2025-06-07 23:16:42,507 - LogUsuariosPipeline - INFO - 📊 Cargando tabla de conversión de perfiles...
2025-06-07 23:16:42,509 - LogUsuariosPipeline - INFO - 📊 Tabla de conversión cargada: 796 registros
2025-06-07 23:16:42,841 - LogUsuariosPipeline - INFO - 🔄 Aplicando función crítica de expansión de registros...
2025-06-07 23:16:42,841 - LogUsuariosPipeline - INFO - 📊 Registros antes de expansión: 13,049
2025-06-07 23:16:43,424 - LogUsuariosPipeline - INFO - 📊 Registros después de expansión: 13,077
2025-06-07 23:16:43,424 - LogUsuariosPipeline - INFO - ✅ Función crítica de expansión aplicada exitosamente
2025-06-07 23:16:43,424 - LogUsuariosPipeline - INFO - 🔄 Aplicando mapeo de columnas...
2025-06-07 23:16:43,426 - LogUsuariosPipeline - INFO - 🔄 Procesando tipos de transacciones válidas...
2025-06-07 23:16:43,476 - LogUsuariosPipeline - INFO - 📊 Registros después de concatenar: 13,077
2025-06-07 23:16:59,086 - LogUsuariosPipeline - INFO - 📊 Registros finales para exportar: 13,077
2025-06-07 23:16:59,086 - LogUsuariosPipeline - INFO - 📁 Generando archivos CSV por BankDomain...
2025-06-07 23:16:59,151 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-FCOMPARTAMOS-********231659.csv (12,956 registros)
2025-06-07 23:16:59,154 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-BNACION-********231659.csv (111 registros)
2025-06-07 23:16:59,155 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-CRANDES-********231659.csv (2 registros)
2025-06-07 23:16:59,156 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-CCUSCO-********231659.csv (8 registros)
2025-06-07 23:16:59,156 - LogUsuariosPipeline - INFO - ✅ Procesamiento completado: 4 archivos generados
2025-06-07 23:16:59,156 - LogUsuariosPipeline - INFO - 📊 Total registros procesados: 13,077
2025-06-07 23:16:59,209 - LogUsuariosPipeline - INFO - ✅ Procesamiento exacto completado: 4 archivos
2025-06-07 23:16:59,210 - LogUsuariosPipeline - INFO -   📁 LOGUSR-FCOMPARTAMOS-********231659.csv
2025-06-07 23:16:59,210 - LogUsuariosPipeline - INFO -   📁 LOGUSR-BNACION-********231659.csv
2025-06-07 23:16:59,210 - LogUsuariosPipeline - INFO -   📁 LOGUSR-CRANDES-********231659.csv
2025-06-07 23:16:59,210 - LogUsuariosPipeline - INFO -   📁 LOGUSR-CCUSCO-********231659.csv
2025-06-07 23:16:59,210 - LogUsuariosPipeline - INFO - Archivo temporal eliminado: TEMP_LOGS_USUARIOS/********/WALLET_OLD.parquet
2025-06-07 23:16:59,210 - LogUsuariosPipeline - INFO - EXPORT-CSV: OK
2025-06-07 23:16:59,210 - LogUsuariosPipeline - INFO - === ARCHIVOS CSV GENERADOS ===
2025-06-07 23:16:59,210 - LogUsuariosPipeline - INFO - Total de archivos CSV segmentados: 11
2025-06-07 23:16:59,210 - LogUsuariosPipeline - INFO - Archivos por BankDomain (7) - EXACTO como flujo original:
2025-06-07 23:16:59,210 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-0231FCONFIANZA-********.csv
2025-06-07 23:16:59,210 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-BNACION-********.csv
2025-06-07 23:16:59,210 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-CCUSCO-********.csv
2025-06-07 23:16:59,210 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-CRANDES-********.csv
2025-06-07 23:16:59,210 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-FCOMPARTAMOS-********.csv
2025-06-07 23:16:59,210 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-FCONFIANZA-********.csv
2025-06-07 23:16:59,210 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-NULL-********.csv
2025-06-07 23:16:59,210 - LogUsuariosPipeline - INFO - Estructura OPTIMIZADA: solo archivos segmentados (datos completos en Parquet)
2025-06-07 23:16:59,210 - LogUsuariosPipeline - INFO - === PIPELINE COMPLETADO EXITOSAMENTE ===
2025-06-07 23:16:59,210 - LogUsuariosPipeline - INFO - 
=== ARCHIVOS GENERADOS ===
2025-06-07 23:16:59,210 - LogUsuariosPipeline - INFO - Archivos temporales en TEMP_LOGS_USUARIOS/********:
2025-06-07 23:16:59,211 - LogUsuariosPipeline - INFO -   - USER_ACCOUNT_HISTORY.parquet (0.00 MB)
2025-06-07 23:16:59,211 - LogUsuariosPipeline - INFO -   - USER_DATA_TRX.parquet (127.15 MB)
2025-06-07 23:16:59,211 - LogUsuariosPipeline - INFO -   - USER_MODIFICATION_DAY.parquet (3.36 MB)
2025-06-07 23:16:59,211 - LogUsuariosPipeline - INFO -   - USER_AUTH_CHANGE_HISTORY.parquet (0.11 MB)
2025-06-07 23:16:59,211 - LogUsuariosPipeline - INFO - Archivos de salida en output/********:
2025-06-07 23:16:59,211 - LogUsuariosPipeline - INFO -   - LOG_USR.parquet (4.34 MB)
2025-06-07 23:16:59,211 - LogUsuariosPipeline - INFO -   - LOG_USR_DEDUPLICATED.parquet (4.23 MB)
2025-06-07 23:16:59,211 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-0231FCONFIANZA-********.csv (0.00 MB)
2025-06-07 23:16:59,211 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-BNACION-********.csv (0.11 MB)
2025-06-07 23:16:59,211 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-CCUSCO-********.csv (0.00 MB)
2025-06-07 23:16:59,211 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-CRANDES-********.csv (0.00 MB)
2025-06-07 23:16:59,211 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-FCOMPARTAMOS-********.csv (30.92 MB)
2025-06-07 23:16:59,211 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-FCONFIANZA-********.csv (0.00 MB)
2025-06-07 23:16:59,211 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-NULL-********.csv (0.00 MB)
2025-06-07 23:16:59,211 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-FCOMPARTAMOS-********231659.csv (2.00 MB)
2025-06-07 23:16:59,211 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-BNACION-********231659.csv (0.01 MB)
2025-06-07 23:16:59,211 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-CRANDES-********231659.csv (0.00 MB)
2025-06-07 23:16:59,212 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-CCUSCO-********231659.csv (0.00 MB)
2025-06-07 23:25:40,995 - LogUsuariosPipeline - INFO - Configurando credenciales S3...
2025-06-07 23:25:41,008 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-07 23:25:41,058 - LogUsuariosPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-07 23:25:41,059 - LogUsuariosPipeline - INFO - === INICIANDO PIPELINE LOG_USUARIOS MODERNIZADO ===
2025-06-07 23:25:41,059 - LogUsuariosPipeline - INFO - Fecha de procesamiento: 2025-06-06 (carpeta: ********)
2025-06-07 23:25:41,059 - LogUsuariosPipeline - INFO - USER-MODIFY: INICIANDO
2025-06-07 23:25:41,059 - LogUsuariosPipeline - INFO - Extrayendo USER_ACCOUNT_HISTORY para fecha: 2025-06-06
2025-06-07 23:25:41,210 - LogUsuariosPipeline - INFO - USER_ACCOUNT_HISTORY extraído: 7 registros -> TEMP_LOGS_USUARIOS/********/USER_ACCOUNT_HISTORY.parquet
2025-06-07 23:25:41,210 - LogUsuariosPipeline - INFO - USER-MODIFY: OK
2025-06-07 23:25:41,211 - LogUsuariosPipeline - INFO - PRE-LOG-USR: INICIANDO
2025-06-07 23:25:41,211 - LogUsuariosPipeline - INFO - Ejecutando SP_PRE_LOG_USR CORREGIDO (incluye USER_IDs múltiples) para fecha: 2025-06-06
2025-06-07 23:25:49,470 - LogUsuariosPipeline - INFO - SP_PRE_LOG_USR completado: 1583130 registros -> TEMP_LOGS_USUARIOS/********/USER_DATA_TRX.parquet
2025-06-07 23:25:49,470 - LogUsuariosPipeline - INFO - Ejecutando SP_USER_MODIFICATION para fecha: 2025-06-06
2025-06-07 23:25:52,659 - LogUsuariosPipeline - INFO - SP_USER_MODIFICATION completado: 6908 registros -> TEMP_LOGS_USUARIOS/********/USER_MODIFICATION_DAY.parquet
2025-06-07 23:25:52,659 - LogUsuariosPipeline - INFO - Ejecutando SP_USER_AUTH_DAY para fecha: 2025-06-06
2025-06-07 23:25:53,280 - LogUsuariosPipeline - INFO - SP_USER_AUTH_DAY completado: 5189 registros -> TEMP_LOGS_USUARIOS/********/USER_AUTH_CHANGE_HISTORY.parquet
2025-06-07 23:25:53,280 - LogUsuariosPipeline - INFO - PRE-LOG-USR: OK
2025-06-07 23:25:53,280 - LogUsuariosPipeline - INFO - LOG-USR: INICIANDO
2025-06-07 23:25:53,281 - LogUsuariosPipeline - INFO - Ejecutando SP_LOG_USR EXACTO (igual a Oracle) para fecha: 2025-06-06
2025-06-07 23:25:55,051 - LogUsuariosPipeline - INFO - SP_LOG_USR EXACTO completado: 19661 registros -> output/********/LOG_USR.parquet
2025-06-07 23:25:55,052 - LogUsuariosPipeline - INFO - LOG-USR: OK
2025-06-07 23:25:55,052 - LogUsuariosPipeline - INFO - EXPORT-CSV: INICIANDO
2025-06-07 23:25:55,052 - LogUsuariosPipeline - INFO - Iniciando exportación CSV con conversiones de perfiles para fecha: 2025-06-06
2025-06-07 23:25:55,052 - LogUsuariosPipeline - INFO - Aplicando SOLO deduplicación como Oracle original (SIN conversión de tipos)
2025-06-07 23:25:55,278 - LogUsuariosPipeline - INFO - Deduplicación completada (manteniendo REQUESTTYPE originales):
2025-06-07 23:25:55,278 - LogUsuariosPipeline - INFO -   - Registros originales: 19,661
2025-06-07 23:25:55,278 - LogUsuariosPipeline - INFO -   - Registros después de deduplicación: 16,069
2025-06-07 23:25:55,278 - LogUsuariosPipeline - INFO -   - Duplicados eliminados: 3,592
2025-06-07 23:25:55,280 - LogUsuariosPipeline - INFO - Tipos de transacción mantenidos:
2025-06-07 23:25:55,280 - LogUsuariosPipeline - INFO -   - User Modification: 6,579 registros
2025-06-07 23:25:55,280 - LogUsuariosPipeline - INFO -   - CHANGE_AUTH_FACTOR: 5,071 registros
2025-06-07 23:25:55,280 - LogUsuariosPipeline - INFO -   - ActivateUser: 1,756 registros
2025-06-07 23:25:55,280 - LogUsuariosPipeline - INFO -   - AfiliaUser: 1,756 registros
2025-06-07 23:25:55,280 - LogUsuariosPipeline - INFO -   - ClosedUserAccount: 289 registros
2025-06-07 23:25:55,280 - LogUsuariosPipeline - INFO -   - ClosedAccount: 289 registros
2025-06-07 23:25:55,280 - LogUsuariosPipeline - INFO -   - Delete User: 289 registros
2025-06-07 23:25:55,280 - LogUsuariosPipeline - INFO -   - Lock Wallet: 24 registros
2025-06-07 23:25:55,280 - LogUsuariosPipeline - INFO -   - Suspend User: 7 registros
2025-06-07 23:25:55,280 - LogUsuariosPipeline - INFO -   - Resume User: 6 registros
2025-06-07 23:25:55,280 - LogUsuariosPipeline - INFO - Aplicando conversiones de perfiles al archivo: output/********/LOG_USR_DEDUPLICATED.parquet
2025-06-07 23:25:55,280 - LogUsuariosPipeline - INFO - Cargando tabla de conversión de perfiles...
2025-06-07 23:25:55,283 - LogUsuariosPipeline - INFO - Tabla conv_perfil.csv cargada: 796 registros
2025-06-07 23:25:55,287 - LogUsuariosPipeline - INFO - Tabla de conversión de perfiles cargada exitosamente en DuckDB
2025-06-07 23:25:55,288 - LogUsuariosPipeline - ERROR - Error aplicando conversiones al archivo: Binder Error: Values list "cp_a" does not have a column named "PERFIL_CODIGO"

LINE 8: ...            LEFT JOIN conv_perfil_table cp_a ON l.perfilA = cp_a.PERFIL_CODIGO
                                                                       ^
2025-06-07 23:25:55,289 - LogUsuariosPipeline - INFO - Omitiendo CSV principal redundante - usando solo archivos Parquet y CSVs segmentados
2025-06-07 23:25:55,289 - LogUsuariosPipeline - INFO - Iniciando exportación segmentada por BankDomain
2025-06-07 23:25:55,289 - LogUsuariosPipeline - INFO - Procesando TODOS los bancos del sistema: ['0231FCONFIANZA', 'BNACION', 'CCUSCO', 'CRANDES', 'FCOMPARTAMOS', 'FCONFIANZA', None]
2025-06-07 23:25:55,292 - LogUsuariosPipeline - INFO - Exportado dominio 0231FCONFIANZA: 0 registros (archivo vacío SIN headers) -> output/********/csv_exports/by_bank/LOG-USUARIOS-0231FCONFIANZA-********.csv
2025-06-07 23:25:55,311 - LogUsuariosPipeline - INFO - Exportado dominio BNACION: 168 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-BNACION-********.csv
2025-06-07 23:25:55,318 - LogUsuariosPipeline - INFO - Exportado dominio CCUSCO: 0 registros (archivo vacío SIN headers) -> output/********/csv_exports/by_bank/LOG-USUARIOS-CCUSCO-********.csv
2025-06-07 23:25:55,325 - LogUsuariosPipeline - INFO - Exportado dominio CRANDES: 4 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-CRANDES-********.csv
2025-06-07 23:25:55,440 - LogUsuariosPipeline - INFO - Exportado dominio FCOMPARTAMOS: 19489 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-FCOMPARTAMOS-********.csv
2025-06-07 23:25:55,445 - LogUsuariosPipeline - INFO - Exportado dominio FCONFIANZA: 0 registros (archivo vacío SIN headers) -> output/********/csv_exports/by_bank/LOG-USUARIOS-FCONFIANZA-********.csv
2025-06-07 23:25:55,450 - LogUsuariosPipeline - INFO - Exportado dominio NULL: 0 registros (archivo vacío SIN headers) -> output/********/csv_exports/by_bank/LOG-USUARIOS-NULL-********.csv
2025-06-07 23:25:55,450 - LogUsuariosPipeline - INFO - Exportación segmentada completada: 7 archivos generados
2025-06-07 23:25:55,450 - LogUsuariosPipeline - INFO - Estructura EXACTA como flujo original: solo by_bank (sin by_profile)
2025-06-07 23:25:55,450 - LogUsuariosPipeline - INFO - 🔄 APLICANDO PROCESAMIENTO EXACTO como procesar.py...
2025-06-07 23:25:55,450 - LogUsuariosPipeline - INFO - 🔄 INICIANDO PROCESAMIENTO EXACTO como procesar.py
2025-06-07 23:25:55,450 - LogUsuariosPipeline - INFO - 📁 Input: output/********/LOG_USR.parquet
2025-06-07 23:25:55,450 - LogUsuariosPipeline - INFO - 📅 Fecha: 2025-06-06
2025-06-07 23:25:55,450 - LogUsuariosPipeline - INFO - 📁 Output: output/********/csv_exports/csv_final_procesado
2025-06-07 23:25:55,450 - LogUsuariosPipeline - INFO - 📊 Cargando datos desde LOG_USR.parquet...
2025-06-07 23:25:55,521 - LogUsuariosPipeline - INFO - 📊 Registros cargados: 19,661
2025-06-07 23:25:55,521 - LogUsuariosPipeline - INFO - 🔄 Procesando grupos por USERHISTID...
2025-06-07 23:25:58,771 - LogUsuariosPipeline - INFO - 📊 Convirtiendo a DataFrame...
2025-06-07 23:25:58,896 - LogUsuariosPipeline - INFO - 📊 Registros después del procesamiento inicial: 48,470
2025-06-07 23:25:58,896 - LogUsuariosPipeline - INFO - 🔍 Aplicando filtro de valores válidos...
2025-06-07 23:25:58,904 - LogUsuariosPipeline - INFO - 📊 Registros después del filtro: 17,983
2025-06-07 23:25:58,904 - LogUsuariosPipeline - INFO - 🥷 Aplicando filtros adicionales de calidad de datos...
2025-06-07 23:25:58,920 - LogUsuariosPipeline - INFO - 📊 Registros después de filtros de calidad: 17,983
2025-06-07 23:25:58,920 - LogUsuariosPipeline - INFO - 🔄 Agrupando y procesando JSON...
2025-06-07 23:26:01,942 - LogUsuariosPipeline - INFO - 📊 Registros después de eliminar duplicados: 13,049
2025-06-07 23:26:01,952 - LogUsuariosPipeline - INFO - 🔄 Aplicando asignaciones a columnas...
2025-06-07 23:26:02,984 - LogUsuariosPipeline - INFO - 📊 Cargando tabla de conversión de perfiles...
2025-06-07 23:26:02,986 - LogUsuariosPipeline - INFO - 📊 Tabla de conversión cargada: 796 registros
2025-06-07 23:26:03,318 - LogUsuariosPipeline - INFO - 🔄 Aplicando función crítica de expansión de registros...
2025-06-07 23:26:03,318 - LogUsuariosPipeline - INFO - 📊 Registros antes de expansión: 13,049
2025-06-07 23:26:03,895 - LogUsuariosPipeline - INFO - 📊 Registros después de expansión: 13,077
2025-06-07 23:26:03,895 - LogUsuariosPipeline - INFO - ✅ Función crítica de expansión aplicada exitosamente
2025-06-07 23:26:03,895 - LogUsuariosPipeline - INFO - 🔄 Aplicando mapeo de columnas...
2025-06-07 23:26:03,897 - LogUsuariosPipeline - INFO - 🔄 Procesando tipos de transacciones válidas...
2025-06-07 23:26:03,947 - LogUsuariosPipeline - INFO - 📊 Registros después de concatenar: 13,077
2025-06-07 23:26:19,556 - LogUsuariosPipeline - INFO - 📊 Registros finales para exportar: 13,077
2025-06-07 23:26:19,556 - LogUsuariosPipeline - INFO - 📁 Generando archivos CSV por BankDomain...
2025-06-07 23:26:19,619 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-FCOMPARTAMOS-********232619.csv (12,956 registros)
2025-06-07 23:26:19,622 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-BNACION-********232619.csv (111 registros)
2025-06-07 23:26:19,623 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-CRANDES-********232619.csv (2 registros)
2025-06-07 23:26:19,624 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-CCUSCO-********232619.csv (8 registros)
2025-06-07 23:26:19,624 - LogUsuariosPipeline - INFO - ✅ Procesamiento completado: 4 archivos generados
2025-06-07 23:26:19,624 - LogUsuariosPipeline - INFO - 📊 Total registros procesados: 13,077
2025-06-07 23:26:19,677 - LogUsuariosPipeline - INFO - ✅ Procesamiento exacto completado: 4 archivos
2025-06-07 23:26:19,677 - LogUsuariosPipeline - INFO -   📁 LOGUSR-FCOMPARTAMOS-********232619.csv
2025-06-07 23:26:19,677 - LogUsuariosPipeline - INFO -   📁 LOGUSR-BNACION-********232619.csv
2025-06-07 23:26:19,677 - LogUsuariosPipeline - INFO -   📁 LOGUSR-CRANDES-********232619.csv
2025-06-07 23:26:19,677 - LogUsuariosPipeline - INFO -   📁 LOGUSR-CCUSCO-********232619.csv
2025-06-07 23:26:19,677 - LogUsuariosPipeline - INFO - Archivo temporal eliminado: TEMP_LOGS_USUARIOS/********/WALLET_OLD.parquet
2025-06-07 23:26:19,677 - LogUsuariosPipeline - INFO - EXPORT-CSV: OK
2025-06-07 23:26:19,677 - LogUsuariosPipeline - INFO - === ARCHIVOS CSV GENERADOS ===
2025-06-07 23:26:19,677 - LogUsuariosPipeline - INFO - Total de archivos CSV segmentados: 11
2025-06-07 23:26:19,677 - LogUsuariosPipeline - INFO - Archivos por BankDomain (7) - EXACTO como flujo original:
2025-06-07 23:26:19,677 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-0231FCONFIANZA-********.csv
2025-06-07 23:26:19,677 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-BNACION-********.csv
2025-06-07 23:26:19,678 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-CCUSCO-********.csv
2025-06-07 23:26:19,678 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-CRANDES-********.csv
2025-06-07 23:26:19,678 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-FCOMPARTAMOS-********.csv
2025-06-07 23:26:19,678 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-FCONFIANZA-********.csv
2025-06-07 23:26:19,678 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-NULL-********.csv
2025-06-07 23:26:19,678 - LogUsuariosPipeline - INFO - Estructura OPTIMIZADA: solo archivos segmentados (datos completos en Parquet)
2025-06-07 23:26:19,678 - LogUsuariosPipeline - INFO - === PIPELINE COMPLETADO EXITOSAMENTE ===
2025-06-07 23:26:19,678 - LogUsuariosPipeline - INFO - 
=== ARCHIVOS GENERADOS ===
2025-06-07 23:26:19,678 - LogUsuariosPipeline - INFO - Archivos temporales en TEMP_LOGS_USUARIOS/********:
2025-06-07 23:26:19,678 - LogUsuariosPipeline - INFO -   - USER_ACCOUNT_HISTORY.parquet (0.00 MB)
2025-06-07 23:26:19,678 - LogUsuariosPipeline - INFO -   - USER_DATA_TRX.parquet (127.15 MB)
2025-06-07 23:26:19,678 - LogUsuariosPipeline - INFO -   - USER_MODIFICATION_DAY.parquet (3.36 MB)
2025-06-07 23:26:19,678 - LogUsuariosPipeline - INFO -   - USER_AUTH_CHANGE_HISTORY.parquet (0.11 MB)
2025-06-07 23:26:19,678 - LogUsuariosPipeline - INFO - Archivos de salida en output/********:
2025-06-07 23:26:19,678 - LogUsuariosPipeline - INFO -   - LOG_USR.parquet (4.34 MB)
2025-06-07 23:26:19,678 - LogUsuariosPipeline - INFO -   - LOG_USR_DEDUPLICATED.parquet (4.23 MB)
2025-06-07 23:26:19,678 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-0231FCONFIANZA-********.csv (0.00 MB)
2025-06-07 23:26:19,678 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-BNACION-********.csv (0.11 MB)
2025-06-07 23:26:19,679 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-CCUSCO-********.csv (0.00 MB)
2025-06-07 23:26:19,679 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-CRANDES-********.csv (0.00 MB)
2025-06-07 23:26:19,679 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-FCOMPARTAMOS-********.csv (30.92 MB)
2025-06-07 23:26:19,679 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-FCONFIANZA-********.csv (0.00 MB)
2025-06-07 23:26:19,679 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-NULL-********.csv (0.00 MB)
2025-06-07 23:26:19,679 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-FCOMPARTAMOS-********232619.csv (2.00 MB)
2025-06-07 23:26:19,679 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-BNACION-********232619.csv (0.01 MB)
2025-06-07 23:26:19,679 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-CRANDES-********232619.csv (0.00 MB)
2025-06-07 23:26:19,679 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-CCUSCO-********232619.csv (0.00 MB)
2025-06-08 16:14:55,978 - LogUsuariosPipeline - INFO - Configurando credenciales S3...
2025-06-08 16:14:55,990 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-08 16:14:56,042 - LogUsuariosPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-08 16:14:56,043 - LogUsuariosPipeline - INFO - === INICIANDO PIPELINE LOG_USUARIOS MODERNIZADO ===
2025-06-08 16:14:56,043 - LogUsuariosPipeline - INFO - Fecha de procesamiento: 2025-06-06 (carpeta: ********)
2025-06-08 16:14:56,043 - LogUsuariosPipeline - INFO - USER-MODIFY: INICIANDO
2025-06-08 16:14:56,044 - LogUsuariosPipeline - INFO - Extrayendo USER_ACCOUNT_HISTORY para fecha: 2025-06-06
2025-06-08 16:14:56,227 - LogUsuariosPipeline - INFO - USER_ACCOUNT_HISTORY extraído: 8 registros -> TEMP_LOGS_USUARIOS/********/USER_ACCOUNT_HISTORY.parquet
2025-06-08 16:14:56,227 - LogUsuariosPipeline - INFO - USER-MODIFY: OK
2025-06-08 16:14:56,227 - LogUsuariosPipeline - INFO - PRE-LOG-USR: INICIANDO
2025-06-08 16:14:56,227 - LogUsuariosPipeline - INFO - Ejecutando SP_PRE_LOG_USR CORREGIDO (incluye USER_IDs múltiples) para fecha: 2025-06-06
2025-06-08 16:15:05,581 - LogUsuariosPipeline - INFO - SP_PRE_LOG_USR completado: 1583130 registros -> TEMP_LOGS_USUARIOS/********/USER_DATA_TRX.parquet
2025-06-08 16:15:05,581 - LogUsuariosPipeline - INFO - Ejecutando SP_USER_MODIFICATION para fecha: 2025-06-06
2025-06-08 16:15:08,820 - LogUsuariosPipeline - INFO - SP_USER_MODIFICATION completado: 6908 registros -> TEMP_LOGS_USUARIOS/********/USER_MODIFICATION_DAY.parquet
2025-06-08 16:15:08,821 - LogUsuariosPipeline - INFO - Ejecutando SP_USER_AUTH_DAY para fecha: 2025-06-06
2025-06-08 16:15:09,479 - LogUsuariosPipeline - INFO - SP_USER_AUTH_DAY completado: 5189 registros -> TEMP_LOGS_USUARIOS/********/USER_AUTH_CHANGE_HISTORY.parquet
2025-06-08 16:15:09,479 - LogUsuariosPipeline - INFO - PRE-LOG-USR: OK
2025-06-08 16:15:09,479 - LogUsuariosPipeline - INFO - LOG-USR: INICIANDO
2025-06-08 16:15:09,479 - LogUsuariosPipeline - INFO - Ejecutando SP_LOG_USR EXACTO (igual a Oracle) para fecha: 2025-06-06
2025-06-08 16:15:11,167 - LogUsuariosPipeline - INFO - SP_LOG_USR EXACTO completado: 19668 registros -> output/********/LOG_USR.parquet
2025-06-08 16:15:11,168 - LogUsuariosPipeline - INFO - LOG-USR: OK
2025-06-08 16:15:11,168 - LogUsuariosPipeline - INFO - EXPORT-CSV: INICIANDO
2025-06-08 16:15:11,168 - LogUsuariosPipeline - INFO - Iniciando exportación CSV con conversiones de perfiles para fecha: 2025-06-06
2025-06-08 16:15:11,168 - LogUsuariosPipeline - INFO - Aplicando SOLO deduplicación como Oracle original (SIN conversión de tipos)
2025-06-08 16:15:11,450 - LogUsuariosPipeline - INFO - Deduplicación completada (manteniendo REQUESTTYPE originales):
2025-06-08 16:15:11,450 - LogUsuariosPipeline - INFO -   - Registros originales: 19,668
2025-06-08 16:15:11,450 - LogUsuariosPipeline - INFO -   - Registros después de deduplicación: 16,069
2025-06-08 16:15:11,450 - LogUsuariosPipeline - INFO -   - Duplicados eliminados: 3,599
2025-06-08 16:15:11,454 - LogUsuariosPipeline - INFO - Tipos de transacción mantenidos:
2025-06-08 16:15:11,454 - LogUsuariosPipeline - INFO -   - User Modification: 6,579 registros
2025-06-08 16:15:11,454 - LogUsuariosPipeline - INFO -   - CHANGE_AUTH_FACTOR: 5,071 registros
2025-06-08 16:15:11,454 - LogUsuariosPipeline - INFO -   - AfiliaUser: 1,756 registros
2025-06-08 16:15:11,454 - LogUsuariosPipeline - INFO -   - ActivateUser: 1,756 registros
2025-06-08 16:15:11,454 - LogUsuariosPipeline - INFO -   - ClosedUserAccount: 289 registros
2025-06-08 16:15:11,454 - LogUsuariosPipeline - INFO -   - Delete User: 289 registros
2025-06-08 16:15:11,454 - LogUsuariosPipeline - INFO -   - ClosedAccount: 289 registros
2025-06-08 16:15:11,454 - LogUsuariosPipeline - INFO -   - Lock Wallet: 24 registros
2025-06-08 16:15:11,454 - LogUsuariosPipeline - INFO -   - Suspend User: 7 registros
2025-06-08 16:15:11,454 - LogUsuariosPipeline - INFO -   - Resume User: 6 registros
2025-06-08 16:15:11,454 - LogUsuariosPipeline - INFO - Aplicando conversiones de perfiles al archivo: output/********/LOG_USR_DEDUPLICATED.parquet
2025-06-08 16:15:11,454 - LogUsuariosPipeline - INFO - Cargando tabla de conversión de perfiles...
2025-06-08 16:15:11,457 - LogUsuariosPipeline - INFO - Tabla conv_perfil.csv cargada: 796 registros
2025-06-08 16:15:11,462 - LogUsuariosPipeline - INFO - Tabla de conversión de perfiles cargada exitosamente en DuckDB
2025-06-08 16:15:11,464 - LogUsuariosPipeline - ERROR - Error aplicando conversiones al archivo: Binder Error: Values list "cp_a" does not have a column named "PERFIL_CODIGO"

LINE 8: ...            LEFT JOIN conv_perfil_table cp_a ON l.perfilA = cp_a.PERFIL_CODIGO
                                                                       ^
2025-06-08 16:15:11,464 - LogUsuariosPipeline - INFO - Omitiendo CSV principal redundante - usando solo archivos Parquet y CSVs segmentados
2025-06-08 16:15:11,464 - LogUsuariosPipeline - INFO - Iniciando exportación segmentada por BankDomain
2025-06-08 16:15:11,464 - LogUsuariosPipeline - INFO - Procesando TODOS los bancos del sistema: ['0231FCONFIANZA', 'BNACION', 'CCUSCO', 'CRANDES', 'FCOMPARTAMOS', 'FCONFIANZA', None]
2025-06-08 16:15:11,468 - LogUsuariosPipeline - INFO - Exportado dominio 0231FCONFIANZA: 0 registros (archivo vacío SIN headers) -> output/********/csv_exports/by_bank/LOG-USUARIOS-0231FCONFIANZA-********.csv
2025-06-08 16:15:11,490 - LogUsuariosPipeline - INFO - Exportado dominio BNACION: 168 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-BNACION-********.csv
2025-06-08 16:15:11,499 - LogUsuariosPipeline - INFO - Exportado dominio CCUSCO: 0 registros (archivo vacío SIN headers) -> output/********/csv_exports/by_bank/LOG-USUARIOS-CCUSCO-********.csv
2025-06-08 16:15:11,505 - LogUsuariosPipeline - INFO - Exportado dominio CRANDES: 4 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-CRANDES-********.csv
2025-06-08 16:15:11,625 - LogUsuariosPipeline - INFO - Exportado dominio FCOMPARTAMOS: 19496 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-FCOMPARTAMOS-********.csv
2025-06-08 16:15:11,629 - LogUsuariosPipeline - INFO - Exportado dominio FCONFIANZA: 0 registros (archivo vacío SIN headers) -> output/********/csv_exports/by_bank/LOG-USUARIOS-FCONFIANZA-********.csv
2025-06-08 16:15:11,634 - LogUsuariosPipeline - INFO - Exportado dominio NULL: 0 registros (archivo vacío SIN headers) -> output/********/csv_exports/by_bank/LOG-USUARIOS-NULL-********.csv
2025-06-08 16:15:11,634 - LogUsuariosPipeline - INFO - Exportación segmentada completada: 7 archivos generados
2025-06-08 16:15:11,634 - LogUsuariosPipeline - INFO - Estructura EXACTA como flujo original: solo by_bank (sin by_profile)
2025-06-08 16:15:11,634 - LogUsuariosPipeline - INFO - 🔄 APLICANDO PROCESAMIENTO EXACTO como procesar.py...
2025-06-08 16:15:11,635 - LogUsuariosPipeline - INFO - 🔄 INICIANDO PROCESAMIENTO EXACTO como procesar.py
2025-06-08 16:15:11,635 - LogUsuariosPipeline - INFO - 📁 Input: output/********/LOG_USR.parquet
2025-06-08 16:15:11,635 - LogUsuariosPipeline - INFO - 📅 Fecha: 2025-06-06
2025-06-08 16:15:11,635 - LogUsuariosPipeline - INFO - 📁 Output: output/********/csv_exports/csv_final_procesado
2025-06-08 16:15:11,635 - LogUsuariosPipeline - INFO - 📊 Cargando datos desde LOG_USR.parquet...
2025-06-08 16:15:11,704 - LogUsuariosPipeline - INFO - 📊 Registros cargados: 19,668
2025-06-08 16:15:11,704 - LogUsuariosPipeline - INFO - 🔄 Procesando grupos por USERHISTID...
2025-06-08 16:15:15,004 - LogUsuariosPipeline - INFO - 📊 Convirtiendo a DataFrame...
2025-06-08 16:15:15,131 - LogUsuariosPipeline - INFO - 📊 Registros después del procesamiento inicial: 48,477
2025-06-08 16:15:15,131 - LogUsuariosPipeline - INFO - 🔍 Aplicando filtro de valores válidos...
2025-06-08 16:15:15,140 - LogUsuariosPipeline - INFO - 📊 Registros después del filtro: 17,990
2025-06-08 16:15:15,140 - LogUsuariosPipeline - INFO - 🥷 Aplicando filtros adicionales de calidad de datos...
2025-06-08 16:15:15,156 - LogUsuariosPipeline - INFO - 📊 Registros después de filtros de calidad: 17,990
2025-06-08 16:15:15,156 - LogUsuariosPipeline - INFO - 🔄 Agrupando y procesando JSON...
2025-06-08 16:15:18,228 - LogUsuariosPipeline - INFO - 📊 Registros después de eliminar duplicados: 13,049
2025-06-08 16:15:18,237 - LogUsuariosPipeline - INFO - 🔄 Aplicando asignaciones a columnas...
2025-06-08 16:15:19,304 - LogUsuariosPipeline - INFO - 📊 Cargando tabla de conversión de perfiles...
2025-06-08 16:15:19,306 - LogUsuariosPipeline - INFO - 📊 Tabla de conversión cargada: 796 registros
2025-06-08 16:15:19,650 - LogUsuariosPipeline - INFO - 🔄 Aplicando función crítica de expansión de registros...
2025-06-08 16:15:19,650 - LogUsuariosPipeline - INFO - 📊 Registros antes de expansión: 13,049
2025-06-08 16:15:20,236 - LogUsuariosPipeline - INFO - 📊 Registros después de expansión: 13,077
2025-06-08 16:15:20,236 - LogUsuariosPipeline - INFO - ✅ Función crítica de expansión aplicada exitosamente
2025-06-08 16:15:20,236 - LogUsuariosPipeline - INFO - 🔄 Aplicando mapeo de columnas...
2025-06-08 16:15:20,238 - LogUsuariosPipeline - INFO - 🔄 Procesando tipos de transacciones válidas...
2025-06-08 16:15:20,288 - LogUsuariosPipeline - INFO - 📊 Registros después de concatenar: 13,077
2025-06-08 16:15:35,898 - LogUsuariosPipeline - INFO - 📊 Registros finales para exportar: 13,077
2025-06-08 16:15:35,898 - LogUsuariosPipeline - INFO - 📁 Generando archivos CSV por BankDomain...
2025-06-08 16:15:35,963 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-FCOMPARTAMOS-********161535.csv (12,956 registros)
2025-06-08 16:15:35,966 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-BNACION-********161535.csv (111 registros)
2025-06-08 16:15:35,967 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-CRANDES-********161535.csv (2 registros)
2025-06-08 16:15:35,968 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-CCUSCO-********161535.csv (8 registros)
2025-06-08 16:15:35,968 - LogUsuariosPipeline - INFO - ✅ Procesamiento completado: 4 archivos generados
2025-06-08 16:15:35,968 - LogUsuariosPipeline - INFO - 📊 Total registros procesados: 13,077
2025-06-08 16:15:36,023 - LogUsuariosPipeline - INFO - ✅ Procesamiento exacto completado: 4 archivos
2025-06-08 16:15:36,023 - LogUsuariosPipeline - INFO -   📁 LOGUSR-FCOMPARTAMOS-********161535.csv
2025-06-08 16:15:36,023 - LogUsuariosPipeline - INFO -   📁 LOGUSR-BNACION-********161535.csv
2025-06-08 16:15:36,023 - LogUsuariosPipeline - INFO -   📁 LOGUSR-CRANDES-********161535.csv
2025-06-08 16:15:36,023 - LogUsuariosPipeline - INFO -   📁 LOGUSR-CCUSCO-********161535.csv
2025-06-08 16:15:36,024 - LogUsuariosPipeline - INFO - Archivo temporal eliminado: TEMP_LOGS_USUARIOS/********/WALLET_OLD.parquet
2025-06-08 16:15:36,024 - LogUsuariosPipeline - INFO - EXPORT-CSV: OK
2025-06-08 16:15:36,024 - LogUsuariosPipeline - INFO - === ARCHIVOS CSV GENERADOS ===
2025-06-08 16:15:36,024 - LogUsuariosPipeline - INFO - Total de archivos CSV segmentados: 11
2025-06-08 16:15:36,024 - LogUsuariosPipeline - INFO - Archivos por BankDomain (7) - EXACTO como flujo original:
2025-06-08 16:15:36,024 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-0231FCONFIANZA-********.csv
2025-06-08 16:15:36,024 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-BNACION-********.csv
2025-06-08 16:15:36,024 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-CCUSCO-********.csv
2025-06-08 16:15:36,024 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-CRANDES-********.csv
2025-06-08 16:15:36,024 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-FCOMPARTAMOS-********.csv
2025-06-08 16:15:36,024 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-FCONFIANZA-********.csv
2025-06-08 16:15:36,024 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-NULL-********.csv
2025-06-08 16:15:36,024 - LogUsuariosPipeline - INFO - Estructura OPTIMIZADA: solo archivos segmentados (datos completos en Parquet)
2025-06-08 16:15:36,024 - LogUsuariosPipeline - INFO - === PIPELINE COMPLETADO EXITOSAMENTE ===
2025-06-08 16:15:36,024 - LogUsuariosPipeline - INFO - 
=== ARCHIVOS GENERADOS ===
2025-06-08 16:15:36,024 - LogUsuariosPipeline - INFO - Archivos temporales en TEMP_LOGS_USUARIOS/********:
2025-06-08 16:15:36,024 - LogUsuariosPipeline - INFO -   - USER_ACCOUNT_HISTORY.parquet (0.00 MB)
2025-06-08 16:15:36,024 - LogUsuariosPipeline - INFO -   - USER_DATA_TRX.parquet (127.15 MB)
2025-06-08 16:15:36,024 - LogUsuariosPipeline - INFO -   - USER_MODIFICATION_DAY.parquet (3.36 MB)
2025-06-08 16:15:36,024 - LogUsuariosPipeline - INFO -   - USER_AUTH_CHANGE_HISTORY.parquet (0.11 MB)
2025-06-08 16:15:36,024 - LogUsuariosPipeline - INFO - Archivos de salida en output/********:
2025-06-08 16:15:36,024 - LogUsuariosPipeline - INFO -   - LOG_USR.parquet (4.34 MB)
2025-06-08 16:15:36,025 - LogUsuariosPipeline - INFO -   - LOG_USR_DEDUPLICATED.parquet (4.23 MB)
2025-06-08 16:15:36,025 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-0231FCONFIANZA-********.csv (0.00 MB)
2025-06-08 16:15:36,025 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-BNACION-********.csv (0.11 MB)
2025-06-08 16:15:36,025 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-CCUSCO-********.csv (0.00 MB)
2025-06-08 16:15:36,025 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-CRANDES-********.csv (0.00 MB)
2025-06-08 16:15:36,025 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-FCOMPARTAMOS-********.csv (30.92 MB)
2025-06-08 16:15:36,025 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-FCONFIANZA-********.csv (0.00 MB)
2025-06-08 16:15:36,025 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-NULL-********.csv (0.00 MB)
2025-06-08 16:15:36,025 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-FCOMPARTAMOS-********161535.csv (2.00 MB)
2025-06-08 16:15:36,025 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-BNACION-********161535.csv (0.01 MB)
2025-06-08 16:15:36,025 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-CRANDES-********161535.csv (0.00 MB)
2025-06-08 16:15:36,025 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-CCUSCO-********161535.csv (0.00 MB)
2025-06-08 22:44:23,707 - LogUsuariosPipeline - INFO - Configurando credenciales S3...
2025-06-08 22:44:23,720 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-08 22:44:23,770 - LogUsuariosPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-08 22:44:23,771 - LogUsuariosPipeline - INFO - === INICIANDO PIPELINE LOG_USUARIOS MODERNIZADO ===
2025-06-08 22:44:23,771 - LogUsuariosPipeline - INFO - Fecha de procesamiento: 2025-05-05 (carpeta: ********)
2025-06-08 22:44:23,771 - LogUsuariosPipeline - INFO - USER-MODIFY: INICIANDO
2025-06-08 22:44:23,771 - LogUsuariosPipeline - INFO - Extrayendo USER_ACCOUNT_HISTORY para fecha: 2025-05-05
2025-06-08 22:44:23,950 - LogUsuariosPipeline - INFO - USER_ACCOUNT_HISTORY extraído: 170 registros -> TEMP_LOGS_USUARIOS/********/USER_ACCOUNT_HISTORY.parquet
2025-06-08 22:44:23,950 - LogUsuariosPipeline - INFO - USER-MODIFY: OK
2025-06-08 22:44:23,950 - LogUsuariosPipeline - INFO - PRE-LOG-USR: INICIANDO
2025-06-08 22:44:23,950 - LogUsuariosPipeline - INFO - Ejecutando SP_PRE_LOG_USR CORREGIDO (incluye USER_IDs múltiples) para fecha: 2025-05-05
2025-06-08 22:44:32,814 - LogUsuariosPipeline - INFO - SP_PRE_LOG_USR completado: 1536340 registros -> TEMP_LOGS_USUARIOS/********/USER_DATA_TRX.parquet
2025-06-08 22:44:32,815 - LogUsuariosPipeline - INFO - Ejecutando SP_USER_MODIFICATION para fecha: 2025-05-05
2025-06-08 22:44:34,851 - LogUsuariosPipeline - INFO - SP_USER_MODIFICATION completado: 6039 registros -> TEMP_LOGS_USUARIOS/********/USER_MODIFICATION_DAY.parquet
2025-06-08 22:44:34,852 - LogUsuariosPipeline - INFO - Ejecutando SP_USER_AUTH_DAY para fecha: 2025-05-05
2025-06-08 22:44:35,456 - LogUsuariosPipeline - INFO - SP_USER_AUTH_DAY completado: 7421 registros -> TEMP_LOGS_USUARIOS/********/USER_AUTH_CHANGE_HISTORY.parquet
2025-06-08 22:44:35,457 - LogUsuariosPipeline - INFO - PRE-LOG-USR: OK
2025-06-08 22:44:35,457 - LogUsuariosPipeline - INFO - LOG-USR: INICIANDO
2025-06-08 22:44:35,457 - LogUsuariosPipeline - INFO - Ejecutando SP_LOG_USR EXACTO (igual a Oracle) para fecha: 2025-05-05
2025-06-08 22:44:37,100 - LogUsuariosPipeline - INFO - SP_LOG_USR EXACTO completado: 23008 registros -> output/********/LOG_USR.parquet
2025-06-08 22:44:37,101 - LogUsuariosPipeline - INFO - LOG-USR: OK
2025-06-08 22:44:37,101 - LogUsuariosPipeline - INFO - EXPORT-CSV: INICIANDO
2025-06-08 22:44:37,101 - LogUsuariosPipeline - INFO - Iniciando exportación CSV con conversiones de perfiles para fecha: 2025-05-05
2025-06-08 22:44:37,101 - LogUsuariosPipeline - INFO - Aplicando SOLO deduplicación como Oracle original (SIN conversión de tipos)
2025-06-08 22:44:37,338 - LogUsuariosPipeline - INFO - Deduplicación completada (manteniendo REQUESTTYPE originales):
2025-06-08 22:44:37,338 - LogUsuariosPipeline - INFO -   - Registros originales: 23,008
2025-06-08 22:44:37,338 - LogUsuariosPipeline - INFO -   - Registros después de deduplicación: 16,982
2025-06-08 22:44:37,338 - LogUsuariosPipeline - INFO -   - Duplicados eliminados: 6,026
2025-06-08 22:44:37,340 - LogUsuariosPipeline - INFO - Tipos de transacción mantenidos:
2025-06-08 22:44:37,340 - LogUsuariosPipeline - INFO -   - CHANGE_AUTH_FACTOR: 7,259 registros
2025-06-08 22:44:37,340 - LogUsuariosPipeline - INFO -   - User Modification: 5,811 registros
2025-06-08 22:44:37,340 - LogUsuariosPipeline - INFO -   - AfiliaUser: 1,644 registros
2025-06-08 22:44:37,340 - LogUsuariosPipeline - INFO -   - ActivateUser: 1,644 registros
2025-06-08 22:44:37,340 - LogUsuariosPipeline - INFO -   - ClosedUserAccount: 199 registros
2025-06-08 22:44:37,340 - LogUsuariosPipeline - INFO -   - ClosedAccount: 199 registros
2025-06-08 22:44:37,340 - LogUsuariosPipeline - INFO -   - Delete User: 199 registros
2025-06-08 22:44:37,340 - LogUsuariosPipeline - INFO -   - Unlock Wallet: 23 registros
2025-06-08 22:44:37,340 - LogUsuariosPipeline - INFO -   - Resume User: 3 registros
2025-06-08 22:44:37,340 - LogUsuariosPipeline - INFO -   - Suspend User: 1 registros
2025-06-08 22:44:37,340 - LogUsuariosPipeline - INFO - Aplicando conversiones de perfiles al archivo: output/********/LOG_USR_DEDUPLICATED.parquet
2025-06-08 22:44:37,340 - LogUsuariosPipeline - INFO - Cargando tabla de conversión de perfiles...
2025-06-08 22:44:37,343 - LogUsuariosPipeline - INFO - Tabla conv_perfil.csv cargada: 796 registros
2025-06-08 22:44:37,347 - LogUsuariosPipeline - INFO - Tabla de conversión de perfiles cargada exitosamente en DuckDB
2025-06-08 22:44:37,350 - LogUsuariosPipeline - ERROR - Error aplicando conversiones al archivo: Binder Error: Values list "cp_a" does not have a column named "PERFIL_CODIGO"

LINE 8: ...            LEFT JOIN conv_perfil_table cp_a ON l.perfilA = cp_a.PERFIL_CODIGO
                                                                       ^
2025-06-08 22:44:37,350 - LogUsuariosPipeline - INFO - Omitiendo CSV principal redundante - usando solo archivos Parquet y CSVs segmentados
2025-06-08 22:44:37,350 - LogUsuariosPipeline - INFO - Iniciando exportación segmentada por BankDomain
2025-06-08 22:44:37,350 - LogUsuariosPipeline - INFO - Procesando TODOS los bancos del sistema: ['0231FCONFIANZA', 'BNACION', 'CCUSCO', 'CRANDES', 'FCOMPARTAMOS', 'FCONFIANZA', None]
2025-06-08 22:44:37,360 - LogUsuariosPipeline - INFO - Exportado dominio 0231FCONFIANZA: 10 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-0231FCONFIANZA-********.csv
2025-06-08 22:44:37,384 - LogUsuariosPipeline - INFO - Exportado dominio BNACION: 114 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-BNACION-********.csv
2025-06-08 22:44:37,402 - LogUsuariosPipeline - INFO - Exportado dominio CCUSCO: 39 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-CCUSCO-********.csv
2025-06-08 22:44:37,423 - LogUsuariosPipeline - INFO - Exportado dominio CRANDES: 6 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-CRANDES-********.csv
2025-06-08 22:44:37,515 - LogUsuariosPipeline - INFO - Exportado dominio FCOMPARTAMOS: 22839 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-FCOMPARTAMOS-********.csv
2025-06-08 22:44:37,520 - LogUsuariosPipeline - INFO - Exportado dominio FCONFIANZA: 0 registros (archivo vacío SIN headers) -> output/********/csv_exports/by_bank/LOG-USUARIOS-FCONFIANZA-********.csv
2025-06-08 22:44:37,524 - LogUsuariosPipeline - INFO - Exportado dominio NULL: 0 registros (archivo vacío SIN headers) -> output/********/csv_exports/by_bank/LOG-USUARIOS-NULL-********.csv
2025-06-08 22:44:37,524 - LogUsuariosPipeline - INFO - Exportación segmentada completada: 7 archivos generados
2025-06-08 22:44:37,524 - LogUsuariosPipeline - INFO - Estructura EXACTA como flujo original: solo by_bank (sin by_profile)
2025-06-08 22:44:37,524 - LogUsuariosPipeline - INFO - 🔄 APLICANDO PROCESAMIENTO EXACTO como procesar.py...
2025-06-08 22:44:37,528 - LogUsuariosPipeline - INFO - 🔄 INICIANDO PROCESAMIENTO EXACTO como procesar.py
2025-06-08 22:44:37,528 - LogUsuariosPipeline - INFO - 📁 Input: output/********/LOG_USR.parquet
2025-06-08 22:44:37,529 - LogUsuariosPipeline - INFO - 📅 Fecha: 2025-05-05
2025-06-08 22:44:37,529 - LogUsuariosPipeline - INFO - 📁 Output: output/********/csv_exports/csv_final_procesado
2025-06-08 22:44:37,529 - LogUsuariosPipeline - INFO - 📊 Cargando datos desde LOG_USR.parquet...
2025-06-08 22:44:37,600 - LogUsuariosPipeline - INFO - 📊 Registros cargados: 23,008
2025-06-08 22:44:37,601 - LogUsuariosPipeline - INFO - 🔄 Procesando grupos por USERHISTID...
2025-06-08 22:44:40,953 - LogUsuariosPipeline - INFO - 📊 Convirtiendo a DataFrame...
2025-06-08 22:44:41,084 - LogUsuariosPipeline - INFO - 📊 Registros después del procesamiento inicial: 49,237
2025-06-08 22:44:41,085 - LogUsuariosPipeline - INFO - 🔍 Aplicando filtro de valores válidos...
2025-06-08 22:44:41,093 - LogUsuariosPipeline - INFO - 📊 Registros después del filtro: 21,609
2025-06-08 22:44:41,093 - LogUsuariosPipeline - INFO - 🥷 Aplicando filtros adicionales de calidad de datos...
2025-06-08 22:44:41,112 - LogUsuariosPipeline - INFO - 📊 Registros después de filtros de calidad: 21,609
2025-06-08 22:44:41,112 - LogUsuariosPipeline - INFO - 🔄 Agrupando y procesando JSON...
2025-06-08 22:44:44,594 - LogUsuariosPipeline - INFO - 📊 Registros después de eliminar duplicados: 14,349
2025-06-08 22:44:44,606 - LogUsuariosPipeline - INFO - 🔄 Aplicando asignaciones a columnas...
2025-06-08 22:44:45,730 - LogUsuariosPipeline - INFO - 📊 Cargando tabla de conversión de perfiles...
2025-06-08 22:44:45,732 - LogUsuariosPipeline - INFO - 📊 Tabla de conversión cargada: 796 registros
2025-06-08 22:44:46,046 - LogUsuariosPipeline - INFO - 🔄 Aplicando función crítica de expansión de registros...
2025-06-08 22:44:46,046 - LogUsuariosPipeline - INFO - 📊 Registros antes de expansión: 14,349
2025-06-08 22:44:46,752 - LogUsuariosPipeline - INFO - 📊 Registros después de expansión: 14,377
2025-06-08 22:44:46,752 - LogUsuariosPipeline - INFO - ✅ Función crítica de expansión aplicada exitosamente
2025-06-08 22:44:46,752 - LogUsuariosPipeline - INFO - 🔄 Aplicando mapeo de columnas...
2025-06-08 22:44:46,754 - LogUsuariosPipeline - INFO - 🔄 Procesando tipos de transacciones válidas...
2025-06-08 22:44:46,807 - LogUsuariosPipeline - INFO - 📊 Registros después de concatenar: 14,377
2025-06-08 22:45:03,931 - LogUsuariosPipeline - INFO - 📊 Registros finales para exportar: 14,377
2025-06-08 22:45:03,932 - LogUsuariosPipeline - INFO - 📁 Generando archivos CSV por BankDomain...
2025-06-08 22:45:04,003 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-FCOMPARTAMOS-**************.csv (14,275 registros)
2025-06-08 22:45:04,006 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-BNACION-**************.csv (64 registros)
2025-06-08 22:45:04,007 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-0231FCONFIANZA-**************.csv (5 registros)
2025-06-08 22:45:04,008 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-CCUSCO-**************.csv (21 registros)
2025-06-08 22:45:04,009 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-CRANDES-**************.csv (12 registros)
2025-06-08 22:45:04,009 - LogUsuariosPipeline - INFO - ✅ Procesamiento completado: 5 archivos generados
2025-06-08 22:45:04,009 - LogUsuariosPipeline - INFO - 📊 Total registros procesados: 14,377
2025-06-08 22:45:04,065 - LogUsuariosPipeline - INFO - ✅ Procesamiento exacto completado: 5 archivos
2025-06-08 22:45:04,065 - LogUsuariosPipeline - INFO -   📁 LOGUSR-FCOMPARTAMOS-**************.csv
2025-06-08 22:45:04,065 - LogUsuariosPipeline - INFO -   📁 LOGUSR-BNACION-**************.csv
2025-06-08 22:45:04,065 - LogUsuariosPipeline - INFO -   📁 LOGUSR-0231FCONFIANZA-**************.csv
2025-06-08 22:45:04,065 - LogUsuariosPipeline - INFO -   📁 LOGUSR-CCUSCO-**************.csv
2025-06-08 22:45:04,065 - LogUsuariosPipeline - INFO -   📁 LOGUSR-CRANDES-**************.csv
2025-06-08 22:45:04,065 - LogUsuariosPipeline - INFO - Archivo temporal eliminado: TEMP_LOGS_USUARIOS/********/WALLET_OLD.parquet
2025-06-08 22:45:04,066 - LogUsuariosPipeline - INFO - EXPORT-CSV: OK
2025-06-08 22:45:04,066 - LogUsuariosPipeline - INFO - === ARCHIVOS CSV GENERADOS ===
2025-06-08 22:45:04,066 - LogUsuariosPipeline - INFO - Total de archivos CSV segmentados: 12
2025-06-08 22:45:04,066 - LogUsuariosPipeline - INFO - Archivos por BankDomain (7) - EXACTO como flujo original:
2025-06-08 22:45:04,066 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-0231FCONFIANZA-********.csv
2025-06-08 22:45:04,066 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-BNACION-********.csv
2025-06-08 22:45:04,066 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-CCUSCO-********.csv
2025-06-08 22:45:04,066 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-CRANDES-********.csv
2025-06-08 22:45:04,066 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-FCOMPARTAMOS-********.csv
2025-06-08 22:45:04,066 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-FCONFIANZA-********.csv
2025-06-08 22:45:04,066 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-NULL-********.csv
2025-06-08 22:45:04,066 - LogUsuariosPipeline - INFO - Estructura OPTIMIZADA: solo archivos segmentados (datos completos en Parquet)
2025-06-08 22:45:04,066 - LogUsuariosPipeline - INFO - === PIPELINE COMPLETADO EXITOSAMENTE ===
2025-06-08 22:45:04,066 - LogUsuariosPipeline - INFO - 
=== ARCHIVOS GENERADOS ===
2025-06-08 22:45:04,066 - LogUsuariosPipeline - INFO - Archivos temporales en TEMP_LOGS_USUARIOS/********:
2025-06-08 22:45:04,066 - LogUsuariosPipeline - INFO -   - USER_ACCOUNT_HISTORY.parquet (0.01 MB)
2025-06-08 22:45:04,066 - LogUsuariosPipeline - INFO -   - USER_DATA_TRX.parquet (121.51 MB)
2025-06-08 22:45:04,066 - LogUsuariosPipeline - INFO -   - USER_MODIFICATION_DAY.parquet (2.96 MB)
2025-06-08 22:45:04,066 - LogUsuariosPipeline - INFO -   - USER_AUTH_CHANGE_HISTORY.parquet (0.16 MB)
2025-06-08 22:45:04,066 - LogUsuariosPipeline - INFO - Archivos de salida en output/********:
2025-06-08 22:45:04,066 - LogUsuariosPipeline - INFO -   - LOG_USR.parquet (4.20 MB)
2025-06-08 22:45:04,066 - LogUsuariosPipeline - INFO -   - LOG_USR_DEDUPLICATED.parquet (3.95 MB)
2025-06-08 22:45:04,067 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-0231FCONFIANZA-********.csv (0.00 MB)
2025-06-08 22:45:04,067 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-BNACION-********.csv (0.07 MB)
2025-06-08 22:45:04,067 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-CCUSCO-********.csv (0.01 MB)
2025-06-08 22:45:04,067 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-CRANDES-********.csv (0.01 MB)
2025-06-08 22:45:04,067 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-FCOMPARTAMOS-********.csv (28.93 MB)
2025-06-08 22:45:04,067 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-FCONFIANZA-********.csv (0.00 MB)
2025-06-08 22:45:04,067 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-NULL-********.csv (0.00 MB)
2025-06-08 22:45:04,067 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-FCOMPARTAMOS-**************.csv (2.09 MB)
2025-06-08 22:45:04,067 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-BNACION-**************.csv (0.01 MB)
2025-06-08 22:45:04,067 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-0231FCONFIANZA-**************.csv (0.00 MB)
2025-06-08 22:45:04,067 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-CCUSCO-**************.csv (0.00 MB)
2025-06-08 22:45:04,067 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-CRANDES-**************.csv (0.00 MB)
2025-06-08 23:09:47,271 - LogUsuariosPipeline - INFO - Configurando credenciales S3...
2025-06-08 23:09:47,285 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-08 23:09:47,339 - LogUsuariosPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-08 23:09:47,339 - LogUsuariosPipeline - INFO - === INICIANDO PIPELINE LOG_USUARIOS MODERNIZADO ===
2025-06-08 23:09:47,340 - LogUsuariosPipeline - INFO - Fecha de procesamiento: 2025-06-07 (carpeta: ********)
2025-06-08 23:09:47,340 - LogUsuariosPipeline - INFO - USER-MODIFY: INICIANDO
2025-06-08 23:09:47,340 - LogUsuariosPipeline - INFO - Extrayendo USER_ACCOUNT_HISTORY para fecha: 2025-06-07
2025-06-08 23:09:47,486 - LogUsuariosPipeline - INFO - USER_ACCOUNT_HISTORY extraído: 1 registros -> TEMP_LOGS_USUARIOS/********/USER_ACCOUNT_HISTORY.parquet
2025-06-08 23:09:47,487 - LogUsuariosPipeline - INFO - USER-MODIFY: OK
2025-06-08 23:09:47,487 - LogUsuariosPipeline - INFO - PRE-LOG-USR: INICIANDO
2025-06-08 23:09:47,487 - LogUsuariosPipeline - INFO - Ejecutando SP_PRE_LOG_USR CORREGIDO (incluye USER_IDs múltiples) para fecha: 2025-06-07
2025-06-08 23:09:55,190 - LogUsuariosPipeline - INFO - SP_PRE_LOG_USR completado: 1583524 registros -> TEMP_LOGS_USUARIOS/********/USER_DATA_TRX.parquet
2025-06-08 23:09:55,190 - LogUsuariosPipeline - INFO - Ejecutando SP_USER_MODIFICATION para fecha: 2025-06-07
2025-06-08 23:09:57,426 - LogUsuariosPipeline - INFO - SP_USER_MODIFICATION completado: 1335 registros -> TEMP_LOGS_USUARIOS/********/USER_MODIFICATION_DAY.parquet
2025-06-08 23:09:57,426 - LogUsuariosPipeline - INFO - Ejecutando SP_USER_AUTH_DAY para fecha: 2025-06-07
2025-06-08 23:09:57,870 - LogUsuariosPipeline - INFO - SP_USER_AUTH_DAY completado: 1501 registros -> TEMP_LOGS_USUARIOS/********/USER_AUTH_CHANGE_HISTORY.parquet
2025-06-08 23:09:57,870 - LogUsuariosPipeline - INFO - PRE-LOG-USR: OK
2025-06-08 23:09:57,870 - LogUsuariosPipeline - INFO - LOG-USR: INICIANDO
2025-06-08 23:09:57,870 - LogUsuariosPipeline - INFO - Ejecutando SP_LOG_USR EXACTO (igual a Oracle) para fecha: 2025-06-07
2025-06-08 23:09:59,523 - LogUsuariosPipeline - INFO - SP_LOG_USR EXACTO completado: 4901 registros -> output/********/LOG_USR.parquet
2025-06-08 23:09:59,524 - LogUsuariosPipeline - INFO - LOG-USR: OK
2025-06-08 23:09:59,524 - LogUsuariosPipeline - INFO - EXPORT-CSV: INICIANDO
2025-06-08 23:09:59,524 - LogUsuariosPipeline - INFO - Iniciando exportación CSV con conversiones de perfiles para fecha: 2025-06-07
2025-06-08 23:09:59,524 - LogUsuariosPipeline - INFO - Aplicando SOLO deduplicación como Oracle original (SIN conversión de tipos)
2025-06-08 23:09:59,586 - LogUsuariosPipeline - INFO - Deduplicación completada (manteniendo REQUESTTYPE originales):
2025-06-08 23:09:59,586 - LogUsuariosPipeline - INFO -   - Registros originales: 4,901
2025-06-08 23:09:59,586 - LogUsuariosPipeline - INFO -   - Registros después de deduplicación: 3,779
2025-06-08 23:09:59,586 - LogUsuariosPipeline - INFO -   - Duplicados eliminados: 1,122
2025-06-08 23:09:59,588 - LogUsuariosPipeline - INFO - Tipos de transacción mantenidos:
2025-06-08 23:09:59,588 - LogUsuariosPipeline - INFO -   - CHANGE_AUTH_FACTOR: 1,467 registros
2025-06-08 23:09:59,588 - LogUsuariosPipeline - INFO -   - User Modification: 1,237 registros
2025-06-08 23:09:59,588 - LogUsuariosPipeline - INFO -   - ActivateUser: 394 registros
2025-06-08 23:09:59,588 - LogUsuariosPipeline - INFO -   - AfiliaUser: 394 registros
2025-06-08 23:09:59,588 - LogUsuariosPipeline - INFO -   - ClosedUserAccount: 94 registros
2025-06-08 23:09:59,588 - LogUsuariosPipeline - INFO -   - ClosedAccount: 94 registros
2025-06-08 23:09:59,588 - LogUsuariosPipeline - INFO -   - Delete User: 94 registros
2025-06-08 23:09:59,588 - LogUsuariosPipeline - INFO -   - Suspend User: 3 registros
2025-06-08 23:09:59,588 - LogUsuariosPipeline - INFO -   - RESET_AUTH_VALUE: 1 registros
2025-06-08 23:09:59,588 - LogUsuariosPipeline - INFO -   - Resume User: 1 registros
2025-06-08 23:09:59,588 - LogUsuariosPipeline - INFO - Aplicando conversiones de perfiles al archivo: output/********/LOG_USR_DEDUPLICATED.parquet
2025-06-08 23:09:59,588 - LogUsuariosPipeline - INFO - Cargando tabla de conversión de perfiles...
2025-06-08 23:09:59,592 - LogUsuariosPipeline - INFO - Tabla conv_perfil.csv cargada: 796 registros
2025-06-08 23:09:59,597 - LogUsuariosPipeline - INFO - Tabla de conversión de perfiles cargada exitosamente en DuckDB
2025-06-08 23:09:59,599 - LogUsuariosPipeline - ERROR - Error aplicando conversiones al archivo: Binder Error: Values list "cp_a" does not have a column named "PERFIL_CODIGO"

LINE 8: ...            LEFT JOIN conv_perfil_table cp_a ON l.perfilA = cp_a.PERFIL_CODIGO
                                                                       ^
2025-06-08 23:09:59,599 - LogUsuariosPipeline - INFO - Omitiendo CSV principal redundante - usando solo archivos Parquet y CSVs segmentados
2025-06-08 23:09:59,599 - LogUsuariosPipeline - INFO - Iniciando exportación segmentada por BankDomain
2025-06-08 23:09:59,599 - LogUsuariosPipeline - INFO - Procesando TODOS los bancos del sistema: ['0231FCONFIANZA', 'BNACION', 'CCUSCO', 'CRANDES', 'FCOMPARTAMOS', 'FCONFIANZA', None]
2025-06-08 23:09:59,605 - LogUsuariosPipeline - INFO - Exportado dominio 0231FCONFIANZA: 2 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-0231FCONFIANZA-********.csv
2025-06-08 23:09:59,614 - LogUsuariosPipeline - INFO - Exportado dominio BNACION: 57 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-BNACION-********.csv
2025-06-08 23:09:59,619 - LogUsuariosPipeline - INFO - Exportado dominio CCUSCO: 0 registros (archivo vacío SIN headers) -> output/********/csv_exports/by_bank/LOG-USUARIOS-CCUSCO-********.csv
2025-06-08 23:09:59,626 - LogUsuariosPipeline - INFO - Exportado dominio CRANDES: 4 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-CRANDES-********.csv
2025-06-08 23:09:59,651 - LogUsuariosPipeline - INFO - Exportado dominio FCOMPARTAMOS: 4838 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-FCOMPARTAMOS-********.csv
2025-06-08 23:09:59,655 - LogUsuariosPipeline - INFO - Exportado dominio FCONFIANZA: 0 registros (archivo vacío SIN headers) -> output/********/csv_exports/by_bank/LOG-USUARIOS-FCONFIANZA-********.csv
2025-06-08 23:09:59,659 - LogUsuariosPipeline - INFO - Exportado dominio NULL: 0 registros (archivo vacío SIN headers) -> output/********/csv_exports/by_bank/LOG-USUARIOS-NULL-********.csv
2025-06-08 23:09:59,659 - LogUsuariosPipeline - INFO - Exportación segmentada completada: 7 archivos generados
2025-06-08 23:09:59,659 - LogUsuariosPipeline - INFO - Estructura EXACTA como flujo original: solo by_bank (sin by_profile)
2025-06-08 23:09:59,659 - LogUsuariosPipeline - INFO - 🔄 APLICANDO PROCESAMIENTO EXACTO como procesar.py...
2025-06-08 23:09:59,660 - LogUsuariosPipeline - INFO - 🔄 INICIANDO PROCESAMIENTO EXACTO como procesar.py
2025-06-08 23:09:59,660 - LogUsuariosPipeline - INFO - 📁 Input: output/********/LOG_USR.parquet
2025-06-08 23:09:59,660 - LogUsuariosPipeline - INFO - 📅 Fecha: 2025-06-07
2025-06-08 23:09:59,660 - LogUsuariosPipeline - INFO - 📁 Output: output/********/csv_exports/csv_final_procesado
2025-06-08 23:09:59,660 - LogUsuariosPipeline - INFO - 📊 Cargando datos desde LOG_USR.parquet...
2025-06-08 23:09:59,687 - LogUsuariosPipeline - INFO - 📊 Registros cargados: 4,901
2025-06-08 23:09:59,687 - LogUsuariosPipeline - INFO - 🔄 Procesando grupos por USERHISTID...
2025-06-08 23:10:00,423 - LogUsuariosPipeline - INFO - 📊 Convirtiendo a DataFrame...
2025-06-08 23:10:00,450 - LogUsuariosPipeline - INFO - 📊 Registros después del procesamiento inicial: 10,900
2025-06-08 23:10:00,450 - LogUsuariosPipeline - INFO - 🔍 Aplicando filtro de valores válidos...
2025-06-08 23:10:00,453 - LogUsuariosPipeline - INFO - 📊 Registros después del filtro: 4,526
2025-06-08 23:10:00,453 - LogUsuariosPipeline - INFO - 🥷 Aplicando filtros adicionales de calidad de datos...
2025-06-08 23:10:00,457 - LogUsuariosPipeline - INFO - 📊 Registros después de filtros de calidad: 4,526
2025-06-08 23:10:00,457 - LogUsuariosPipeline - INFO - 🔄 Agrupando y procesando JSON...
2025-06-08 23:10:01,150 - LogUsuariosPipeline - INFO - 📊 Registros después de eliminar duplicados: 3,123
2025-06-08 23:10:01,153 - LogUsuariosPipeline - INFO - 🔄 Aplicando asignaciones a columnas...
2025-06-08 23:10:01,386 - LogUsuariosPipeline - INFO - 📊 Cargando tabla de conversión de perfiles...
2025-06-08 23:10:01,388 - LogUsuariosPipeline - INFO - 📊 Tabla de conversión cargada: 796 registros
2025-06-08 23:10:01,440 - LogUsuariosPipeline - INFO - 🔄 Aplicando función crítica de expansión de registros...
2025-06-08 23:10:01,440 - LogUsuariosPipeline - INFO - 📊 Registros antes de expansión: 3,123
2025-06-08 23:10:01,535 - LogUsuariosPipeline - INFO - 📊 Registros después de expansión: 3,127
2025-06-08 23:10:01,535 - LogUsuariosPipeline - INFO - ✅ Función crítica de expansión aplicada exitosamente
2025-06-08 23:10:01,535 - LogUsuariosPipeline - INFO - 🔄 Aplicando mapeo de columnas...
2025-06-08 23:10:01,536 - LogUsuariosPipeline - INFO - 🔄 Procesando tipos de transacciones válidas...
2025-06-08 23:10:01,570 - LogUsuariosPipeline - INFO - 📊 Registros después de concatenar: 3,127
2025-06-08 23:10:05,302 - LogUsuariosPipeline - INFO - 📊 Registros finales para exportar: 3,127
2025-06-08 23:10:05,302 - LogUsuariosPipeline - INFO - 📁 Generando archivos CSV por BankDomain...
2025-06-08 23:10:05,317 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-FCOMPARTAMOS-********231005.csv (3,094 registros)
2025-06-08 23:10:05,319 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-BNACION-********231005.csv (30 registros)
2025-06-08 23:10:05,319 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-0231FCONFIANZA-********231005.csv (1 registros)
2025-06-08 23:10:05,320 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-CRANDES-********231005.csv (2 registros)
2025-06-08 23:10:05,320 - LogUsuariosPipeline - INFO - ✅ Procesamiento completado: 4 archivos generados
2025-06-08 23:10:05,320 - LogUsuariosPipeline - INFO - 📊 Total registros procesados: 3,127
2025-06-08 23:10:05,333 - LogUsuariosPipeline - INFO - ✅ Procesamiento exacto completado: 4 archivos
2025-06-08 23:10:05,334 - LogUsuariosPipeline - INFO -   📁 LOGUSR-FCOMPARTAMOS-********231005.csv
2025-06-08 23:10:05,334 - LogUsuariosPipeline - INFO -   📁 LOGUSR-BNACION-********231005.csv
2025-06-08 23:10:05,334 - LogUsuariosPipeline - INFO -   📁 LOGUSR-0231FCONFIANZA-********231005.csv
2025-06-08 23:10:05,334 - LogUsuariosPipeline - INFO -   📁 LOGUSR-CRANDES-********231005.csv
2025-06-08 23:10:05,334 - LogUsuariosPipeline - INFO - Archivo temporal eliminado: TEMP_LOGS_USUARIOS/********/WALLET_OLD.parquet
2025-06-08 23:10:05,334 - LogUsuariosPipeline - INFO - EXPORT-CSV: OK
2025-06-08 23:10:05,334 - LogUsuariosPipeline - INFO - === ARCHIVOS CSV GENERADOS ===
2025-06-08 23:10:05,334 - LogUsuariosPipeline - INFO - Total de archivos CSV segmentados: 11
2025-06-08 23:10:05,334 - LogUsuariosPipeline - INFO - Archivos por BankDomain (7) - EXACTO como flujo original:
2025-06-08 23:10:05,334 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-0231FCONFIANZA-********.csv
2025-06-08 23:10:05,334 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-BNACION-********.csv
2025-06-08 23:10:05,334 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-CCUSCO-********.csv
2025-06-08 23:10:05,334 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-CRANDES-********.csv
2025-06-08 23:10:05,334 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-FCOMPARTAMOS-********.csv
2025-06-08 23:10:05,334 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-FCONFIANZA-********.csv
2025-06-08 23:10:05,334 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-NULL-********.csv
2025-06-08 23:10:05,334 - LogUsuariosPipeline - INFO - Estructura OPTIMIZADA: solo archivos segmentados (datos completos en Parquet)
2025-06-08 23:10:05,334 - LogUsuariosPipeline - INFO - === PIPELINE COMPLETADO EXITOSAMENTE ===
2025-06-08 23:10:05,334 - LogUsuariosPipeline - INFO - 
=== ARCHIVOS GENERADOS ===
2025-06-08 23:10:05,334 - LogUsuariosPipeline - INFO - Archivos temporales en TEMP_LOGS_USUARIOS/********:
2025-06-08 23:10:05,334 - LogUsuariosPipeline - INFO -   - USER_ACCOUNT_HISTORY.parquet (0.00 MB)
2025-06-08 23:10:05,334 - LogUsuariosPipeline - INFO -   - USER_DATA_TRX.parquet (127.19 MB)
2025-06-08 23:10:05,335 - LogUsuariosPipeline - INFO -   - USER_MODIFICATION_DAY.parquet (0.64 MB)
2025-06-08 23:10:05,335 - LogUsuariosPipeline - INFO -   - USER_AUTH_CHANGE_HISTORY.parquet (0.04 MB)
2025-06-08 23:10:05,335 - LogUsuariosPipeline - INFO - Archivos de salida en output/********:
2025-06-08 23:10:05,335 - LogUsuariosPipeline - INFO -   - LOG_USR.parquet (1.01 MB)
2025-06-08 23:10:05,335 - LogUsuariosPipeline - INFO -   - LOG_USR_DEDUPLICATED.parquet (0.84 MB)
2025-06-08 23:10:05,335 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-0231FCONFIANZA-********.csv (0.00 MB)
2025-06-08 23:10:05,335 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-BNACION-********.csv (0.04 MB)
2025-06-08 23:10:05,335 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-CCUSCO-********.csv (0.00 MB)
2025-06-08 23:10:05,335 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-CRANDES-********.csv (0.01 MB)
2025-06-08 23:10:05,335 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-FCOMPARTAMOS-********.csv (6.09 MB)
2025-06-08 23:10:05,335 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-FCONFIANZA-********.csv (0.00 MB)
2025-06-08 23:10:05,335 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-NULL-********.csv (0.00 MB)
2025-06-08 23:10:05,335 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-FCOMPARTAMOS-********231005.csv (0.45 MB)
2025-06-08 23:10:05,335 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-BNACION-********231005.csv (0.00 MB)
2025-06-08 23:10:05,335 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-0231FCONFIANZA-********231005.csv (0.00 MB)
2025-06-08 23:10:05,335 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-CRANDES-********231005.csv (0.00 MB)
2025-06-08 23:10:54,091 - LogUsuariosPipeline - INFO - Configurando credenciales S3...
2025-06-08 23:10:54,104 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-08 23:10:54,155 - LogUsuariosPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-08 23:10:54,156 - LogUsuariosPipeline - INFO - === INICIANDO PIPELINE LOG_USUARIOS MODERNIZADO ===
2025-06-08 23:10:54,156 - LogUsuariosPipeline - INFO - Fecha de procesamiento: 2025-06-06 (carpeta: ********)
2025-06-08 23:10:54,156 - LogUsuariosPipeline - INFO - USER-MODIFY: INICIANDO
2025-06-08 23:10:54,156 - LogUsuariosPipeline - INFO - Extrayendo USER_ACCOUNT_HISTORY para fecha: 2025-06-06
2025-06-08 23:10:54,287 - LogUsuariosPipeline - INFO - USER_ACCOUNT_HISTORY extraído: 8 registros -> TEMP_LOGS_USUARIOS/********/USER_ACCOUNT_HISTORY.parquet
2025-06-08 23:10:54,287 - LogUsuariosPipeline - INFO - USER-MODIFY: OK
2025-06-08 23:10:54,287 - LogUsuariosPipeline - INFO - PRE-LOG-USR: INICIANDO
2025-06-08 23:10:54,287 - LogUsuariosPipeline - INFO - Ejecutando SP_PRE_LOG_USR CORREGIDO (incluye USER_IDs múltiples) para fecha: 2025-06-06
2025-06-08 23:11:01,665 - LogUsuariosPipeline - INFO - SP_PRE_LOG_USR completado: 1583130 registros -> TEMP_LOGS_USUARIOS/********/USER_DATA_TRX.parquet
2025-06-08 23:11:01,665 - LogUsuariosPipeline - INFO - Ejecutando SP_USER_MODIFICATION para fecha: 2025-06-06
2025-06-08 23:11:03,876 - LogUsuariosPipeline - INFO - SP_USER_MODIFICATION completado: 6908 registros -> TEMP_LOGS_USUARIOS/********/USER_MODIFICATION_DAY.parquet
2025-06-08 23:11:03,876 - LogUsuariosPipeline - INFO - Ejecutando SP_USER_AUTH_DAY para fecha: 2025-06-06
2025-06-08 23:11:04,282 - LogUsuariosPipeline - INFO - SP_USER_AUTH_DAY completado: 5189 registros -> TEMP_LOGS_USUARIOS/********/USER_AUTH_CHANGE_HISTORY.parquet
2025-06-08 23:11:04,282 - LogUsuariosPipeline - INFO - PRE-LOG-USR: OK
2025-06-08 23:11:04,283 - LogUsuariosPipeline - INFO - LOG-USR: INICIANDO
2025-06-08 23:11:04,283 - LogUsuariosPipeline - INFO - Ejecutando SP_LOG_USR EXACTO (igual a Oracle) para fecha: 2025-06-06
2025-06-08 23:11:05,857 - LogUsuariosPipeline - INFO - SP_LOG_USR EXACTO completado: 19668 registros -> output/********/LOG_USR.parquet
2025-06-08 23:11:05,857 - LogUsuariosPipeline - INFO - LOG-USR: OK
2025-06-08 23:11:05,857 - LogUsuariosPipeline - INFO - EXPORT-CSV: INICIANDO
2025-06-08 23:11:05,857 - LogUsuariosPipeline - INFO - Iniciando exportación CSV con conversiones de perfiles para fecha: 2025-06-06
2025-06-08 23:11:05,858 - LogUsuariosPipeline - INFO - Aplicando SOLO deduplicación como Oracle original (SIN conversión de tipos)
2025-06-08 23:11:06,090 - LogUsuariosPipeline - INFO - Deduplicación completada (manteniendo REQUESTTYPE originales):
2025-06-08 23:11:06,091 - LogUsuariosPipeline - INFO -   - Registros originales: 19,668
2025-06-08 23:11:06,091 - LogUsuariosPipeline - INFO -   - Registros después de deduplicación: 16,069
2025-06-08 23:11:06,091 - LogUsuariosPipeline - INFO -   - Duplicados eliminados: 3,599
2025-06-08 23:11:06,092 - LogUsuariosPipeline - INFO - Tipos de transacción mantenidos:
2025-06-08 23:11:06,093 - LogUsuariosPipeline - INFO -   - User Modification: 6,579 registros
2025-06-08 23:11:06,093 - LogUsuariosPipeline - INFO -   - CHANGE_AUTH_FACTOR: 5,071 registros
2025-06-08 23:11:06,093 - LogUsuariosPipeline - INFO -   - ActivateUser: 1,756 registros
2025-06-08 23:11:06,093 - LogUsuariosPipeline - INFO -   - AfiliaUser: 1,756 registros
2025-06-08 23:11:06,093 - LogUsuariosPipeline - INFO -   - ClosedAccount: 289 registros
2025-06-08 23:11:06,093 - LogUsuariosPipeline - INFO -   - ClosedUserAccount: 289 registros
2025-06-08 23:11:06,093 - LogUsuariosPipeline - INFO -   - Delete User: 289 registros
2025-06-08 23:11:06,093 - LogUsuariosPipeline - INFO -   - Lock Wallet: 24 registros
2025-06-08 23:11:06,093 - LogUsuariosPipeline - INFO -   - Suspend User: 7 registros
2025-06-08 23:11:06,093 - LogUsuariosPipeline - INFO -   - Resume User: 6 registros
2025-06-08 23:11:06,093 - LogUsuariosPipeline - INFO - Aplicando conversiones de perfiles al archivo: output/********/LOG_USR_DEDUPLICATED.parquet
2025-06-08 23:11:06,093 - LogUsuariosPipeline - INFO - Cargando tabla de conversión de perfiles...
2025-06-08 23:11:06,096 - LogUsuariosPipeline - INFO - Tabla conv_perfil.csv cargada: 796 registros
2025-06-08 23:11:06,100 - LogUsuariosPipeline - INFO - Tabla de conversión de perfiles cargada exitosamente en DuckDB
2025-06-08 23:11:06,102 - LogUsuariosPipeline - ERROR - Error aplicando conversiones al archivo: Binder Error: Values list "cp_a" does not have a column named "PERFIL_CODIGO"

LINE 8: ...            LEFT JOIN conv_perfil_table cp_a ON l.perfilA = cp_a.PERFIL_CODIGO
                                                                       ^
2025-06-08 23:11:06,102 - LogUsuariosPipeline - INFO - Omitiendo CSV principal redundante - usando solo archivos Parquet y CSVs segmentados
2025-06-08 23:11:06,102 - LogUsuariosPipeline - INFO - Iniciando exportación segmentada por BankDomain
2025-06-08 23:11:06,102 - LogUsuariosPipeline - INFO - Procesando TODOS los bancos del sistema: ['0231FCONFIANZA', 'BNACION', 'CCUSCO', 'CRANDES', 'FCOMPARTAMOS', 'FCONFIANZA', None]
2025-06-08 23:11:06,106 - LogUsuariosPipeline - INFO - Exportado dominio 0231FCONFIANZA: 0 registros (archivo vacío SIN headers) -> output/********/csv_exports/by_bank/LOG-USUARIOS-0231FCONFIANZA-********.csv
2025-06-08 23:11:06,123 - LogUsuariosPipeline - INFO - Exportado dominio BNACION: 168 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-BNACION-********.csv
2025-06-08 23:11:06,131 - LogUsuariosPipeline - INFO - Exportado dominio CCUSCO: 0 registros (archivo vacío SIN headers) -> output/********/csv_exports/by_bank/LOG-USUARIOS-CCUSCO-********.csv
2025-06-08 23:11:06,137 - LogUsuariosPipeline - INFO - Exportado dominio CRANDES: 4 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-CRANDES-********.csv
2025-06-08 23:11:06,253 - LogUsuariosPipeline - INFO - Exportado dominio FCOMPARTAMOS: 19496 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-FCOMPARTAMOS-********.csv
2025-06-08 23:11:06,257 - LogUsuariosPipeline - INFO - Exportado dominio FCONFIANZA: 0 registros (archivo vacío SIN headers) -> output/********/csv_exports/by_bank/LOG-USUARIOS-FCONFIANZA-********.csv
2025-06-08 23:11:06,261 - LogUsuariosPipeline - INFO - Exportado dominio NULL: 0 registros (archivo vacío SIN headers) -> output/********/csv_exports/by_bank/LOG-USUARIOS-NULL-********.csv
2025-06-08 23:11:06,261 - LogUsuariosPipeline - INFO - Exportación segmentada completada: 7 archivos generados
2025-06-08 23:11:06,261 - LogUsuariosPipeline - INFO - Estructura EXACTA como flujo original: solo by_bank (sin by_profile)
2025-06-08 23:11:06,261 - LogUsuariosPipeline - INFO - 🔄 APLICANDO PROCESAMIENTO EXACTO como procesar.py...
2025-06-08 23:11:06,261 - LogUsuariosPipeline - INFO - 🔄 INICIANDO PROCESAMIENTO EXACTO como procesar.py
2025-06-08 23:11:06,262 - LogUsuariosPipeline - INFO - 📁 Input: output/********/LOG_USR.parquet
2025-06-08 23:11:06,262 - LogUsuariosPipeline - INFO - 📅 Fecha: 2025-06-06
2025-06-08 23:11:06,262 - LogUsuariosPipeline - INFO - 📁 Output: output/********/csv_exports/csv_final_procesado
2025-06-08 23:11:06,262 - LogUsuariosPipeline - INFO - 📊 Cargando datos desde LOG_USR.parquet...
2025-06-08 23:11:06,327 - LogUsuariosPipeline - INFO - 📊 Registros cargados: 19,668
2025-06-08 23:11:06,328 - LogUsuariosPipeline - INFO - 🔄 Procesando grupos por USERHISTID...
2025-06-08 23:11:09,543 - LogUsuariosPipeline - INFO - 📊 Convirtiendo a DataFrame...
2025-06-08 23:11:09,669 - LogUsuariosPipeline - INFO - 📊 Registros después del procesamiento inicial: 48,477
2025-06-08 23:11:09,670 - LogUsuariosPipeline - INFO - 🔍 Aplicando filtro de valores válidos...
2025-06-08 23:11:09,677 - LogUsuariosPipeline - INFO - 📊 Registros después del filtro: 17,990
2025-06-08 23:11:09,677 - LogUsuariosPipeline - INFO - 🥷 Aplicando filtros adicionales de calidad de datos...
2025-06-08 23:11:09,693 - LogUsuariosPipeline - INFO - 📊 Registros después de filtros de calidad: 17,990
2025-06-08 23:11:09,693 - LogUsuariosPipeline - INFO - 🔄 Agrupando y procesando JSON...
2025-06-08 23:11:12,737 - LogUsuariosPipeline - INFO - 📊 Registros después de eliminar duplicados: 13,049
2025-06-08 23:11:12,747 - LogUsuariosPipeline - INFO - 🔄 Aplicando asignaciones a columnas...
2025-06-08 23:11:13,799 - LogUsuariosPipeline - INFO - 📊 Cargando tabla de conversión de perfiles...
2025-06-08 23:11:13,801 - LogUsuariosPipeline - INFO - 📊 Tabla de conversión cargada: 796 registros
2025-06-08 23:11:14,136 - LogUsuariosPipeline - INFO - 🔄 Aplicando función crítica de expansión de registros...
2025-06-08 23:11:14,136 - LogUsuariosPipeline - INFO - 📊 Registros antes de expansión: 13,049
2025-06-08 23:11:14,705 - LogUsuariosPipeline - INFO - 📊 Registros después de expansión: 13,077
2025-06-08 23:11:14,705 - LogUsuariosPipeline - INFO - ✅ Función crítica de expansión aplicada exitosamente
2025-06-08 23:11:14,705 - LogUsuariosPipeline - INFO - 🔄 Aplicando mapeo de columnas...
2025-06-08 23:11:14,707 - LogUsuariosPipeline - INFO - 🔄 Procesando tipos de transacciones válidas...
2025-06-08 23:11:14,756 - LogUsuariosPipeline - INFO - 📊 Registros después de concatenar: 13,077
2025-06-08 23:11:30,332 - LogUsuariosPipeline - INFO - 📊 Registros finales para exportar: 13,077
2025-06-08 23:11:30,333 - LogUsuariosPipeline - INFO - 📁 Generando archivos CSV por BankDomain...
2025-06-08 23:11:30,395 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-FCOMPARTAMOS-********231130.csv (12,956 registros)
2025-06-08 23:11:30,398 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-BNACION-********231130.csv (111 registros)
2025-06-08 23:11:30,399 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-CRANDES-********231130.csv (2 registros)
2025-06-08 23:11:30,400 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-CCUSCO-********231130.csv (8 registros)
2025-06-08 23:11:30,400 - LogUsuariosPipeline - INFO - ✅ Procesamiento completado: 4 archivos generados
2025-06-08 23:11:30,400 - LogUsuariosPipeline - INFO - 📊 Total registros procesados: 13,077
2025-06-08 23:11:30,450 - LogUsuariosPipeline - INFO - ✅ Procesamiento exacto completado: 4 archivos
2025-06-08 23:11:30,451 - LogUsuariosPipeline - INFO -   📁 LOGUSR-FCOMPARTAMOS-********231130.csv
2025-06-08 23:11:30,451 - LogUsuariosPipeline - INFO -   📁 LOGUSR-BNACION-********231130.csv
2025-06-08 23:11:30,451 - LogUsuariosPipeline - INFO -   📁 LOGUSR-CRANDES-********231130.csv
2025-06-08 23:11:30,451 - LogUsuariosPipeline - INFO -   📁 LOGUSR-CCUSCO-********231130.csv
2025-06-08 23:11:30,451 - LogUsuariosPipeline - INFO - Archivo temporal eliminado: TEMP_LOGS_USUARIOS/********/WALLET_OLD.parquet
2025-06-08 23:11:30,451 - LogUsuariosPipeline - INFO - EXPORT-CSV: OK
2025-06-08 23:11:30,451 - LogUsuariosPipeline - INFO - === ARCHIVOS CSV GENERADOS ===
2025-06-08 23:11:30,451 - LogUsuariosPipeline - INFO - Total de archivos CSV segmentados: 11
2025-06-08 23:11:30,451 - LogUsuariosPipeline - INFO - Archivos por BankDomain (7) - EXACTO como flujo original:
2025-06-08 23:11:30,451 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-0231FCONFIANZA-********.csv
2025-06-08 23:11:30,451 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-BNACION-********.csv
2025-06-08 23:11:30,451 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-CCUSCO-********.csv
2025-06-08 23:11:30,451 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-CRANDES-********.csv
2025-06-08 23:11:30,451 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-FCOMPARTAMOS-********.csv
2025-06-08 23:11:30,451 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-FCONFIANZA-********.csv
2025-06-08 23:11:30,451 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-NULL-********.csv
2025-06-08 23:11:30,451 - LogUsuariosPipeline - INFO - Estructura OPTIMIZADA: solo archivos segmentados (datos completos en Parquet)
2025-06-08 23:11:30,451 - LogUsuariosPipeline - INFO - === PIPELINE COMPLETADO EXITOSAMENTE ===
2025-06-08 23:11:30,451 - LogUsuariosPipeline - INFO - 
=== ARCHIVOS GENERADOS ===
2025-06-08 23:11:30,451 - LogUsuariosPipeline - INFO - Archivos temporales en TEMP_LOGS_USUARIOS/********:
2025-06-08 23:11:30,452 - LogUsuariosPipeline - INFO -   - USER_ACCOUNT_HISTORY.parquet (0.00 MB)
2025-06-08 23:11:30,452 - LogUsuariosPipeline - INFO -   - USER_DATA_TRX.parquet (127.15 MB)
2025-06-08 23:11:30,452 - LogUsuariosPipeline - INFO -   - USER_MODIFICATION_DAY.parquet (3.36 MB)
2025-06-08 23:11:30,452 - LogUsuariosPipeline - INFO -   - USER_AUTH_CHANGE_HISTORY.parquet (0.11 MB)
2025-06-08 23:11:30,452 - LogUsuariosPipeline - INFO - Archivos de salida en output/********:
2025-06-08 23:11:30,452 - LogUsuariosPipeline - INFO -   - LOG_USR.parquet (4.34 MB)
2025-06-08 23:11:30,452 - LogUsuariosPipeline - INFO -   - LOG_USR_DEDUPLICATED.parquet (4.23 MB)
2025-06-08 23:11:30,452 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-0231FCONFIANZA-********.csv (0.00 MB)
2025-06-08 23:11:30,452 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-BNACION-********.csv (0.11 MB)
2025-06-08 23:11:30,452 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-CCUSCO-********.csv (0.00 MB)
2025-06-08 23:11:30,452 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-CRANDES-********.csv (0.00 MB)
2025-06-08 23:11:30,452 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-FCOMPARTAMOS-********.csv (30.92 MB)
2025-06-08 23:11:30,452 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-FCONFIANZA-********.csv (0.00 MB)
2025-06-08 23:11:30,452 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-NULL-********.csv (0.00 MB)
2025-06-08 23:11:30,452 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-FCOMPARTAMOS-********161535.csv (2.00 MB)
2025-06-08 23:11:30,452 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-BNACION-********161535.csv (0.01 MB)
2025-06-08 23:11:30,452 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-CRANDES-********161535.csv (0.00 MB)
2025-06-08 23:11:30,452 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-CCUSCO-********161535.csv (0.00 MB)
2025-06-08 23:11:30,452 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-FCOMPARTAMOS-********231130.csv (2.00 MB)
2025-06-08 23:11:30,452 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-BNACION-********231130.csv (0.01 MB)
2025-06-08 23:11:30,452 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-CRANDES-********231130.csv (0.00 MB)
2025-06-08 23:11:30,453 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-CCUSCO-********231130.csv (0.00 MB)
2025-06-08 23:15:23,322 - LogUsuariosPipeline - INFO - Configurando credenciales S3...
2025-06-08 23:15:23,334 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-08 23:15:23,386 - LogUsuariosPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-08 23:15:23,387 - LogUsuariosPipeline - INFO - === INICIANDO PIPELINE LOG_USUARIOS MODERNIZADO ===
2025-06-08 23:15:23,387 - LogUsuariosPipeline - INFO - Fecha de procesamiento: 2025-06-06 (carpeta: ********)
2025-06-08 23:15:23,387 - LogUsuariosPipeline - INFO - USER-MODIFY: INICIANDO
2025-06-08 23:15:23,387 - LogUsuariosPipeline - INFO - Extrayendo USER_ACCOUNT_HISTORY para fecha: 2025-06-06
2025-06-08 23:15:23,516 - LogUsuariosPipeline - INFO - USER_ACCOUNT_HISTORY extraído: 8 registros -> TEMP_LOGS_USUARIOS/********/USER_ACCOUNT_HISTORY.parquet
2025-06-08 23:15:23,517 - LogUsuariosPipeline - INFO - USER-MODIFY: OK
2025-06-08 23:15:23,517 - LogUsuariosPipeline - INFO - PRE-LOG-USR: INICIANDO
2025-06-08 23:15:23,517 - LogUsuariosPipeline - INFO - Ejecutando SP_PRE_LOG_USR CORREGIDO (incluye USER_IDs múltiples) para fecha: 2025-06-06
2025-06-08 23:15:31,276 - LogUsuariosPipeline - INFO - SP_PRE_LOG_USR completado: 1583130 registros -> TEMP_LOGS_USUARIOS/********/USER_DATA_TRX.parquet
2025-06-08 23:15:31,276 - LogUsuariosPipeline - INFO - Ejecutando SP_USER_MODIFICATION para fecha: 2025-06-06
2025-06-08 23:15:33,506 - LogUsuariosPipeline - INFO - SP_USER_MODIFICATION completado: 6908 registros -> TEMP_LOGS_USUARIOS/********/USER_MODIFICATION_DAY.parquet
2025-06-08 23:15:33,506 - LogUsuariosPipeline - INFO - Ejecutando SP_USER_AUTH_DAY para fecha: 2025-06-06
2025-06-08 23:15:33,899 - LogUsuariosPipeline - INFO - SP_USER_AUTH_DAY completado: 5189 registros -> TEMP_LOGS_USUARIOS/********/USER_AUTH_CHANGE_HISTORY.parquet
2025-06-08 23:15:33,899 - LogUsuariosPipeline - INFO - PRE-LOG-USR: OK
2025-06-08 23:15:33,899 - LogUsuariosPipeline - INFO - LOG-USR: INICIANDO
2025-06-08 23:15:33,899 - LogUsuariosPipeline - INFO - Ejecutando SP_LOG_USR EXACTO (igual a Oracle) para fecha: 2025-06-06
2025-06-08 23:15:35,517 - LogUsuariosPipeline - INFO - SP_LOG_USR EXACTO completado: 19668 registros -> output/********/LOG_USR.parquet
2025-06-08 23:15:35,518 - LogUsuariosPipeline - INFO - LOG-USR: OK
2025-06-08 23:15:35,518 - LogUsuariosPipeline - INFO - EXPORT-CSV: INICIANDO
2025-06-08 23:15:35,518 - LogUsuariosPipeline - INFO - Iniciando exportación CSV con conversiones de perfiles para fecha: 2025-06-06
2025-06-08 23:15:35,518 - LogUsuariosPipeline - INFO - Aplicando SOLO deduplicación como Oracle original (SIN conversión de tipos)
2025-06-08 23:15:35,758 - LogUsuariosPipeline - INFO - Deduplicación completada (manteniendo REQUESTTYPE originales):
2025-06-08 23:15:35,758 - LogUsuariosPipeline - INFO -   - Registros originales: 19,668
2025-06-08 23:15:35,758 - LogUsuariosPipeline - INFO -   - Registros después de deduplicación: 16,069
2025-06-08 23:15:35,758 - LogUsuariosPipeline - INFO -   - Duplicados eliminados: 3,599
2025-06-08 23:15:35,760 - LogUsuariosPipeline - INFO - Tipos de transacción mantenidos:
2025-06-08 23:15:35,760 - LogUsuariosPipeline - INFO -   - User Modification: 6,579 registros
2025-06-08 23:15:35,760 - LogUsuariosPipeline - INFO -   - CHANGE_AUTH_FACTOR: 5,071 registros
2025-06-08 23:15:35,760 - LogUsuariosPipeline - INFO -   - AfiliaUser: 1,756 registros
2025-06-08 23:15:35,760 - LogUsuariosPipeline - INFO -   - ActivateUser: 1,756 registros
2025-06-08 23:15:35,760 - LogUsuariosPipeline - INFO -   - ClosedUserAccount: 289 registros
2025-06-08 23:15:35,760 - LogUsuariosPipeline - INFO -   - ClosedAccount: 289 registros
2025-06-08 23:15:35,760 - LogUsuariosPipeline - INFO -   - Delete User: 289 registros
2025-06-08 23:15:35,760 - LogUsuariosPipeline - INFO -   - Lock Wallet: 24 registros
2025-06-08 23:15:35,760 - LogUsuariosPipeline - INFO -   - Suspend User: 7 registros
2025-06-08 23:15:35,760 - LogUsuariosPipeline - INFO -   - Resume User: 6 registros
2025-06-08 23:15:35,760 - LogUsuariosPipeline - INFO - Aplicando conversiones de perfiles al archivo: output/********/LOG_USR_DEDUPLICATED.parquet
2025-06-08 23:15:35,760 - LogUsuariosPipeline - INFO - Cargando tabla de conversión de perfiles...
2025-06-08 23:15:35,763 - LogUsuariosPipeline - INFO - Tabla conv_perfil.csv cargada: 796 registros
2025-06-08 23:15:35,767 - LogUsuariosPipeline - INFO - Tabla de conversión de perfiles cargada exitosamente en DuckDB
2025-06-08 23:15:35,769 - LogUsuariosPipeline - ERROR - Error aplicando conversiones al archivo: Binder Error: Values list "cp_a" does not have a column named "PERFIL_CODIGO"

LINE 8: ...            LEFT JOIN conv_perfil_table cp_a ON l.perfilA = cp_a.PERFIL_CODIGO
                                                                       ^
2025-06-08 23:15:35,769 - LogUsuariosPipeline - INFO - Omitiendo CSV principal redundante - usando solo archivos Parquet y CSVs segmentados
2025-06-08 23:15:35,769 - LogUsuariosPipeline - INFO - Iniciando exportación segmentada por BankDomain
2025-06-08 23:15:35,769 - LogUsuariosPipeline - INFO - Procesando TODOS los bancos del sistema: ['0231FCONFIANZA', 'BNACION', 'CCUSCO', 'CRANDES', 'FCOMPARTAMOS', 'FCONFIANZA', None]
2025-06-08 23:15:35,774 - LogUsuariosPipeline - INFO - Exportado dominio 0231FCONFIANZA: 0 registros (archivo vacío SIN headers) -> output/********/csv_exports/by_bank/LOG-USUARIOS-0231FCONFIANZA-********.csv
2025-06-08 23:15:35,792 - LogUsuariosPipeline - INFO - Exportado dominio BNACION: 168 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-BNACION-********.csv
2025-06-08 23:15:35,798 - LogUsuariosPipeline - INFO - Exportado dominio CCUSCO: 0 registros (archivo vacío SIN headers) -> output/********/csv_exports/by_bank/LOG-USUARIOS-CCUSCO-********.csv
2025-06-08 23:15:35,805 - LogUsuariosPipeline - INFO - Exportado dominio CRANDES: 4 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-CRANDES-********.csv
2025-06-08 23:15:35,926 - LogUsuariosPipeline - INFO - Exportado dominio FCOMPARTAMOS: 19496 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-FCOMPARTAMOS-********.csv
2025-06-08 23:15:35,930 - LogUsuariosPipeline - INFO - Exportado dominio FCONFIANZA: 0 registros (archivo vacío SIN headers) -> output/********/csv_exports/by_bank/LOG-USUARIOS-FCONFIANZA-********.csv
2025-06-08 23:15:35,934 - LogUsuariosPipeline - INFO - Exportado dominio NULL: 0 registros (archivo vacío SIN headers) -> output/********/csv_exports/by_bank/LOG-USUARIOS-NULL-********.csv
2025-06-08 23:15:35,934 - LogUsuariosPipeline - INFO - Exportación segmentada completada: 7 archivos generados
2025-06-08 23:15:35,934 - LogUsuariosPipeline - INFO - Estructura EXACTA como flujo original: solo by_bank (sin by_profile)
2025-06-08 23:15:35,934 - LogUsuariosPipeline - INFO - 🔄 APLICANDO PROCESAMIENTO EXACTO como procesar.py...
2025-06-08 23:15:35,935 - LogUsuariosPipeline - INFO - 🔄 INICIANDO PROCESAMIENTO EXACTO como procesar.py
2025-06-08 23:15:35,935 - LogUsuariosPipeline - INFO - 📁 Input: output/********/LOG_USR.parquet
2025-06-08 23:15:35,935 - LogUsuariosPipeline - INFO - 📅 Fecha: 2025-06-06
2025-06-08 23:15:35,935 - LogUsuariosPipeline - INFO - 📁 Output: output/********/csv_exports/csv_final_procesado
2025-06-08 23:15:35,935 - LogUsuariosPipeline - INFO - 📊 Cargando datos desde LOG_USR.parquet...
2025-06-08 23:15:35,999 - LogUsuariosPipeline - INFO - 📊 Registros cargados: 19,668
2025-06-08 23:15:35,999 - LogUsuariosPipeline - INFO - 🔄 Procesando grupos por USERHISTID...
2025-06-08 23:15:39,310 - LogUsuariosPipeline - INFO - 📊 Convirtiendo a DataFrame...
2025-06-08 23:15:39,435 - LogUsuariosPipeline - INFO - 📊 Registros después del procesamiento inicial: 48,477
2025-06-08 23:15:39,436 - LogUsuariosPipeline - INFO - 🔍 Aplicando filtro de valores válidos...
2025-06-08 23:15:39,444 - LogUsuariosPipeline - INFO - 📊 Registros después del filtro: 17,990
2025-06-08 23:15:39,444 - LogUsuariosPipeline - INFO - 🥷 Aplicando filtros adicionales de calidad de datos...
2025-06-08 23:15:39,460 - LogUsuariosPipeline - INFO - 📊 Registros después de filtros de calidad: 17,990
2025-06-08 23:15:39,460 - LogUsuariosPipeline - INFO - 🔄 Agrupando y procesando JSON...
2025-06-08 23:15:42,541 - LogUsuariosPipeline - INFO - 📊 Registros después de eliminar duplicados: 13,049
2025-06-08 23:15:42,551 - LogUsuariosPipeline - INFO - 🔄 Aplicando asignaciones a columnas...
2025-06-08 23:15:43,612 - LogUsuariosPipeline - INFO - 📊 Cargando tabla de conversión de perfiles...
2025-06-08 23:15:43,614 - LogUsuariosPipeline - INFO - 📊 Tabla de conversión cargada: 796 registros
2025-06-08 23:15:43,950 - LogUsuariosPipeline - INFO - 🔄 Aplicando función crítica de expansión de registros...
2025-06-08 23:15:43,950 - LogUsuariosPipeline - INFO - 📊 Registros antes de expansión: 13,049
2025-06-08 23:15:44,522 - LogUsuariosPipeline - INFO - 📊 Registros después de expansión: 13,077
2025-06-08 23:15:44,522 - LogUsuariosPipeline - INFO - ✅ Función crítica de expansión aplicada exitosamente
2025-06-08 23:15:44,522 - LogUsuariosPipeline - INFO - 🔄 Aplicando mapeo de columnas...
2025-06-08 23:15:44,524 - LogUsuariosPipeline - INFO - 🔄 Procesando tipos de transacciones válidas...
2025-06-08 23:15:44,573 - LogUsuariosPipeline - INFO - 📊 Registros después de concatenar: 13,077
2025-06-08 23:16:00,186 - LogUsuariosPipeline - INFO - 📊 Registros finales para exportar: 13,077
2025-06-08 23:16:00,187 - LogUsuariosPipeline - INFO - 📁 Generando archivos CSV por BankDomain...
2025-06-08 23:16:00,249 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-FCOMPARTAMOS-********231600.csv (12,956 registros)
2025-06-08 23:16:00,252 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-BNACION-********231600.csv (111 registros)
2025-06-08 23:16:00,253 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-CRANDES-********231600.csv (2 registros)
2025-06-08 23:16:00,254 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-CCUSCO-********231600.csv (8 registros)
2025-06-08 23:16:00,254 - LogUsuariosPipeline - INFO - ✅ Procesamiento completado: 4 archivos generados
2025-06-08 23:16:00,254 - LogUsuariosPipeline - INFO - 📊 Total registros procesados: 13,077
2025-06-08 23:16:00,306 - LogUsuariosPipeline - INFO - ✅ Procesamiento exacto completado: 4 archivos
2025-06-08 23:16:00,307 - LogUsuariosPipeline - INFO -   📁 LOGUSR-FCOMPARTAMOS-********231600.csv
2025-06-08 23:16:00,307 - LogUsuariosPipeline - INFO -   📁 LOGUSR-BNACION-********231600.csv
2025-06-08 23:16:00,307 - LogUsuariosPipeline - INFO -   📁 LOGUSR-CRANDES-********231600.csv
2025-06-08 23:16:00,307 - LogUsuariosPipeline - INFO -   📁 LOGUSR-CCUSCO-********231600.csv
2025-06-08 23:16:00,307 - LogUsuariosPipeline - INFO - Archivo temporal eliminado: TEMP_LOGS_USUARIOS/********/WALLET_OLD.parquet
2025-06-08 23:16:00,307 - LogUsuariosPipeline - INFO - EXPORT-CSV: OK
2025-06-08 23:16:00,307 - LogUsuariosPipeline - INFO - === ARCHIVOS CSV GENERADOS ===
2025-06-08 23:16:00,307 - LogUsuariosPipeline - INFO - Total de archivos CSV segmentados: 11
2025-06-08 23:16:00,307 - LogUsuariosPipeline - INFO - Archivos por BankDomain (7) - EXACTO como flujo original:
2025-06-08 23:16:00,307 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-0231FCONFIANZA-********.csv
2025-06-08 23:16:00,307 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-BNACION-********.csv
2025-06-08 23:16:00,307 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-CCUSCO-********.csv
2025-06-08 23:16:00,307 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-CRANDES-********.csv
2025-06-08 23:16:00,307 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-FCOMPARTAMOS-********.csv
2025-06-08 23:16:00,307 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-FCONFIANZA-********.csv
2025-06-08 23:16:00,307 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-NULL-********.csv
2025-06-08 23:16:00,308 - LogUsuariosPipeline - INFO - Estructura OPTIMIZADA: solo archivos segmentados (datos completos en Parquet)
2025-06-08 23:16:00,308 - LogUsuariosPipeline - INFO - === PIPELINE COMPLETADO EXITOSAMENTE ===
2025-06-08 23:16:00,308 - LogUsuariosPipeline - INFO - 
=== ARCHIVOS GENERADOS ===
2025-06-08 23:16:00,308 - LogUsuariosPipeline - INFO - Archivos temporales en TEMP_LOGS_USUARIOS/********:
2025-06-08 23:16:00,308 - LogUsuariosPipeline - INFO -   - USER_ACCOUNT_HISTORY.parquet (0.00 MB)
2025-06-08 23:16:00,308 - LogUsuariosPipeline - INFO -   - USER_DATA_TRX.parquet (127.15 MB)
2025-06-08 23:16:00,308 - LogUsuariosPipeline - INFO -   - USER_MODIFICATION_DAY.parquet (3.36 MB)
2025-06-08 23:16:00,308 - LogUsuariosPipeline - INFO -   - USER_AUTH_CHANGE_HISTORY.parquet (0.11 MB)
2025-06-08 23:16:00,308 - LogUsuariosPipeline - INFO - Archivos de salida en output/********:
2025-06-08 23:16:00,308 - LogUsuariosPipeline - INFO -   - LOG_USR.parquet (4.34 MB)
2025-06-08 23:16:00,308 - LogUsuariosPipeline - INFO -   - LOG_USR_DEDUPLICATED.parquet (4.23 MB)
2025-06-08 23:16:00,308 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-0231FCONFIANZA-********.csv (0.00 MB)
2025-06-08 23:16:00,308 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-BNACION-********.csv (0.11 MB)
2025-06-08 23:16:00,308 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-CCUSCO-********.csv (0.00 MB)
2025-06-08 23:16:00,308 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-CRANDES-********.csv (0.00 MB)
2025-06-08 23:16:00,308 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-FCOMPARTAMOS-********.csv (30.92 MB)
2025-06-08 23:16:00,308 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-FCONFIANZA-********.csv (0.00 MB)
2025-06-08 23:16:00,309 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-NULL-********.csv (0.00 MB)
2025-06-08 23:16:00,309 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-FCOMPARTAMOS-********161535.csv (2.00 MB)
2025-06-08 23:16:00,309 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-BNACION-********161535.csv (0.01 MB)
2025-06-08 23:16:00,309 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-CRANDES-********161535.csv (0.00 MB)
2025-06-08 23:16:00,309 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-CCUSCO-********161535.csv (0.00 MB)
2025-06-08 23:16:00,309 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-FCOMPARTAMOS-********231130.csv (2.00 MB)
2025-06-08 23:16:00,309 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-BNACION-********231130.csv (0.01 MB)
2025-06-08 23:16:00,309 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-CRANDES-********231130.csv (0.00 MB)
2025-06-08 23:16:00,309 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-CCUSCO-********231130.csv (0.00 MB)
2025-06-08 23:16:00,309 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-FCOMPARTAMOS-********231600.csv (2.00 MB)
2025-06-08 23:16:00,309 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-BNACION-********231600.csv (0.01 MB)
2025-06-08 23:16:00,309 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-CRANDES-********231600.csv (0.00 MB)
2025-06-08 23:16:00,309 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-CCUSCO-********231600.csv (0.00 MB)
2025-06-08 23:17:30,064 - LogUsuariosPipeline - INFO - Configurando credenciales S3...
2025-06-08 23:17:30,078 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-08 23:17:30,133 - LogUsuariosPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-08 23:17:30,134 - LogUsuariosPipeline - INFO - === INICIANDO PIPELINE LOG_USUARIOS MODERNIZADO ===
2025-06-08 23:17:30,134 - LogUsuariosPipeline - INFO - Fecha de procesamiento: 2025-06-06 (carpeta: ********)
2025-06-08 23:17:30,134 - LogUsuariosPipeline - INFO - USER-MODIFY: INICIANDO
2025-06-08 23:17:30,134 - LogUsuariosPipeline - INFO - Extrayendo USER_ACCOUNT_HISTORY para fecha: 2025-06-06
2025-06-08 23:17:30,262 - LogUsuariosPipeline - INFO - USER_ACCOUNT_HISTORY extraído: 8 registros -> TEMP_LOGS_USUARIOS/********/USER_ACCOUNT_HISTORY.parquet
2025-06-08 23:17:30,262 - LogUsuariosPipeline - INFO - USER-MODIFY: OK
2025-06-08 23:17:30,262 - LogUsuariosPipeline - INFO - PRE-LOG-USR: INICIANDO
2025-06-08 23:17:30,262 - LogUsuariosPipeline - INFO - Ejecutando SP_PRE_LOG_USR CORREGIDO (incluye USER_IDs múltiples) para fecha: 2025-06-06
2025-06-08 23:17:38,037 - LogUsuariosPipeline - INFO - SP_PRE_LOG_USR completado: 1583130 registros -> TEMP_LOGS_USUARIOS/********/USER_DATA_TRX.parquet
2025-06-08 23:17:38,038 - LogUsuariosPipeline - INFO - Ejecutando SP_USER_MODIFICATION para fecha: 2025-06-06
2025-06-08 23:17:40,260 - LogUsuariosPipeline - INFO - SP_USER_MODIFICATION completado: 6908 registros -> TEMP_LOGS_USUARIOS/********/USER_MODIFICATION_DAY.parquet
2025-06-08 23:17:40,260 - LogUsuariosPipeline - INFO - Ejecutando SP_USER_AUTH_DAY para fecha: 2025-06-06
2025-06-08 23:17:40,684 - LogUsuariosPipeline - INFO - SP_USER_AUTH_DAY completado: 5189 registros -> TEMP_LOGS_USUARIOS/********/USER_AUTH_CHANGE_HISTORY.parquet
2025-06-08 23:17:40,684 - LogUsuariosPipeline - INFO - PRE-LOG-USR: OK
2025-06-08 23:17:40,684 - LogUsuariosPipeline - INFO - LOG-USR: INICIANDO
2025-06-08 23:17:40,684 - LogUsuariosPipeline - INFO - Ejecutando SP_LOG_USR EXACTO (igual a Oracle) para fecha: 2025-06-06
2025-06-08 23:17:42,261 - LogUsuariosPipeline - INFO - SP_LOG_USR EXACTO completado: 19668 registros -> output/********/LOG_USR.parquet
2025-06-08 23:17:42,262 - LogUsuariosPipeline - INFO - LOG-USR: OK
2025-06-08 23:17:42,262 - LogUsuariosPipeline - INFO - EXPORT-CSV: INICIANDO
2025-06-08 23:17:42,262 - LogUsuariosPipeline - INFO - Iniciando exportación CSV con conversiones de perfiles para fecha: 2025-06-06
2025-06-08 23:17:42,262 - LogUsuariosPipeline - INFO - Aplicando SOLO deduplicación como Oracle original (SIN conversión de tipos)
2025-06-08 23:17:42,487 - LogUsuariosPipeline - INFO - Deduplicación completada (manteniendo REQUESTTYPE originales):
2025-06-08 23:17:42,487 - LogUsuariosPipeline - INFO -   - Registros originales: 19,668
2025-06-08 23:17:42,487 - LogUsuariosPipeline - INFO -   - Registros después de deduplicación: 16,069
2025-06-08 23:17:42,487 - LogUsuariosPipeline - INFO -   - Duplicados eliminados: 3,599
2025-06-08 23:17:42,490 - LogUsuariosPipeline - INFO - Tipos de transacción mantenidos:
2025-06-08 23:17:42,490 - LogUsuariosPipeline - INFO -   - User Modification: 6,579 registros
2025-06-08 23:17:42,490 - LogUsuariosPipeline - INFO -   - CHANGE_AUTH_FACTOR: 5,071 registros
2025-06-08 23:17:42,490 - LogUsuariosPipeline - INFO -   - ActivateUser: 1,756 registros
2025-06-08 23:17:42,490 - LogUsuariosPipeline - INFO -   - AfiliaUser: 1,756 registros
2025-06-08 23:17:42,490 - LogUsuariosPipeline - INFO -   - ClosedUserAccount: 289 registros
2025-06-08 23:17:42,490 - LogUsuariosPipeline - INFO -   - Delete User: 289 registros
2025-06-08 23:17:42,490 - LogUsuariosPipeline - INFO -   - ClosedAccount: 289 registros
2025-06-08 23:17:42,490 - LogUsuariosPipeline - INFO -   - Lock Wallet: 24 registros
2025-06-08 23:17:42,491 - LogUsuariosPipeline - INFO -   - Suspend User: 7 registros
2025-06-08 23:17:42,491 - LogUsuariosPipeline - INFO -   - Resume User: 6 registros
2025-06-08 23:17:42,491 - LogUsuariosPipeline - INFO - Aplicando conversiones de perfiles al archivo: output/********/LOG_USR_DEDUPLICATED.parquet
2025-06-08 23:17:42,491 - LogUsuariosPipeline - INFO - Cargando tabla de conversión de perfiles...
2025-06-08 23:17:42,493 - LogUsuariosPipeline - INFO - Tabla conv_perfil.csv cargada: 796 registros
2025-06-08 23:17:42,497 - LogUsuariosPipeline - INFO - Tabla de conversión de perfiles cargada exitosamente en DuckDB
2025-06-08 23:17:42,499 - LogUsuariosPipeline - ERROR - Error aplicando conversiones al archivo: Binder Error: Values list "cp_a" does not have a column named "PERFIL_CODIGO"

LINE 8: ...            LEFT JOIN conv_perfil_table cp_a ON l.perfilA = cp_a.PERFIL_CODIGO
                                                                       ^
2025-06-08 23:17:42,499 - LogUsuariosPipeline - INFO - Omitiendo CSV principal redundante - usando solo archivos Parquet y CSVs segmentados
2025-06-08 23:17:42,499 - LogUsuariosPipeline - INFO - Iniciando exportación segmentada por BankDomain
2025-06-08 23:17:42,499 - LogUsuariosPipeline - INFO - Procesando TODOS los bancos del sistema: ['0231FCONFIANZA', 'BNACION', 'CCUSCO', 'CRANDES', 'FCOMPARTAMOS', 'FCONFIANZA', None]
2025-06-08 23:17:42,503 - LogUsuariosPipeline - INFO - Exportado dominio 0231FCONFIANZA: 0 registros (archivo vacío SIN headers) -> output/********/csv_exports/by_bank/LOG-USUARIOS-0231FCONFIANZA-********.csv
2025-06-08 23:17:42,522 - LogUsuariosPipeline - INFO - Exportado dominio BNACION: 168 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-BNACION-********.csv
2025-06-08 23:17:42,529 - LogUsuariosPipeline - INFO - Exportado dominio CCUSCO: 0 registros (archivo vacío SIN headers) -> output/********/csv_exports/by_bank/LOG-USUARIOS-CCUSCO-********.csv
2025-06-08 23:17:42,535 - LogUsuariosPipeline - INFO - Exportado dominio CRANDES: 4 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-CRANDES-********.csv
2025-06-08 23:17:42,659 - LogUsuariosPipeline - INFO - Exportado dominio FCOMPARTAMOS: 19496 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-FCOMPARTAMOS-********.csv
2025-06-08 23:17:42,663 - LogUsuariosPipeline - INFO - Exportado dominio FCONFIANZA: 0 registros (archivo vacío SIN headers) -> output/********/csv_exports/by_bank/LOG-USUARIOS-FCONFIANZA-********.csv
2025-06-08 23:17:42,668 - LogUsuariosPipeline - INFO - Exportado dominio NULL: 0 registros (archivo vacío SIN headers) -> output/********/csv_exports/by_bank/LOG-USUARIOS-NULL-********.csv
2025-06-08 23:17:42,668 - LogUsuariosPipeline - INFO - Exportación segmentada completada: 7 archivos generados
2025-06-08 23:17:42,668 - LogUsuariosPipeline - INFO - Estructura EXACTA como flujo original: solo by_bank (sin by_profile)
2025-06-08 23:17:42,668 - LogUsuariosPipeline - INFO - 🔄 APLICANDO PROCESAMIENTO EXACTO como procesar.py...
2025-06-08 23:17:42,668 - LogUsuariosPipeline - INFO - 🔄 INICIANDO PROCESAMIENTO EXACTO como procesar.py
2025-06-08 23:17:42,668 - LogUsuariosPipeline - INFO - 📁 Input: output/********/LOG_USR.parquet
2025-06-08 23:17:42,668 - LogUsuariosPipeline - INFO - 📅 Fecha: 2025-06-06
2025-06-08 23:17:42,668 - LogUsuariosPipeline - INFO - 📁 Output: output/********/csv_exports/csv_final_procesado
2025-06-08 23:17:42,668 - LogUsuariosPipeline - INFO - 📊 Cargando datos desde LOG_USR.parquet...
2025-06-08 23:17:42,732 - LogUsuariosPipeline - INFO - 📊 Registros cargados: 19,668
2025-06-08 23:17:42,732 - LogUsuariosPipeline - INFO - 🔄 Procesando grupos por USERHISTID...
2025-06-08 23:17:46,081 - LogUsuariosPipeline - INFO - 📊 Convirtiendo a DataFrame...
2025-06-08 23:17:46,206 - LogUsuariosPipeline - INFO - 📊 Registros después del procesamiento inicial: 48,477
2025-06-08 23:17:46,206 - LogUsuariosPipeline - INFO - 🔍 Aplicando filtro de valores válidos...
2025-06-08 23:17:46,214 - LogUsuariosPipeline - INFO - 📊 Registros después del filtro: 17,990
2025-06-08 23:17:46,214 - LogUsuariosPipeline - INFO - 🥷 Aplicando filtros adicionales de calidad de datos...
2025-06-08 23:17:46,230 - LogUsuariosPipeline - INFO - 📊 Registros después de filtros de calidad: 17,990
2025-06-08 23:17:46,230 - LogUsuariosPipeline - INFO - 🔄 Agrupando y procesando JSON...
2025-06-08 23:17:49,464 - LogUsuariosPipeline - INFO - 📊 Registros después de eliminar duplicados: 13,049
2025-06-08 23:17:49,474 - LogUsuariosPipeline - INFO - 🔄 Aplicando asignaciones a columnas...
2025-06-08 23:17:50,518 - LogUsuariosPipeline - INFO - 📊 Cargando tabla de conversión de perfiles...
2025-06-08 23:17:50,520 - LogUsuariosPipeline - INFO - 📊 Tabla de conversión cargada: 796 registros
2025-06-08 23:17:50,856 - LogUsuariosPipeline - INFO - 🔄 Aplicando función crítica de expansión de registros...
2025-06-08 23:17:50,856 - LogUsuariosPipeline - INFO - 📊 Registros antes de expansión: 13,049
2025-06-08 23:17:51,432 - LogUsuariosPipeline - INFO - 📊 Registros después de expansión: 13,077
2025-06-08 23:17:51,432 - LogUsuariosPipeline - INFO - ✅ Función crítica de expansión aplicada exitosamente
2025-06-08 23:17:51,432 - LogUsuariosPipeline - INFO - 🔄 Aplicando mapeo de columnas...
2025-06-08 23:17:51,434 - LogUsuariosPipeline - INFO - 🔄 Procesando tipos de transacciones válidas...
2025-06-08 23:17:51,483 - LogUsuariosPipeline - INFO - 📊 Registros después de concatenar: 13,077
2025-06-08 23:18:07,076 - LogUsuariosPipeline - INFO - 📊 Registros finales para exportar: 13,077
2025-06-08 23:18:07,076 - LogUsuariosPipeline - INFO - 📁 Generando archivos CSV por BankDomain...
2025-06-08 23:18:07,139 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-FCOMPARTAMOS-********231807.csv (12,956 registros)
2025-06-08 23:18:07,142 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-BNACION-********231807.csv (111 registros)
2025-06-08 23:18:07,144 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-CRANDES-********231807.csv (2 registros)
2025-06-08 23:18:07,145 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-CCUSCO-********231807.csv (8 registros)
2025-06-08 23:18:07,145 - LogUsuariosPipeline - INFO - ✅ Procesamiento completado: 4 archivos generados
2025-06-08 23:18:07,145 - LogUsuariosPipeline - INFO - 📊 Total registros procesados: 13,077
2025-06-08 23:18:07,197 - LogUsuariosPipeline - INFO - ✅ Procesamiento exacto completado: 4 archivos
2025-06-08 23:18:07,198 - LogUsuariosPipeline - INFO -   📁 LOGUSR-FCOMPARTAMOS-********231807.csv
2025-06-08 23:18:07,198 - LogUsuariosPipeline - INFO -   📁 LOGUSR-BNACION-********231807.csv
2025-06-08 23:18:07,198 - LogUsuariosPipeline - INFO -   📁 LOGUSR-CRANDES-********231807.csv
2025-06-08 23:18:07,198 - LogUsuariosPipeline - INFO -   📁 LOGUSR-CCUSCO-********231807.csv
2025-06-08 23:18:07,198 - LogUsuariosPipeline - INFO - Archivo temporal eliminado: TEMP_LOGS_USUARIOS/********/WALLET_OLD.parquet
2025-06-08 23:18:07,198 - LogUsuariosPipeline - INFO - EXPORT-CSV: OK
2025-06-08 23:18:07,198 - LogUsuariosPipeline - INFO - === ARCHIVOS CSV GENERADOS ===
2025-06-08 23:18:07,198 - LogUsuariosPipeline - INFO - Total de archivos CSV segmentados: 11
2025-06-08 23:18:07,198 - LogUsuariosPipeline - INFO - Archivos por BankDomain (7) - EXACTO como flujo original:
2025-06-08 23:18:07,198 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-0231FCONFIANZA-********.csv
2025-06-08 23:18:07,198 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-BNACION-********.csv
2025-06-08 23:18:07,198 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-CCUSCO-********.csv
2025-06-08 23:18:07,198 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-CRANDES-********.csv
2025-06-08 23:18:07,198 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-FCOMPARTAMOS-********.csv
2025-06-08 23:18:07,198 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-FCONFIANZA-********.csv
2025-06-08 23:18:07,198 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-NULL-********.csv
2025-06-08 23:18:07,198 - LogUsuariosPipeline - INFO - Estructura OPTIMIZADA: solo archivos segmentados (datos completos en Parquet)
2025-06-08 23:18:07,198 - LogUsuariosPipeline - INFO - === PIPELINE COMPLETADO EXITOSAMENTE ===
2025-06-08 23:18:07,198 - LogUsuariosPipeline - INFO - 
=== ARCHIVOS GENERADOS ===
2025-06-08 23:18:07,198 - LogUsuariosPipeline - INFO - Archivos temporales en TEMP_LOGS_USUARIOS/********:
2025-06-08 23:18:07,199 - LogUsuariosPipeline - INFO -   - USER_ACCOUNT_HISTORY.parquet (0.00 MB)
2025-06-08 23:18:07,199 - LogUsuariosPipeline - INFO -   - USER_DATA_TRX.parquet (127.15 MB)
2025-06-08 23:18:07,199 - LogUsuariosPipeline - INFO -   - USER_MODIFICATION_DAY.parquet (3.36 MB)
2025-06-08 23:18:07,199 - LogUsuariosPipeline - INFO -   - USER_AUTH_CHANGE_HISTORY.parquet (0.11 MB)
2025-06-08 23:18:07,199 - LogUsuariosPipeline - INFO - Archivos de salida en output/********:
2025-06-08 23:18:07,199 - LogUsuariosPipeline - INFO -   - LOG_USR.parquet (4.34 MB)
2025-06-08 23:18:07,199 - LogUsuariosPipeline - INFO -   - LOG_USR_DEDUPLICATED.parquet (4.23 MB)
2025-06-08 23:18:07,199 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-0231FCONFIANZA-********.csv (0.00 MB)
2025-06-08 23:18:07,199 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-BNACION-********.csv (0.11 MB)
2025-06-08 23:18:07,199 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-CCUSCO-********.csv (0.00 MB)
2025-06-08 23:18:07,199 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-CRANDES-********.csv (0.00 MB)
2025-06-08 23:18:07,199 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-FCOMPARTAMOS-********.csv (30.92 MB)
2025-06-08 23:18:07,199 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-FCONFIANZA-********.csv (0.00 MB)
2025-06-08 23:18:07,199 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-NULL-********.csv (0.00 MB)
2025-06-08 23:18:07,199 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-FCOMPARTAMOS-********161535.csv (2.00 MB)
2025-06-08 23:18:07,199 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-BNACION-********161535.csv (0.01 MB)
2025-06-08 23:18:07,199 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-CRANDES-********161535.csv (0.00 MB)
2025-06-08 23:18:07,199 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-CCUSCO-********161535.csv (0.00 MB)
2025-06-08 23:18:07,199 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-FCOMPARTAMOS-********231130.csv (2.00 MB)
2025-06-08 23:18:07,200 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-BNACION-********231130.csv (0.01 MB)
2025-06-08 23:18:07,200 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-CRANDES-********231130.csv (0.00 MB)
2025-06-08 23:18:07,200 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-CCUSCO-********231130.csv (0.00 MB)
2025-06-08 23:18:07,200 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-FCOMPARTAMOS-********231600.csv (2.00 MB)
2025-06-08 23:18:07,200 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-BNACION-********231600.csv (0.01 MB)
2025-06-08 23:18:07,200 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-CRANDES-********231600.csv (0.00 MB)
2025-06-08 23:18:07,200 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-CCUSCO-********231600.csv (0.00 MB)
2025-06-08 23:18:07,200 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-FCOMPARTAMOS-********231807.csv (2.00 MB)
2025-06-08 23:18:07,200 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-BNACION-********231807.csv (0.01 MB)
2025-06-08 23:18:07,200 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-CRANDES-********231807.csv (0.00 MB)
2025-06-08 23:18:07,200 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-CCUSCO-********231807.csv (0.00 MB)
2025-06-08 23:24:16,710 - LogUsuariosPipeline - INFO - Configurando credenciales S3...
2025-06-08 23:24:16,722 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-08 23:24:16,772 - LogUsuariosPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-08 23:24:16,773 - LogUsuariosPipeline - INFO - === INICIANDO PIPELINE LOG_USUARIOS MODERNIZADO ===
2025-06-08 23:24:16,773 - LogUsuariosPipeline - INFO - Fecha de procesamiento: 2025-06-06 (carpeta: ********)
2025-06-08 23:24:16,773 - LogUsuariosPipeline - INFO - USER-MODIFY: INICIANDO
2025-06-08 23:24:16,773 - LogUsuariosPipeline - INFO - Extrayendo USER_ACCOUNT_HISTORY para fecha: 2025-06-06
2025-06-08 23:24:16,908 - LogUsuariosPipeline - INFO - USER_ACCOUNT_HISTORY extraído: 8 registros -> TEMP_LOGS_USUARIOS/********/USER_ACCOUNT_HISTORY.parquet
2025-06-08 23:24:16,908 - LogUsuariosPipeline - INFO - USER-MODIFY: OK
2025-06-08 23:24:16,909 - LogUsuariosPipeline - INFO - PRE-LOG-USR: INICIANDO
2025-06-08 23:24:16,909 - LogUsuariosPipeline - INFO - Ejecutando SP_PRE_LOG_USR CORREGIDO (incluye USER_IDs múltiples) para fecha: 2025-06-06
2025-06-08 23:24:25,115 - LogUsuariosPipeline - INFO - SP_PRE_LOG_USR completado: 1583130 registros -> TEMP_LOGS_USUARIOS/********/USER_DATA_TRX.parquet
2025-06-08 23:24:25,116 - LogUsuariosPipeline - INFO - Ejecutando SP_USER_MODIFICATION para fecha: 2025-06-06
2025-06-08 23:24:27,489 - LogUsuariosPipeline - INFO - SP_USER_MODIFICATION completado: 6908 registros -> TEMP_LOGS_USUARIOS/********/USER_MODIFICATION_DAY.parquet
2025-06-08 23:24:27,489 - LogUsuariosPipeline - INFO - Ejecutando SP_USER_AUTH_DAY para fecha: 2025-06-06
2025-06-08 23:24:27,969 - LogUsuariosPipeline - INFO - SP_USER_AUTH_DAY completado: 5189 registros -> TEMP_LOGS_USUARIOS/********/USER_AUTH_CHANGE_HISTORY.parquet
2025-06-08 23:24:27,969 - LogUsuariosPipeline - INFO - PRE-LOG-USR: OK
2025-06-08 23:24:27,969 - LogUsuariosPipeline - INFO - LOG-USR: INICIANDO
2025-06-08 23:24:27,969 - LogUsuariosPipeline - INFO - Ejecutando SP_LOG_USR EXACTO (igual a Oracle) para fecha: 2025-06-06
2025-06-08 23:24:29,642 - LogUsuariosPipeline - INFO - SP_LOG_USR EXACTO completado: 19668 registros -> output/********/LOG_USR.parquet
2025-06-08 23:24:29,642 - LogUsuariosPipeline - INFO - LOG-USR: OK
2025-06-08 23:24:29,643 - LogUsuariosPipeline - INFO - EXPORT-CSV: INICIANDO
2025-06-08 23:24:29,643 - LogUsuariosPipeline - INFO - Iniciando exportación CSV con conversiones de perfiles para fecha: 2025-06-06
2025-06-08 23:24:29,643 - LogUsuariosPipeline - INFO - Aplicando SOLO deduplicación como Oracle original (SIN conversión de tipos)
2025-06-08 23:24:29,874 - LogUsuariosPipeline - INFO - Deduplicación completada (manteniendo REQUESTTYPE originales):
2025-06-08 23:24:29,874 - LogUsuariosPipeline - INFO -   - Registros originales: 19,668
2025-06-08 23:24:29,874 - LogUsuariosPipeline - INFO -   - Registros después de deduplicación: 16,069
2025-06-08 23:24:29,874 - LogUsuariosPipeline - INFO -   - Duplicados eliminados: 3,599
2025-06-08 23:24:29,876 - LogUsuariosPipeline - INFO - Tipos de transacción mantenidos:
2025-06-08 23:24:29,876 - LogUsuariosPipeline - INFO -   - User Modification: 6,579 registros
2025-06-08 23:24:29,876 - LogUsuariosPipeline - INFO -   - CHANGE_AUTH_FACTOR: 5,071 registros
2025-06-08 23:24:29,876 - LogUsuariosPipeline - INFO -   - ActivateUser: 1,756 registros
2025-06-08 23:24:29,876 - LogUsuariosPipeline - INFO -   - AfiliaUser: 1,756 registros
2025-06-08 23:24:29,876 - LogUsuariosPipeline - INFO -   - Delete User: 289 registros
2025-06-08 23:24:29,876 - LogUsuariosPipeline - INFO -   - ClosedUserAccount: 289 registros
2025-06-08 23:24:29,877 - LogUsuariosPipeline - INFO -   - ClosedAccount: 289 registros
2025-06-08 23:24:29,877 - LogUsuariosPipeline - INFO -   - Lock Wallet: 24 registros
2025-06-08 23:24:29,877 - LogUsuariosPipeline - INFO -   - Suspend User: 7 registros
2025-06-08 23:24:29,877 - LogUsuariosPipeline - INFO -   - Resume User: 6 registros
2025-06-08 23:24:29,877 - LogUsuariosPipeline - INFO - Aplicando conversiones de perfiles al archivo: output/********/LOG_USR_DEDUPLICATED.parquet
2025-06-08 23:24:29,877 - LogUsuariosPipeline - INFO - Cargando tabla de conversión de perfiles...
2025-06-08 23:24:29,879 - LogUsuariosPipeline - INFO - Tabla conv_perfil.csv cargada: 796 registros
2025-06-08 23:24:29,883 - LogUsuariosPipeline - INFO - Tabla de conversión de perfiles cargada exitosamente en DuckDB
2025-06-08 23:24:29,885 - LogUsuariosPipeline - ERROR - Error aplicando conversiones al archivo: Binder Error: Values list "cp_a" does not have a column named "PERFIL_CODIGO"

LINE 8: ...            LEFT JOIN conv_perfil_table cp_a ON l.perfilA = cp_a.PERFIL_CODIGO
                                                                       ^
2025-06-08 23:24:29,885 - LogUsuariosPipeline - INFO - Omitiendo CSV principal redundante - usando solo archivos Parquet y CSVs segmentados
2025-06-08 23:24:29,885 - LogUsuariosPipeline - INFO - Iniciando exportación segmentada por BankDomain
2025-06-08 23:24:29,885 - LogUsuariosPipeline - INFO - Procesando TODOS los bancos del sistema: ['0231FCONFIANZA', 'BNACION', 'CCUSCO', 'CRANDES', 'FCOMPARTAMOS', 'FCONFIANZA', None]
2025-06-08 23:24:29,889 - LogUsuariosPipeline - INFO - Exportado dominio 0231FCONFIANZA: 0 registros (archivo vacío SIN headers) -> output/********/csv_exports/by_bank/LOG-USUARIOS-0231FCONFIANZA-********.csv
2025-06-08 23:24:29,909 - LogUsuariosPipeline - INFO - Exportado dominio BNACION: 168 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-BNACION-********.csv
2025-06-08 23:24:29,919 - LogUsuariosPipeline - INFO - Exportado dominio CCUSCO: 0 registros (archivo vacío SIN headers) -> output/********/csv_exports/by_bank/LOG-USUARIOS-CCUSCO-********.csv
2025-06-08 23:24:29,925 - LogUsuariosPipeline - INFO - Exportado dominio CRANDES: 4 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-CRANDES-********.csv
2025-06-08 23:24:30,047 - LogUsuariosPipeline - INFO - Exportado dominio FCOMPARTAMOS: 19496 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-FCOMPARTAMOS-********.csv
2025-06-08 23:24:30,052 - LogUsuariosPipeline - INFO - Exportado dominio FCONFIANZA: 0 registros (archivo vacío SIN headers) -> output/********/csv_exports/by_bank/LOG-USUARIOS-FCONFIANZA-********.csv
2025-06-08 23:24:30,056 - LogUsuariosPipeline - INFO - Exportado dominio NULL: 0 registros (archivo vacío SIN headers) -> output/********/csv_exports/by_bank/LOG-USUARIOS-NULL-********.csv
2025-06-08 23:24:30,056 - LogUsuariosPipeline - INFO - Exportación segmentada completada: 7 archivos generados
2025-06-08 23:24:30,056 - LogUsuariosPipeline - INFO - Estructura EXACTA como flujo original: solo by_bank (sin by_profile)
2025-06-08 23:24:30,056 - LogUsuariosPipeline - INFO - 🔄 APLICANDO PROCESAMIENTO EXACTO como procesar.py...
2025-06-08 23:24:30,057 - LogUsuariosPipeline - INFO - 🔄 INICIANDO PROCESAMIENTO EXACTO como procesar.py
2025-06-08 23:24:30,057 - LogUsuariosPipeline - INFO - 📁 Input: output/********/LOG_USR.parquet
2025-06-08 23:24:30,057 - LogUsuariosPipeline - INFO - 📅 Fecha: 2025-06-06
2025-06-08 23:24:30,057 - LogUsuariosPipeline - INFO - 📁 Output: output/********/csv_exports/csv_final_procesado
2025-06-08 23:24:30,057 - LogUsuariosPipeline - INFO - 📊 Cargando datos desde LOG_USR.parquet...
2025-06-08 23:24:30,123 - LogUsuariosPipeline - INFO - 📊 Registros cargados: 19,668
2025-06-08 23:24:30,123 - LogUsuariosPipeline - INFO - 🔄 Procesando grupos por USERHISTID...
2025-06-08 23:24:33,424 - LogUsuariosPipeline - INFO - 📊 Convirtiendo a DataFrame...
2025-06-08 23:24:33,556 - LogUsuariosPipeline - INFO - 📊 Registros después del procesamiento inicial: 48,477
2025-06-08 23:24:33,556 - LogUsuariosPipeline - INFO - 🔍 Aplicando filtro de valores válidos...
2025-06-08 23:24:33,565 - LogUsuariosPipeline - INFO - 📊 Registros después del filtro: 17,990
2025-06-08 23:24:33,565 - LogUsuariosPipeline - INFO - 🥷 Aplicando filtros adicionales de calidad de datos...
2025-06-08 23:24:33,582 - LogUsuariosPipeline - INFO - 📊 Registros después de filtros de calidad: 17,990
2025-06-08 23:24:33,582 - LogUsuariosPipeline - INFO - 🔄 Agrupando y procesando JSON...
2025-06-08 23:24:36,655 - LogUsuariosPipeline - INFO - 📊 Registros después de eliminar duplicados: 13,049
2025-06-08 23:24:36,665 - LogUsuariosPipeline - INFO - 🔄 Aplicando asignaciones a columnas...
2025-06-08 23:24:37,719 - LogUsuariosPipeline - INFO - 📊 Cargando tabla de conversión de perfiles...
2025-06-08 23:24:37,721 - LogUsuariosPipeline - INFO - 📊 Tabla de conversión cargada: 796 registros
2025-06-08 23:24:38,056 - LogUsuariosPipeline - INFO - 🔄 Aplicando función crítica de expansión de registros...
2025-06-08 23:24:38,056 - LogUsuariosPipeline - INFO - 📊 Registros antes de expansión: 13,049
2025-06-08 23:24:38,622 - LogUsuariosPipeline - INFO - 📊 Registros después de expansión: 13,077
2025-06-08 23:24:38,622 - LogUsuariosPipeline - INFO - ✅ Función crítica de expansión aplicada exitosamente
2025-06-08 23:24:38,622 - LogUsuariosPipeline - INFO - 🔄 Aplicando mapeo de columnas...
2025-06-08 23:24:38,624 - LogUsuariosPipeline - INFO - 🔄 Procesando tipos de transacciones válidas...
2025-06-08 23:24:38,673 - LogUsuariosPipeline - INFO - 📊 Registros después de concatenar: 13,077
2025-06-08 23:24:54,243 - LogUsuariosPipeline - INFO - 📊 Registros finales para exportar: 13,077
2025-06-08 23:24:54,243 - LogUsuariosPipeline - INFO - 📁 Generando archivos CSV por BankDomain...
2025-06-08 23:24:54,313 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-FCOMPARTAMOS-********232454.csv (12,956 registros)
2025-06-08 23:24:54,317 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-BNACION-********232454.csv (111 registros)
2025-06-08 23:24:54,318 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-CRANDES-********232454.csv (2 registros)
2025-06-08 23:24:54,319 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-CCUSCO-********232454.csv (8 registros)
2025-06-08 23:24:54,319 - LogUsuariosPipeline - INFO - ✅ Procesamiento completado: 4 archivos generados
2025-06-08 23:24:54,319 - LogUsuariosPipeline - INFO - 📊 Total registros procesados: 13,077
2025-06-08 23:24:54,373 - LogUsuariosPipeline - INFO - ✅ Procesamiento exacto completado: 4 archivos
2025-06-08 23:24:54,373 - LogUsuariosPipeline - INFO -   📁 LOGUSR-FCOMPARTAMOS-********232454.csv
2025-06-08 23:24:54,373 - LogUsuariosPipeline - INFO -   📁 LOGUSR-BNACION-********232454.csv
2025-06-08 23:24:54,373 - LogUsuariosPipeline - INFO -   📁 LOGUSR-CRANDES-********232454.csv
2025-06-08 23:24:54,373 - LogUsuariosPipeline - INFO -   📁 LOGUSR-CCUSCO-********232454.csv
2025-06-08 23:24:54,373 - LogUsuariosPipeline - INFO - Archivo temporal eliminado: TEMP_LOGS_USUARIOS/********/WALLET_OLD.parquet
2025-06-08 23:24:54,374 - LogUsuariosPipeline - INFO - EXPORT-CSV: OK
2025-06-08 23:24:54,374 - LogUsuariosPipeline - INFO - === ARCHIVOS CSV GENERADOS ===
2025-06-08 23:24:54,374 - LogUsuariosPipeline - INFO - Total de archivos CSV segmentados: 11
2025-06-08 23:24:54,374 - LogUsuariosPipeline - INFO - Archivos por BankDomain (7) - EXACTO como flujo original:
2025-06-08 23:24:54,374 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-0231FCONFIANZA-********.csv
2025-06-08 23:24:54,374 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-BNACION-********.csv
2025-06-08 23:24:54,374 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-CCUSCO-********.csv
2025-06-08 23:24:54,374 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-CRANDES-********.csv
2025-06-08 23:24:54,374 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-FCOMPARTAMOS-********.csv
2025-06-08 23:24:54,374 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-FCONFIANZA-********.csv
2025-06-08 23:24:54,374 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-NULL-********.csv
2025-06-08 23:24:54,374 - LogUsuariosPipeline - INFO - Estructura OPTIMIZADA: solo archivos segmentados (datos completos en Parquet)
2025-06-08 23:24:54,374 - LogUsuariosPipeline - INFO - === PIPELINE COMPLETADO EXITOSAMENTE ===
2025-06-08 23:24:54,374 - LogUsuariosPipeline - INFO - 
=== ARCHIVOS GENERADOS ===
2025-06-08 23:24:54,374 - LogUsuariosPipeline - INFO - Archivos temporales en TEMP_LOGS_USUARIOS/********:
2025-06-08 23:24:54,374 - LogUsuariosPipeline - INFO -   - USER_ACCOUNT_HISTORY.parquet (0.00 MB)
2025-06-08 23:24:54,374 - LogUsuariosPipeline - INFO -   - USER_DATA_TRX.parquet (127.15 MB)
2025-06-08 23:24:54,375 - LogUsuariosPipeline - INFO -   - USER_MODIFICATION_DAY.parquet (3.36 MB)
2025-06-08 23:24:54,375 - LogUsuariosPipeline - INFO -   - USER_AUTH_CHANGE_HISTORY.parquet (0.11 MB)
2025-06-08 23:24:54,375 - LogUsuariosPipeline - INFO - Archivos de salida en output/********:
2025-06-08 23:24:54,375 - LogUsuariosPipeline - INFO -   - LOG_USR.parquet (4.34 MB)
2025-06-08 23:24:54,375 - LogUsuariosPipeline - INFO -   - LOG_USR_DEDUPLICATED.parquet (4.23 MB)
2025-06-08 23:24:54,375 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-0231FCONFIANZA-********.csv (0.00 MB)
2025-06-08 23:24:54,375 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-BNACION-********.csv (0.11 MB)
2025-06-08 23:24:54,375 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-CCUSCO-********.csv (0.00 MB)
2025-06-08 23:24:54,375 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-CRANDES-********.csv (0.00 MB)
2025-06-08 23:24:54,375 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-FCOMPARTAMOS-********.csv (30.92 MB)
2025-06-08 23:24:54,375 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-FCONFIANZA-********.csv (0.00 MB)
2025-06-08 23:24:54,375 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-NULL-********.csv (0.00 MB)
2025-06-08 23:24:54,375 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-FCOMPARTAMOS-********232454.csv (2.00 MB)
2025-06-08 23:24:54,375 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-BNACION-********232454.csv (0.01 MB)
2025-06-08 23:24:54,375 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-CRANDES-********232454.csv (0.00 MB)
2025-06-08 23:24:54,375 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-CCUSCO-********232454.csv (0.00 MB)
2025-06-08 23:29:51,252 - LogUsuariosPipeline - INFO - Configurando credenciales S3...
2025-06-08 23:29:51,266 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-08 23:29:51,320 - LogUsuariosPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-08 23:29:51,320 - LogUsuariosPipeline - INFO - === INICIANDO PIPELINE LOG_USUARIOS MODERNIZADO ===
2025-06-08 23:29:51,320 - LogUsuariosPipeline - INFO - Fecha de procesamiento: 2025-06-05 (carpeta: ********)
2025-06-08 23:29:51,321 - LogUsuariosPipeline - INFO - USER-MODIFY: INICIANDO
2025-06-08 23:29:51,321 - LogUsuariosPipeline - INFO - Extrayendo USER_ACCOUNT_HISTORY para fecha: 2025-06-05
2025-06-08 23:29:51,483 - LogUsuariosPipeline - INFO - USER_ACCOUNT_HISTORY extraído: 12 registros -> TEMP_LOGS_USUARIOS/********/USER_ACCOUNT_HISTORY.parquet
2025-06-08 23:29:51,483 - LogUsuariosPipeline - INFO - USER-MODIFY: OK
2025-06-08 23:29:51,483 - LogUsuariosPipeline - INFO - PRE-LOG-USR: INICIANDO
2025-06-08 23:29:51,483 - LogUsuariosPipeline - INFO - Ejecutando SP_PRE_LOG_USR CORREGIDO (incluye USER_IDs múltiples) para fecha: 2025-06-05
2025-06-08 23:29:59,108 - LogUsuariosPipeline - INFO - SP_PRE_LOG_USR completado: 1581374 registros -> TEMP_LOGS_USUARIOS/********/USER_DATA_TRX.parquet
2025-06-08 23:29:59,109 - LogUsuariosPipeline - INFO - Ejecutando SP_USER_MODIFICATION para fecha: 2025-06-05
2025-06-08 23:30:01,370 - LogUsuariosPipeline - INFO - SP_USER_MODIFICATION completado: 7438 registros -> TEMP_LOGS_USUARIOS/********/USER_MODIFICATION_DAY.parquet
2025-06-08 23:30:01,370 - LogUsuariosPipeline - INFO - Ejecutando SP_USER_AUTH_DAY para fecha: 2025-06-05
2025-06-08 23:30:01,779 - LogUsuariosPipeline - INFO - SP_USER_AUTH_DAY completado: 5804 registros -> TEMP_LOGS_USUARIOS/********/USER_AUTH_CHANGE_HISTORY.parquet
2025-06-08 23:30:01,779 - LogUsuariosPipeline - INFO - PRE-LOG-USR: OK
2025-06-08 23:30:01,779 - LogUsuariosPipeline - INFO - LOG-USR: INICIANDO
2025-06-08 23:30:01,779 - LogUsuariosPipeline - INFO - Ejecutando SP_LOG_USR EXACTO (igual a Oracle) para fecha: 2025-06-05
2025-06-08 23:30:03,349 - LogUsuariosPipeline - INFO - SP_LOG_USR EXACTO completado: 21396 registros -> output/********/LOG_USR.parquet
2025-06-08 23:30:03,350 - LogUsuariosPipeline - INFO - LOG-USR: OK
2025-06-08 23:30:03,350 - LogUsuariosPipeline - INFO - EXPORT-CSV: INICIANDO
2025-06-08 23:30:03,350 - LogUsuariosPipeline - INFO - Iniciando exportación CSV con conversiones de perfiles para fecha: 2025-06-05
2025-06-08 23:30:03,350 - LogUsuariosPipeline - INFO - Aplicando SOLO deduplicación como Oracle original (SIN conversión de tipos)
2025-06-08 23:30:03,616 - LogUsuariosPipeline - INFO - Deduplicación completada (manteniendo REQUESTTYPE originales):
2025-06-08 23:30:03,616 - LogUsuariosPipeline - INFO -   - Registros originales: 21,396
2025-06-08 23:30:03,616 - LogUsuariosPipeline - INFO -   - Registros después de deduplicación: 17,305
2025-06-08 23:30:03,616 - LogUsuariosPipeline - INFO -   - Duplicados eliminados: 4,091
2025-06-08 23:30:03,618 - LogUsuariosPipeline - INFO - Tipos de transacción mantenidos:
2025-06-08 23:30:03,618 - LogUsuariosPipeline - INFO -   - User Modification: 7,193 registros
2025-06-08 23:30:03,618 - LogUsuariosPipeline - INFO -   - CHANGE_AUTH_FACTOR: 5,677 registros
2025-06-08 23:30:03,618 - LogUsuariosPipeline - INFO -   - AfiliaUser: 1,877 registros
2025-06-08 23:30:03,618 - LogUsuariosPipeline - INFO -   - ActivateUser: 1,877 registros
2025-06-08 23:30:03,618 - LogUsuariosPipeline - INFO -   - ClosedUserAccount: 218 registros
2025-06-08 23:30:03,618 - LogUsuariosPipeline - INFO -   - ClosedAccount: 218 registros
2025-06-08 23:30:03,618 - LogUsuariosPipeline - INFO -   - Delete User: 218 registros
2025-06-08 23:30:03,618 - LogUsuariosPipeline - INFO -   - Lock Wallet: 18 registros
2025-06-08 23:30:03,618 - LogUsuariosPipeline - INFO -   - Resume User: 7 registros
2025-06-08 23:30:03,618 - LogUsuariosPipeline - INFO -   - Suspend User: 2 registros
2025-06-08 23:30:03,618 - LogUsuariosPipeline - INFO - Aplicando conversiones de perfiles al archivo: output/********/LOG_USR_DEDUPLICATED.parquet
2025-06-08 23:30:03,618 - LogUsuariosPipeline - INFO - Cargando tabla de conversión de perfiles...
2025-06-08 23:30:03,621 - LogUsuariosPipeline - INFO - Tabla conv_perfil.csv cargada: 796 registros
2025-06-08 23:30:03,625 - LogUsuariosPipeline - INFO - Tabla de conversión de perfiles cargada exitosamente en DuckDB
2025-06-08 23:30:03,627 - LogUsuariosPipeline - ERROR - Error aplicando conversiones al archivo: Binder Error: Values list "cp_a" does not have a column named "PERFIL_CODIGO"

LINE 8: ...            LEFT JOIN conv_perfil_table cp_a ON l.perfilA = cp_a.PERFIL_CODIGO
                                                                       ^
2025-06-08 23:30:03,627 - LogUsuariosPipeline - INFO - Omitiendo CSV principal redundante - usando solo archivos Parquet y CSVs segmentados
2025-06-08 23:30:03,627 - LogUsuariosPipeline - INFO - Iniciando exportación segmentada por BankDomain
2025-06-08 23:30:03,627 - LogUsuariosPipeline - INFO - Procesando TODOS los bancos del sistema: ['0231FCONFIANZA', 'BNACION', 'CCUSCO', 'CRANDES', 'FCOMPARTAMOS', 'FCONFIANZA', None]
2025-06-08 23:30:03,633 - LogUsuariosPipeline - INFO - Exportado dominio 0231FCONFIANZA: 0 registros (archivo vacío SIN headers) -> output/********/csv_exports/by_bank/LOG-USUARIOS-0231FCONFIANZA-********.csv
2025-06-08 23:30:03,653 - LogUsuariosPipeline - INFO - Exportado dominio BNACION: 87 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-BNACION-********.csv
2025-06-08 23:30:03,659 - LogUsuariosPipeline - INFO - Exportado dominio CCUSCO: 2 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-CCUSCO-********.csv
2025-06-08 23:30:03,681 - LogUsuariosPipeline - INFO - Exportado dominio CRANDES: 10 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-CRANDES-********.csv
2025-06-08 23:30:03,811 - LogUsuariosPipeline - INFO - Exportado dominio FCOMPARTAMOS: 21279 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-FCOMPARTAMOS-********.csv
2025-06-08 23:30:03,816 - LogUsuariosPipeline - INFO - Exportado dominio FCONFIANZA: 0 registros (archivo vacío SIN headers) -> output/********/csv_exports/by_bank/LOG-USUARIOS-FCONFIANZA-********.csv
2025-06-08 23:30:03,845 - LogUsuariosPipeline - INFO - Exportado dominio NULL: 18 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-NULL-********.csv
2025-06-08 23:30:03,846 - LogUsuariosPipeline - INFO - Exportación segmentada completada: 7 archivos generados
2025-06-08 23:30:03,846 - LogUsuariosPipeline - INFO - Estructura EXACTA como flujo original: solo by_bank (sin by_profile)
2025-06-08 23:30:03,846 - LogUsuariosPipeline - INFO - 🔄 APLICANDO PROCESAMIENTO EXACTO como procesar.py...
2025-06-08 23:30:03,846 - LogUsuariosPipeline - INFO - 🔄 INICIANDO PROCESAMIENTO EXACTO como procesar.py
2025-06-08 23:30:03,846 - LogUsuariosPipeline - INFO - 📁 Input: output/********/LOG_USR.parquet
2025-06-08 23:30:03,846 - LogUsuariosPipeline - INFO - 📅 Fecha: 2025-06-05
2025-06-08 23:30:03,846 - LogUsuariosPipeline - INFO - 📁 Output: output/********/csv_exports/csv_final_procesado
2025-06-08 23:30:03,846 - LogUsuariosPipeline - INFO - 📊 Cargando datos desde LOG_USR.parquet...
2025-06-08 23:30:03,914 - LogUsuariosPipeline - INFO - 📊 Registros cargados: 21,396
2025-06-08 23:30:03,914 - LogUsuariosPipeline - INFO - 🔄 Procesando grupos por USERHISTID...
2025-06-08 23:30:07,439 - LogUsuariosPipeline - INFO - 📊 Convirtiendo a DataFrame...
2025-06-08 23:30:07,583 - LogUsuariosPipeline - INFO - 📊 Registros después del procesamiento inicial: 51,980
2025-06-08 23:30:07,583 - LogUsuariosPipeline - INFO - 🔍 Aplicando filtro de valores válidos...
2025-06-08 23:30:07,592 - LogUsuariosPipeline - INFO - 📊 Registros después del filtro: 19,732
2025-06-08 23:30:07,592 - LogUsuariosPipeline - INFO - 🥷 Aplicando filtros adicionales de calidad de datos...
2025-06-08 23:30:07,601 - LogUsuariosPipeline - INFO -   🔍 Eliminados 18 registros con BANKDOMAIN inválido
2025-06-08 23:30:07,614 - LogUsuariosPipeline - INFO - 📊 Registros después de filtros de calidad: 19,714
2025-06-08 23:30:07,614 - LogUsuariosPipeline - INFO - 🔄 Agrupando y procesando JSON...
2025-06-08 23:30:10,990 - LogUsuariosPipeline - INFO - 📊 Registros después de eliminar duplicados: 14,144
2025-06-08 23:30:11,002 - LogUsuariosPipeline - INFO - 🔄 Aplicando asignaciones a columnas...
2025-06-08 23:30:12,113 - LogUsuariosPipeline - INFO - 📊 Cargando tabla de conversión de perfiles...
2025-06-08 23:30:12,114 - LogUsuariosPipeline - INFO - 📊 Tabla de conversión cargada: 796 registros
2025-06-08 23:30:12,485 - LogUsuariosPipeline - INFO - 🔄 Aplicando función crítica de expansión de registros...
2025-06-08 23:30:12,485 - LogUsuariosPipeline - INFO - 📊 Registros antes de expansión: 14,144
2025-06-08 23:30:13,172 - LogUsuariosPipeline - INFO - 📊 Registros después de expansión: 14,160
2025-06-08 23:30:13,172 - LogUsuariosPipeline - INFO - ✅ Función crítica de expansión aplicada exitosamente
2025-06-08 23:30:13,172 - LogUsuariosPipeline - INFO - 🔄 Aplicando mapeo de columnas...
2025-06-08 23:30:13,174 - LogUsuariosPipeline - INFO - 🔄 Procesando tipos de transacciones válidas...
2025-06-08 23:30:13,224 - LogUsuariosPipeline - INFO - 📊 Registros después de concatenar: 14,160
2025-06-08 23:30:30,079 - LogUsuariosPipeline - INFO - 📊 Registros finales para exportar: 14,160
2025-06-08 23:30:30,079 - LogUsuariosPipeline - INFO - 📁 Generando archivos CSV por BankDomain...
2025-06-08 23:30:30,148 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-FCOMPARTAMOS-********233030.csv (14,098 registros)
2025-06-08 23:30:30,151 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-BNACION-********233030.csv (55 registros)
2025-06-08 23:30:30,153 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-CRANDES-********233030.csv (6 registros)
2025-06-08 23:30:30,154 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-CCUSCO-********233030.csv (1 registros)
2025-06-08 23:30:30,154 - LogUsuariosPipeline - INFO - ✅ Procesamiento completado: 4 archivos generados
2025-06-08 23:30:30,154 - LogUsuariosPipeline - INFO - 📊 Total registros procesados: 14,160
2025-06-08 23:30:30,213 - LogUsuariosPipeline - INFO - ✅ Procesamiento exacto completado: 4 archivos
2025-06-08 23:30:30,214 - LogUsuariosPipeline - INFO -   📁 LOGUSR-FCOMPARTAMOS-********233030.csv
2025-06-08 23:30:30,214 - LogUsuariosPipeline - INFO -   📁 LOGUSR-BNACION-********233030.csv
2025-06-08 23:30:30,214 - LogUsuariosPipeline - INFO -   📁 LOGUSR-CRANDES-********233030.csv
2025-06-08 23:30:30,214 - LogUsuariosPipeline - INFO -   📁 LOGUSR-CCUSCO-********233030.csv
2025-06-08 23:30:30,214 - LogUsuariosPipeline - INFO - Archivo temporal eliminado: TEMP_LOGS_USUARIOS/********/WALLET_OLD.parquet
2025-06-08 23:30:30,214 - LogUsuariosPipeline - INFO - EXPORT-CSV: OK
2025-06-08 23:30:30,214 - LogUsuariosPipeline - INFO - === ARCHIVOS CSV GENERADOS ===
2025-06-08 23:30:30,214 - LogUsuariosPipeline - INFO - Total de archivos CSV segmentados: 11
2025-06-08 23:30:30,214 - LogUsuariosPipeline - INFO - Archivos por BankDomain (7) - EXACTO como flujo original:
2025-06-08 23:30:30,214 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-0231FCONFIANZA-********.csv
2025-06-08 23:30:30,214 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-BNACION-********.csv
2025-06-08 23:30:30,214 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-CCUSCO-********.csv
2025-06-08 23:30:30,214 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-CRANDES-********.csv
2025-06-08 23:30:30,214 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-FCOMPARTAMOS-********.csv
2025-06-08 23:30:30,214 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-FCONFIANZA-********.csv
2025-06-08 23:30:30,214 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-NULL-********.csv
2025-06-08 23:30:30,214 - LogUsuariosPipeline - INFO - Estructura OPTIMIZADA: solo archivos segmentados (datos completos en Parquet)
2025-06-08 23:30:30,214 - LogUsuariosPipeline - INFO - === PIPELINE COMPLETADO EXITOSAMENTE ===
2025-06-08 23:30:30,214 - LogUsuariosPipeline - INFO - 
=== ARCHIVOS GENERADOS ===
2025-06-08 23:30:30,214 - LogUsuariosPipeline - INFO - Archivos temporales en TEMP_LOGS_USUARIOS/********:
2025-06-08 23:30:30,215 - LogUsuariosPipeline - INFO -   - USER_ACCOUNT_HISTORY.parquet (0.00 MB)
2025-06-08 23:30:30,215 - LogUsuariosPipeline - INFO -   - USER_DATA_TRX.parquet (126.93 MB)
2025-06-08 23:30:30,215 - LogUsuariosPipeline - INFO -   - USER_MODIFICATION_DAY.parquet (3.68 MB)
2025-06-08 23:30:30,215 - LogUsuariosPipeline - INFO -   - USER_AUTH_CHANGE_HISTORY.parquet (0.13 MB)
2025-06-08 23:30:30,215 - LogUsuariosPipeline - INFO - Archivos de salida en output/********:
2025-06-08 23:30:30,215 - LogUsuariosPipeline - INFO -   - LOG_USR.parquet (4.72 MB)
2025-06-08 23:30:30,215 - LogUsuariosPipeline - INFO -   - LOG_USR_DEDUPLICATED.parquet (4.60 MB)
2025-06-08 23:30:30,215 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-0231FCONFIANZA-********.csv (0.00 MB)
2025-06-08 23:30:30,215 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-BNACION-********.csv (0.07 MB)
2025-06-08 23:30:30,215 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-CCUSCO-********.csv (0.00 MB)
2025-06-08 23:30:30,215 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-CRANDES-********.csv (0.01 MB)
2025-06-08 23:30:30,215 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-FCOMPARTAMOS-********.csv (33.64 MB)
2025-06-08 23:30:30,215 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-FCONFIANZA-********.csv (0.00 MB)
2025-06-08 23:30:30,215 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-NULL-********.csv (0.00 MB)
2025-06-08 23:30:30,215 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-FCOMPARTAMOS-********233030.csv (2.16 MB)
2025-06-08 23:30:30,216 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-BNACION-********233030.csv (0.01 MB)
2025-06-08 23:30:30,216 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-CRANDES-********233030.csv (0.00 MB)
2025-06-08 23:30:30,216 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-CCUSCO-********233030.csv (0.00 MB)
2025-06-09 17:23:23,200 - LogUsuariosPipeline - INFO - Configurando credenciales S3...
2025-06-09 17:23:23,212 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-09 17:23:23,262 - LogUsuariosPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-09 17:23:23,263 - LogUsuariosPipeline - INFO - === INICIANDO PIPELINE LOG_USUARIOS MODERNIZADO ===
2025-06-09 17:23:23,263 - LogUsuariosPipeline - INFO - Fecha de procesamiento: 2025-06-06 (carpeta: ********)
2025-06-09 17:23:23,263 - LogUsuariosPipeline - INFO - USER-MODIFY: INICIANDO
2025-06-09 17:23:23,263 - LogUsuariosPipeline - INFO - Extrayendo USER_ACCOUNT_HISTORY para fecha: 2025-06-06
2025-06-09 17:23:23,412 - LogUsuariosPipeline - INFO - USER_ACCOUNT_HISTORY extraído: 8 registros -> TEMP_LOGS_USUARIOS/********/USER_ACCOUNT_HISTORY.parquet
2025-06-09 17:23:23,412 - LogUsuariosPipeline - INFO - USER-MODIFY: OK
2025-06-09 17:23:23,413 - LogUsuariosPipeline - INFO - PRE-LOG-USR: INICIANDO
2025-06-09 17:23:23,413 - LogUsuariosPipeline - INFO - Ejecutando SP_PRE_LOG_USR CORREGIDO (incluye USER_IDs múltiples) para fecha: 2025-06-06
2025-06-09 17:23:32,316 - LogUsuariosPipeline - INFO - SP_PRE_LOG_USR completado: 1583130 registros -> TEMP_LOGS_USUARIOS/********/USER_DATA_TRX.parquet
2025-06-09 17:23:32,317 - LogUsuariosPipeline - INFO - Ejecutando SP_USER_MODIFICATION para fecha: 2025-06-06
2025-06-09 17:23:35,654 - LogUsuariosPipeline - INFO - SP_USER_MODIFICATION completado: 6908 registros -> TEMP_LOGS_USUARIOS/********/USER_MODIFICATION_DAY.parquet
2025-06-09 17:23:35,655 - LogUsuariosPipeline - INFO - Ejecutando SP_USER_AUTH_DAY para fecha: 2025-06-06
2025-06-09 17:23:36,399 - LogUsuariosPipeline - INFO - SP_USER_AUTH_DAY completado: 5189 registros -> TEMP_LOGS_USUARIOS/********/USER_AUTH_CHANGE_HISTORY.parquet
2025-06-09 17:23:36,399 - LogUsuariosPipeline - INFO - PRE-LOG-USR: OK
2025-06-09 17:23:36,399 - LogUsuariosPipeline - INFO - LOG-USR: INICIANDO
2025-06-09 17:23:36,400 - LogUsuariosPipeline - INFO - Ejecutando SP_LOG_USR EXACTO (igual a Oracle) para fecha: 2025-06-06
2025-06-09 17:23:38,305 - LogUsuariosPipeline - INFO - SP_LOG_USR EXACTO completado: 19671 registros -> output/********/LOG_USR.parquet
2025-06-09 17:23:38,306 - LogUsuariosPipeline - INFO - LOG-USR: OK
2025-06-09 17:23:38,306 - LogUsuariosPipeline - INFO - EXPORT-CSV: INICIANDO
2025-06-09 17:23:38,306 - LogUsuariosPipeline - INFO - Iniciando exportación CSV con conversiones de perfiles para fecha: 2025-06-06
2025-06-09 17:23:38,306 - LogUsuariosPipeline - INFO - Aplicando SOLO deduplicación como Oracle original (SIN conversión de tipos)
2025-06-09 17:23:38,540 - LogUsuariosPipeline - INFO - Deduplicación completada (manteniendo REQUESTTYPE originales):
2025-06-09 17:23:38,540 - LogUsuariosPipeline - INFO -   - Registros originales: 19,671
2025-06-09 17:23:38,540 - LogUsuariosPipeline - INFO -   - Registros después de deduplicación: 16,069
2025-06-09 17:23:38,540 - LogUsuariosPipeline - INFO -   - Duplicados eliminados: 3,602
2025-06-09 17:23:38,543 - LogUsuariosPipeline - INFO - Tipos de transacción mantenidos:
2025-06-09 17:23:38,543 - LogUsuariosPipeline - INFO -   - User Modification: 6,579 registros
2025-06-09 17:23:38,543 - LogUsuariosPipeline - INFO -   - CHANGE_AUTH_FACTOR: 5,071 registros
2025-06-09 17:23:38,543 - LogUsuariosPipeline - INFO -   - ActivateUser: 1,756 registros
2025-06-09 17:23:38,543 - LogUsuariosPipeline - INFO -   - AfiliaUser: 1,756 registros
2025-06-09 17:23:38,543 - LogUsuariosPipeline - INFO -   - ClosedAccount: 289 registros
2025-06-09 17:23:38,543 - LogUsuariosPipeline - INFO -   - Delete User: 289 registros
2025-06-09 17:23:38,543 - LogUsuariosPipeline - INFO -   - ClosedUserAccount: 289 registros
2025-06-09 17:23:38,543 - LogUsuariosPipeline - INFO -   - Lock Wallet: 24 registros
2025-06-09 17:23:38,543 - LogUsuariosPipeline - INFO -   - Suspend User: 7 registros
2025-06-09 17:23:38,543 - LogUsuariosPipeline - INFO -   - Resume User: 6 registros
2025-06-09 17:23:38,543 - LogUsuariosPipeline - INFO - Aplicando conversiones de perfiles al archivo: output/********/LOG_USR_DEDUPLICATED.parquet
2025-06-09 17:23:38,543 - LogUsuariosPipeline - INFO - Cargando tabla de conversión de perfiles...
2025-06-09 17:23:38,546 - LogUsuariosPipeline - INFO - Tabla conv_perfil.csv cargada: 796 registros
2025-06-09 17:23:38,550 - LogUsuariosPipeline - INFO - Tabla de conversión de perfiles cargada exitosamente en DuckDB
2025-06-09 17:23:38,552 - LogUsuariosPipeline - ERROR - Error aplicando conversiones al archivo: Binder Error: Values list "cp_a" does not have a column named "PERFIL_CODIGO"

LINE 8: ...            LEFT JOIN conv_perfil_table cp_a ON l.perfilA = cp_a.PERFIL_CODIGO
                                                                       ^
2025-06-09 17:23:38,552 - LogUsuariosPipeline - INFO - Omitiendo CSV principal redundante - usando solo archivos Parquet y CSVs segmentados
2025-06-09 17:23:38,552 - LogUsuariosPipeline - INFO - Iniciando exportación segmentada por BankDomain
2025-06-09 17:23:38,552 - LogUsuariosPipeline - INFO - Procesando TODOS los bancos del sistema: ['0231FCONFIANZA', 'BNACION', 'CCUSCO', 'CRANDES', 'FCOMPARTAMOS', 'FCONFIANZA', None]
2025-06-09 17:23:38,556 - LogUsuariosPipeline - INFO - Exportado dominio 0231FCONFIANZA: 0 registros (archivo vacío SIN headers) -> output/********/csv_exports/by_bank/LOG-USUARIOS-0231FCONFIANZA-********.csv
2025-06-09 17:23:38,577 - LogUsuariosPipeline - INFO - Exportado dominio BNACION: 168 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-BNACION-********.csv
2025-06-09 17:23:38,584 - LogUsuariosPipeline - INFO - Exportado dominio CCUSCO: 0 registros (archivo vacío SIN headers) -> output/********/csv_exports/by_bank/LOG-USUARIOS-CCUSCO-********.csv
2025-06-09 17:23:38,590 - LogUsuariosPipeline - INFO - Exportado dominio CRANDES: 4 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-CRANDES-********.csv
2025-06-09 17:23:38,710 - LogUsuariosPipeline - INFO - Exportado dominio FCOMPARTAMOS: 19499 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-FCOMPARTAMOS-********.csv
2025-06-09 17:23:38,714 - LogUsuariosPipeline - INFO - Exportado dominio FCONFIANZA: 0 registros (archivo vacío SIN headers) -> output/********/csv_exports/by_bank/LOG-USUARIOS-FCONFIANZA-********.csv
2025-06-09 17:23:38,717 - LogUsuariosPipeline - INFO - Exportado dominio NULL: 0 registros (archivo vacío SIN headers) -> output/********/csv_exports/by_bank/LOG-USUARIOS-NULL-********.csv
2025-06-09 17:23:38,717 - LogUsuariosPipeline - INFO - Exportación segmentada completada: 7 archivos generados
2025-06-09 17:23:38,717 - LogUsuariosPipeline - INFO - Estructura EXACTA como flujo original: solo by_bank (sin by_profile)
2025-06-09 17:23:38,717 - LogUsuariosPipeline - INFO - 🔄 APLICANDO PROCESAMIENTO EXACTO como procesar.py...
2025-06-09 17:23:38,717 - LogUsuariosPipeline - INFO - 🔄 INICIANDO PROCESAMIENTO EXACTO como procesar.py
2025-06-09 17:23:38,718 - LogUsuariosPipeline - INFO - 📁 Input: output/********/LOG_USR.parquet
2025-06-09 17:23:38,718 - LogUsuariosPipeline - INFO - 📅 Fecha: 2025-06-06
2025-06-09 17:23:38,718 - LogUsuariosPipeline - INFO - 📁 Output: output/********/csv_exports/csv_final_procesado
2025-06-09 17:23:38,718 - LogUsuariosPipeline - INFO - 📊 Cargando datos desde LOG_USR.parquet...
2025-06-09 17:23:38,784 - LogUsuariosPipeline - INFO - 📊 Registros cargados: 19,671
2025-06-09 17:23:38,785 - LogUsuariosPipeline - INFO - 🔄 Procesando grupos por USERHISTID...
2025-06-09 17:23:42,056 - LogUsuariosPipeline - INFO - 📊 Convirtiendo a DataFrame...
2025-06-09 17:23:42,185 - LogUsuariosPipeline - INFO - 📊 Registros después del procesamiento inicial: 48,480
2025-06-09 17:23:42,185 - LogUsuariosPipeline - INFO - 🔍 Aplicando filtro de valores válidos...
2025-06-09 17:23:42,193 - LogUsuariosPipeline - INFO - 📊 Registros después del filtro: 17,993
2025-06-09 17:23:42,193 - LogUsuariosPipeline - INFO - 🥷 Aplicando filtros adicionales de calidad de datos...
2025-06-09 17:23:42,209 - LogUsuariosPipeline - INFO - 📊 Registros después de filtros de calidad: 17,993
2025-06-09 17:23:42,210 - LogUsuariosPipeline - INFO - 🔄 Agrupando y procesando JSON...
2025-06-09 17:23:45,318 - LogUsuariosPipeline - INFO - 📊 Registros después de eliminar duplicados: 13,049
2025-06-09 17:23:45,329 - LogUsuariosPipeline - INFO - 🔄 Aplicando asignaciones a columnas...
2025-06-09 17:23:46,395 - LogUsuariosPipeline - INFO - 📊 Cargando tabla de conversión de perfiles...
2025-06-09 17:23:46,396 - LogUsuariosPipeline - INFO - 📊 Tabla de conversión cargada: 796 registros
2025-06-09 17:23:46,734 - LogUsuariosPipeline - INFO - 🔄 Aplicando función crítica de expansión de registros...
2025-06-09 17:23:46,734 - LogUsuariosPipeline - INFO - 📊 Registros antes de expansión: 13,049
2025-06-09 17:23:47,301 - LogUsuariosPipeline - INFO - 📊 Registros después de expansión: 13,077
2025-06-09 17:23:47,301 - LogUsuariosPipeline - INFO - ✅ Función crítica de expansión aplicada exitosamente
2025-06-09 17:23:47,301 - LogUsuariosPipeline - INFO - 🔄 Aplicando mapeo de columnas...
2025-06-09 17:23:47,303 - LogUsuariosPipeline - INFO - 🔄 Procesando tipos de transacciones válidas...
2025-06-09 17:23:47,353 - LogUsuariosPipeline - INFO - 📊 Registros después de concatenar: 13,077
2025-06-09 17:24:02,986 - LogUsuariosPipeline - INFO - 📊 Registros finales para exportar: 13,077
2025-06-09 17:24:02,986 - LogUsuariosPipeline - INFO - 📁 Generando archivos CSV por BankDomain...
2025-06-09 17:24:03,053 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-FCOMPARTAMOS-********172402.csv (12,956 registros)
2025-06-09 17:24:03,056 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-BNACION-********172402.csv (111 registros)
2025-06-09 17:24:03,058 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-CRANDES-********172402.csv (2 registros)
2025-06-09 17:24:03,059 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-CCUSCO-********172402.csv (8 registros)
2025-06-09 17:24:03,059 - LogUsuariosPipeline - INFO - ✅ Procesamiento completado: 4 archivos generados
2025-06-09 17:24:03,059 - LogUsuariosPipeline - INFO - 📊 Total registros procesados: 13,077
2025-06-09 17:24:03,113 - LogUsuariosPipeline - INFO - ✅ Procesamiento exacto completado: 4 archivos
2025-06-09 17:24:03,113 - LogUsuariosPipeline - INFO -   📁 LOGUSR-FCOMPARTAMOS-********172402.csv
2025-06-09 17:24:03,113 - LogUsuariosPipeline - INFO -   📁 LOGUSR-BNACION-********172402.csv
2025-06-09 17:24:03,113 - LogUsuariosPipeline - INFO -   📁 LOGUSR-CRANDES-********172402.csv
2025-06-09 17:24:03,113 - LogUsuariosPipeline - INFO -   📁 LOGUSR-CCUSCO-********172402.csv
2025-06-09 17:24:03,113 - LogUsuariosPipeline - INFO - Archivo temporal eliminado: TEMP_LOGS_USUARIOS/********/WALLET_OLD.parquet
2025-06-09 17:24:03,114 - LogUsuariosPipeline - INFO - EXPORT-CSV: OK
2025-06-09 17:24:03,114 - LogUsuariosPipeline - INFO - === ARCHIVOS CSV GENERADOS ===
2025-06-09 17:24:03,114 - LogUsuariosPipeline - INFO - Total de archivos CSV segmentados: 11
2025-06-09 17:24:03,114 - LogUsuariosPipeline - INFO - Archivos por BankDomain (7) - EXACTO como flujo original:
2025-06-09 17:24:03,114 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-0231FCONFIANZA-********.csv
2025-06-09 17:24:03,114 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-BNACION-********.csv
2025-06-09 17:24:03,114 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-CCUSCO-********.csv
2025-06-09 17:24:03,114 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-CRANDES-********.csv
2025-06-09 17:24:03,114 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-FCOMPARTAMOS-********.csv
2025-06-09 17:24:03,114 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-FCONFIANZA-********.csv
2025-06-09 17:24:03,114 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-NULL-********.csv
2025-06-09 17:24:03,114 - LogUsuariosPipeline - INFO - Estructura OPTIMIZADA: solo archivos segmentados (datos completos en Parquet)
2025-06-09 17:24:03,114 - LogUsuariosPipeline - INFO - === PIPELINE COMPLETADO EXITOSAMENTE ===
2025-06-09 17:24:03,114 - LogUsuariosPipeline - INFO - 
=== ARCHIVOS GENERADOS ===
2025-06-09 17:24:03,114 - LogUsuariosPipeline - INFO - Archivos temporales en TEMP_LOGS_USUARIOS/********:
2025-06-09 17:24:03,114 - LogUsuariosPipeline - INFO -   - USER_ACCOUNT_HISTORY.parquet (0.00 MB)
2025-06-09 17:24:03,114 - LogUsuariosPipeline - INFO -   - USER_DATA_TRX.parquet (127.14 MB)
2025-06-09 17:24:03,114 - LogUsuariosPipeline - INFO -   - USER_MODIFICATION_DAY.parquet (3.36 MB)
2025-06-09 17:24:03,114 - LogUsuariosPipeline - INFO -   - USER_AUTH_CHANGE_HISTORY.parquet (0.11 MB)
2025-06-09 17:24:03,114 - LogUsuariosPipeline - INFO - Archivos de salida en output/********:
2025-06-09 17:24:03,114 - LogUsuariosPipeline - INFO -   - LOG_USR.parquet (4.35 MB)
2025-06-09 17:24:03,114 - LogUsuariosPipeline - INFO -   - LOG_USR_DEDUPLICATED.parquet (4.23 MB)
2025-06-09 17:24:03,115 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-0231FCONFIANZA-********.csv (0.00 MB)
2025-06-09 17:24:03,115 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-BNACION-********.csv (0.11 MB)
2025-06-09 17:24:03,115 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-CCUSCO-********.csv (0.00 MB)
2025-06-09 17:24:03,115 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-CRANDES-********.csv (0.00 MB)
2025-06-09 17:24:03,115 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-FCOMPARTAMOS-********.csv (30.92 MB)
2025-06-09 17:24:03,115 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-FCONFIANZA-********.csv (0.00 MB)
2025-06-09 17:24:03,115 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-NULL-********.csv (0.00 MB)
2025-06-09 17:24:03,115 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-FCOMPARTAMOS-********232454.csv (2.00 MB)
2025-06-09 17:24:03,115 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-BNACION-********232454.csv (0.01 MB)
2025-06-09 17:24:03,115 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-CRANDES-********232454.csv (0.00 MB)
2025-06-09 17:24:03,115 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-CCUSCO-********232454.csv (0.00 MB)
2025-06-09 17:24:03,115 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-FCOMPARTAMOS-********172402.csv (2.00 MB)
2025-06-09 17:24:03,115 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-BNACION-********172402.csv (0.01 MB)
2025-06-09 17:24:03,115 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-CRANDES-********172402.csv (0.00 MB)
2025-06-09 17:24:03,115 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-CCUSCO-********172402.csv (0.00 MB)
2025-06-10 01:08:48,432 - LogUsuariosPipeline - INFO - Configurando credenciales S3...
2025-06-10 01:08:48,445 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-10 01:08:48,494 - LogUsuariosPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-10 01:08:48,495 - LogUsuariosPipeline - INFO - === INICIANDO PIPELINE LOG_USUARIOS MODERNIZADO ===
2025-06-10 01:08:48,495 - LogUsuariosPipeline - INFO - Fecha de procesamiento: 2025-06-06 (carpeta: ********)
2025-06-10 01:08:48,495 - LogUsuariosPipeline - INFO - USER-MODIFY: INICIANDO
2025-06-10 01:08:48,495 - LogUsuariosPipeline - INFO - Extrayendo USER_ACCOUNT_HISTORY para fecha: 2025-06-06
2025-06-10 01:08:48,642 - LogUsuariosPipeline - INFO - USER_ACCOUNT_HISTORY extraído: 8 registros -> TEMP_LOGS_USUARIOS/********/USER_ACCOUNT_HISTORY.parquet
2025-06-10 01:08:48,643 - LogUsuariosPipeline - INFO - USER-MODIFY: OK
2025-06-10 01:08:48,643 - LogUsuariosPipeline - INFO - PRE-LOG-USR: INICIANDO
2025-06-10 01:08:48,643 - LogUsuariosPipeline - INFO - Ejecutando SP_PRE_LOG_USR CORREGIDO (incluye USER_IDs múltiples) para fecha: 2025-06-06
2025-06-10 01:08:55,325 - LogUsuariosPipeline - INFO - SP_PRE_LOG_USR completado: 1583130 registros -> TEMP_LOGS_USUARIOS/********/USER_DATA_TRX.parquet
2025-06-10 01:08:55,326 - LogUsuariosPipeline - INFO - Ejecutando SP_USER_MODIFICATION para fecha: 2025-06-06
2025-06-10 01:08:57,615 - LogUsuariosPipeline - INFO - SP_USER_MODIFICATION completado: 6908 registros -> TEMP_LOGS_USUARIOS/********/USER_MODIFICATION_DAY.parquet
2025-06-10 01:08:57,616 - LogUsuariosPipeline - INFO - Ejecutando SP_USER_AUTH_DAY para fecha: 2025-06-06
2025-06-10 01:08:57,997 - LogUsuariosPipeline - INFO - SP_USER_AUTH_DAY completado: 5189 registros -> TEMP_LOGS_USUARIOS/********/USER_AUTH_CHANGE_HISTORY.parquet
2025-06-10 01:08:57,997 - LogUsuariosPipeline - INFO - PRE-LOG-USR: OK
2025-06-10 01:08:57,997 - LogUsuariosPipeline - INFO - LOG-USR: INICIANDO
2025-06-10 01:08:57,997 - LogUsuariosPipeline - INFO - Ejecutando SP_LOG_USR EXACTO (igual a Oracle) para fecha: 2025-06-06
2025-06-10 01:08:59,557 - LogUsuariosPipeline - INFO - SP_LOG_USR EXACTO completado: 19671 registros -> output/********/LOG_USR.parquet
2025-06-10 01:08:59,558 - LogUsuariosPipeline - INFO - LOG-USR: OK
2025-06-10 01:08:59,558 - LogUsuariosPipeline - INFO - EXPORT-CSV: INICIANDO
2025-06-10 01:08:59,558 - LogUsuariosPipeline - INFO - Iniciando exportación CSV con conversiones de perfiles para fecha: 2025-06-06
2025-06-10 01:08:59,558 - LogUsuariosPipeline - INFO - Aplicando SOLO deduplicación como Oracle original (SIN conversión de tipos)
2025-06-10 01:08:59,779 - LogUsuariosPipeline - INFO - Deduplicación completada (manteniendo REQUESTTYPE originales):
2025-06-10 01:08:59,779 - LogUsuariosPipeline - INFO -   - Registros originales: 19,671
2025-06-10 01:08:59,779 - LogUsuariosPipeline - INFO -   - Registros después de deduplicación: 16,069
2025-06-10 01:08:59,779 - LogUsuariosPipeline - INFO -   - Duplicados eliminados: 3,602
2025-06-10 01:08:59,781 - LogUsuariosPipeline - INFO - Tipos de transacción mantenidos:
2025-06-10 01:08:59,781 - LogUsuariosPipeline - INFO -   - User Modification: 6,579 registros
2025-06-10 01:08:59,781 - LogUsuariosPipeline - INFO -   - CHANGE_AUTH_FACTOR: 5,071 registros
2025-06-10 01:08:59,781 - LogUsuariosPipeline - INFO -   - AfiliaUser: 1,756 registros
2025-06-10 01:08:59,781 - LogUsuariosPipeline - INFO -   - ActivateUser: 1,756 registros
2025-06-10 01:08:59,781 - LogUsuariosPipeline - INFO -   - ClosedUserAccount: 289 registros
2025-06-10 01:08:59,781 - LogUsuariosPipeline - INFO -   - ClosedAccount: 289 registros
2025-06-10 01:08:59,781 - LogUsuariosPipeline - INFO -   - Delete User: 289 registros
2025-06-10 01:08:59,781 - LogUsuariosPipeline - INFO -   - Lock Wallet: 24 registros
2025-06-10 01:08:59,781 - LogUsuariosPipeline - INFO -   - Suspend User: 7 registros
2025-06-10 01:08:59,781 - LogUsuariosPipeline - INFO -   - Resume User: 6 registros
2025-06-10 01:08:59,781 - LogUsuariosPipeline - INFO - Aplicando conversiones de perfiles al archivo: output/********/LOG_USR_DEDUPLICATED.parquet
2025-06-10 01:08:59,781 - LogUsuariosPipeline - INFO - Cargando tabla de conversión de perfiles...
2025-06-10 01:08:59,784 - LogUsuariosPipeline - INFO - Tabla conv_perfil.csv cargada: 796 registros
2025-06-10 01:08:59,787 - LogUsuariosPipeline - INFO - Tabla de conversión de perfiles cargada exitosamente en DuckDB
2025-06-10 01:08:59,789 - LogUsuariosPipeline - ERROR - Error aplicando conversiones al archivo: Binder Error: Values list "cp_a" does not have a column named "PERFIL_CODIGO"

LINE 8: ...            LEFT JOIN conv_perfil_table cp_a ON l.perfilA = cp_a.PERFIL_CODIGO
                                                                       ^
2025-06-10 01:08:59,789 - LogUsuariosPipeline - INFO - Omitiendo CSV principal redundante - usando solo archivos Parquet y CSVs segmentados
2025-06-10 01:08:59,789 - LogUsuariosPipeline - INFO - Iniciando exportación segmentada por BankDomain
2025-06-10 01:08:59,789 - LogUsuariosPipeline - INFO - Procesando TODOS los bancos del sistema: ['0231FCONFIANZA', 'BNACION', 'CCUSCO', 'CRANDES', 'FCOMPARTAMOS', 'FCONFIANZA', None]
2025-06-10 01:08:59,793 - LogUsuariosPipeline - INFO - Exportado dominio 0231FCONFIANZA: 0 registros (archivo vacío SIN headers) -> output/********/csv_exports/by_bank/LOG-USUARIOS-0231FCONFIANZA-********.csv
2025-06-10 01:08:59,812 - LogUsuariosPipeline - INFO - Exportado dominio BNACION: 168 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-BNACION-********.csv
2025-06-10 01:08:59,818 - LogUsuariosPipeline - INFO - Exportado dominio CCUSCO: 0 registros (archivo vacío SIN headers) -> output/********/csv_exports/by_bank/LOG-USUARIOS-CCUSCO-********.csv
2025-06-10 01:08:59,826 - LogUsuariosPipeline - INFO - Exportado dominio CRANDES: 4 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-CRANDES-********.csv
2025-06-10 01:08:59,939 - LogUsuariosPipeline - INFO - Exportado dominio FCOMPARTAMOS: 19499 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-FCOMPARTAMOS-********.csv
2025-06-10 01:08:59,943 - LogUsuariosPipeline - INFO - Exportado dominio FCONFIANZA: 0 registros (archivo vacío SIN headers) -> output/********/csv_exports/by_bank/LOG-USUARIOS-FCONFIANZA-********.csv
2025-06-10 01:08:59,946 - LogUsuariosPipeline - INFO - Exportado dominio NULL: 0 registros (archivo vacío SIN headers) -> output/********/csv_exports/by_bank/LOG-USUARIOS-NULL-********.csv
2025-06-10 01:08:59,946 - LogUsuariosPipeline - INFO - Exportación segmentada completada: 7 archivos generados
2025-06-10 01:08:59,946 - LogUsuariosPipeline - INFO - Estructura EXACTA como flujo original: solo by_bank (sin by_profile)
2025-06-10 01:08:59,946 - LogUsuariosPipeline - INFO - 🔄 APLICANDO PROCESAMIENTO EXACTO como procesar.py...
2025-06-10 01:08:59,947 - LogUsuariosPipeline - INFO - 🔄 INICIANDO PROCESAMIENTO EXACTO como procesar.py
2025-06-10 01:08:59,947 - LogUsuariosPipeline - INFO - 📁 Input: output/********/LOG_USR.parquet
2025-06-10 01:08:59,947 - LogUsuariosPipeline - INFO - 📅 Fecha: 2025-06-06
2025-06-10 01:08:59,947 - LogUsuariosPipeline - INFO - 📁 Output: output/********/csv_exports/csv_final_procesado
2025-06-10 01:08:59,947 - LogUsuariosPipeline - INFO - 📊 Cargando datos desde LOG_USR.parquet...
2025-06-10 01:09:00,011 - LogUsuariosPipeline - INFO - 📊 Registros cargados: 19,671
2025-06-10 01:09:00,011 - LogUsuariosPipeline - INFO - 🔄 Procesando grupos por USERHISTID...
2025-06-10 01:09:03,189 - LogUsuariosPipeline - INFO - 📊 Convirtiendo a DataFrame...
2025-06-10 01:09:03,314 - LogUsuariosPipeline - INFO - 📊 Registros después del procesamiento inicial: 48,480
2025-06-10 01:09:03,314 - LogUsuariosPipeline - INFO - 🔍 Aplicando filtro de valores válidos...
2025-06-10 01:09:03,322 - LogUsuariosPipeline - INFO - 📊 Registros después del filtro: 17,993
2025-06-10 01:09:03,322 - LogUsuariosPipeline - INFO - 🥷 Aplicando filtros adicionales de calidad de datos...
2025-06-10 01:09:03,337 - LogUsuariosPipeline - INFO - 📊 Registros después de filtros de calidad: 17,993
2025-06-10 01:09:03,337 - LogUsuariosPipeline - INFO - 🔄 Agrupando y procesando JSON...
2025-06-10 01:09:06,153 - LogUsuariosPipeline - INFO - 📊 Registros después de eliminar duplicados: 13,049
2025-06-10 01:09:06,162 - LogUsuariosPipeline - INFO - 🔄 Aplicando asignaciones a columnas...
2025-06-10 01:09:07,087 - LogUsuariosPipeline - INFO - 📊 Cargando tabla de conversión de perfiles...
2025-06-10 01:09:07,088 - LogUsuariosPipeline - INFO - 📊 Tabla de conversión cargada: 796 registros
2025-06-10 01:09:07,412 - LogUsuariosPipeline - INFO - 🔄 Aplicando función crítica de expansión de registros...
2025-06-10 01:09:07,412 - LogUsuariosPipeline - INFO - 📊 Registros antes de expansión: 13,049
2025-06-10 01:09:07,905 - LogUsuariosPipeline - INFO - 📊 Registros después de expansión: 13,077
2025-06-10 01:09:07,905 - LogUsuariosPipeline - INFO - ✅ Función crítica de expansión aplicada exitosamente
2025-06-10 01:09:07,905 - LogUsuariosPipeline - INFO - 🔄 Aplicando mapeo de columnas...
2025-06-10 01:09:07,907 - LogUsuariosPipeline - INFO - 🔄 Procesando tipos de transacciones válidas...
2025-06-10 01:09:07,953 - LogUsuariosPipeline - INFO - 📊 Registros después de concatenar: 13,077
2025-06-10 01:09:23,688 - LogUsuariosPipeline - INFO - 📊 Registros finales para exportar: 13,077
2025-06-10 01:09:23,689 - LogUsuariosPipeline - INFO - 📁 Generando archivos CSV por BankDomain...
2025-06-10 01:09:23,748 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-FCOMPARTAMOS-********010923.csv (12,956 registros)
2025-06-10 01:09:23,750 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-BNACION-********010923.csv (111 registros)
2025-06-10 01:09:23,751 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-CRANDES-********010923.csv (2 registros)
2025-06-10 01:09:23,752 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-CCUSCO-********010923.csv (8 registros)
2025-06-10 01:09:23,753 - LogUsuariosPipeline - INFO - ✅ Procesamiento completado: 4 archivos generados
2025-06-10 01:09:23,753 - LogUsuariosPipeline - INFO - 📊 Total registros procesados: 13,077
2025-06-10 01:09:23,801 - LogUsuariosPipeline - INFO - ✅ Procesamiento exacto completado: 4 archivos
2025-06-10 01:09:23,801 - LogUsuariosPipeline - INFO -   📁 LOGUSR-FCOMPARTAMOS-********010923.csv
2025-06-10 01:09:23,801 - LogUsuariosPipeline - INFO -   📁 LOGUSR-BNACION-********010923.csv
2025-06-10 01:09:23,801 - LogUsuariosPipeline - INFO -   📁 LOGUSR-CRANDES-********010923.csv
2025-06-10 01:09:23,801 - LogUsuariosPipeline - INFO -   📁 LOGUSR-CCUSCO-********010923.csv
2025-06-10 01:09:23,802 - LogUsuariosPipeline - INFO - Archivo temporal eliminado: TEMP_LOGS_USUARIOS/********/WALLET_OLD.parquet
2025-06-10 01:09:23,802 - LogUsuariosPipeline - INFO - EXPORT-CSV: OK
2025-06-10 01:09:23,802 - LogUsuariosPipeline - INFO - === ARCHIVOS CSV GENERADOS ===
2025-06-10 01:09:23,802 - LogUsuariosPipeline - INFO - Total de archivos CSV segmentados: 11
2025-06-10 01:09:23,802 - LogUsuariosPipeline - INFO - Archivos por BankDomain (7) - EXACTO como flujo original:
2025-06-10 01:09:23,802 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-0231FCONFIANZA-********.csv
2025-06-10 01:09:23,802 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-BNACION-********.csv
2025-06-10 01:09:23,802 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-CCUSCO-********.csv
2025-06-10 01:09:23,802 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-CRANDES-********.csv
2025-06-10 01:09:23,802 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-FCOMPARTAMOS-********.csv
2025-06-10 01:09:23,802 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-FCONFIANZA-********.csv
2025-06-10 01:09:23,802 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-NULL-********.csv
2025-06-10 01:09:23,802 - LogUsuariosPipeline - INFO - Estructura OPTIMIZADA: solo archivos segmentados (datos completos en Parquet)
2025-06-10 01:09:23,802 - LogUsuariosPipeline - INFO - === PIPELINE COMPLETADO EXITOSAMENTE ===
2025-06-10 01:09:23,802 - LogUsuariosPipeline - INFO - 
=== ARCHIVOS GENERADOS ===
2025-06-10 01:09:23,802 - LogUsuariosPipeline - INFO - Archivos temporales en TEMP_LOGS_USUARIOS/********:
2025-06-10 01:09:23,802 - LogUsuariosPipeline - INFO -   - USER_ACCOUNT_HISTORY.parquet (0.00 MB)
2025-06-10 01:09:23,802 - LogUsuariosPipeline - INFO -   - USER_DATA_TRX.parquet (127.15 MB)
2025-06-10 01:09:23,802 - LogUsuariosPipeline - INFO -   - USER_MODIFICATION_DAY.parquet (3.36 MB)
2025-06-10 01:09:23,802 - LogUsuariosPipeline - INFO -   - USER_AUTH_CHANGE_HISTORY.parquet (0.11 MB)
2025-06-10 01:09:23,802 - LogUsuariosPipeline - INFO - Archivos de salida en output/********:
2025-06-10 01:09:23,803 - LogUsuariosPipeline - INFO -   - LOG_USR.parquet (4.34 MB)
2025-06-10 01:09:23,803 - LogUsuariosPipeline - INFO -   - LOG_USR_DEDUPLICATED.parquet (4.23 MB)
2025-06-10 01:09:23,803 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-0231FCONFIANZA-********.csv (0.00 MB)
2025-06-10 01:09:23,803 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-BNACION-********.csv (0.11 MB)
2025-06-10 01:09:23,803 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-CCUSCO-********.csv (0.00 MB)
2025-06-10 01:09:23,803 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-CRANDES-********.csv (0.00 MB)
2025-06-10 01:09:23,803 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-FCOMPARTAMOS-********.csv (30.92 MB)
2025-06-10 01:09:23,803 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-FCONFIANZA-********.csv (0.00 MB)
2025-06-10 01:09:23,803 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-NULL-********.csv (0.00 MB)
2025-06-10 01:09:23,803 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-FCOMPARTAMOS-********232454.csv (2.00 MB)
2025-06-10 01:09:23,803 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-BNACION-********232454.csv (0.01 MB)
2025-06-10 01:09:23,803 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-CRANDES-********232454.csv (0.00 MB)
2025-06-10 01:09:23,803 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-CCUSCO-********232454.csv (0.00 MB)
2025-06-10 01:09:23,803 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-FCOMPARTAMOS-********172402.csv (2.00 MB)
2025-06-10 01:09:23,803 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-BNACION-********172402.csv (0.01 MB)
2025-06-10 01:09:23,803 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-CRANDES-********172402.csv (0.00 MB)
2025-06-10 01:09:23,803 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-CCUSCO-********172402.csv (0.00 MB)
2025-06-10 01:09:23,803 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-FCOMPARTAMOS-********010923.csv (2.00 MB)
2025-06-10 01:09:23,803 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-BNACION-********010923.csv (0.01 MB)
2025-06-10 01:09:23,803 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-CRANDES-********010923.csv (0.00 MB)
2025-06-10 01:09:23,803 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-CCUSCO-********010923.csv (0.00 MB)
2025-06-10 07:37:22,362 - LogUsuariosPipeline - INFO - Configurando credenciales S3...
2025-06-10 07:37:22,374 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-10 07:37:22,422 - LogUsuariosPipeline - INFO - Credenciales S3 configuradas exitosamente
2025-06-10 07:37:22,423 - LogUsuariosPipeline - INFO - === INICIANDO PIPELINE LOG_USUARIOS MODERNIZADO ===
2025-06-10 07:37:22,423 - LogUsuariosPipeline - INFO - Fecha de procesamiento: 2025-06-09 (carpeta: ********)
2025-06-10 07:37:22,423 - LogUsuariosPipeline - INFO - USER-MODIFY: INICIANDO
2025-06-10 07:37:22,423 - LogUsuariosPipeline - INFO - Extrayendo USER_ACCOUNT_HISTORY para fecha: 2025-06-09
2025-06-10 07:37:22,551 - LogUsuariosPipeline - INFO - USER_ACCOUNT_HISTORY extraído: 11 registros -> TEMP_LOGS_USUARIOS/********/USER_ACCOUNT_HISTORY.parquet
2025-06-10 07:37:22,551 - LogUsuariosPipeline - INFO - USER-MODIFY: OK
2025-06-10 07:37:22,551 - LogUsuariosPipeline - INFO - PRE-LOG-USR: INICIANDO
2025-06-10 07:37:22,551 - LogUsuariosPipeline - INFO - Ejecutando SP_PRE_LOG_USR CORREGIDO (incluye USER_IDs múltiples) para fecha: 2025-06-09
2025-06-10 07:37:29,455 - LogUsuariosPipeline - INFO - SP_PRE_LOG_USR completado: 1585633 registros -> TEMP_LOGS_USUARIOS/********/USER_DATA_TRX.parquet
2025-06-10 07:37:29,456 - LogUsuariosPipeline - INFO - Ejecutando SP_USER_MODIFICATION para fecha: 2025-06-09
2025-06-10 07:37:31,907 - LogUsuariosPipeline - INFO - SP_USER_MODIFICATION completado: 6898 registros -> TEMP_LOGS_USUARIOS/********/USER_MODIFICATION_DAY.parquet
2025-06-10 07:37:31,907 - LogUsuariosPipeline - INFO - Ejecutando SP_USER_AUTH_DAY para fecha: 2025-06-09
2025-06-10 07:37:32,426 - LogUsuariosPipeline - INFO - SP_USER_AUTH_DAY completado: 5223 registros -> TEMP_LOGS_USUARIOS/********/USER_AUTH_CHANGE_HISTORY.parquet
2025-06-10 07:37:32,426 - LogUsuariosPipeline - INFO - PRE-LOG-USR: OK
2025-06-10 07:37:32,426 - LogUsuariosPipeline - INFO - LOG-USR: INICIANDO
2025-06-10 07:37:32,426 - LogUsuariosPipeline - INFO - Ejecutando SP_LOG_USR EXACTO (igual a Oracle) para fecha: 2025-06-09
2025-06-10 07:37:34,011 - LogUsuariosPipeline - INFO - SP_LOG_USR EXACTO completado: 19754 registros -> output/********/LOG_USR.parquet
2025-06-10 07:37:34,012 - LogUsuariosPipeline - INFO - LOG-USR: OK
2025-06-10 07:37:34,012 - LogUsuariosPipeline - INFO - EXPORT-CSV: INICIANDO
2025-06-10 07:37:34,012 - LogUsuariosPipeline - INFO - Iniciando exportación CSV con conversiones de perfiles para fecha: 2025-06-09
2025-06-10 07:37:34,012 - LogUsuariosPipeline - INFO - Aplicando SOLO deduplicación como Oracle original (SIN conversión de tipos)
2025-06-10 07:37:34,229 - LogUsuariosPipeline - INFO - Deduplicación completada (manteniendo REQUESTTYPE originales):
2025-06-10 07:37:34,229 - LogUsuariosPipeline - INFO -   - Registros originales: 19,754
2025-06-10 07:37:34,230 - LogUsuariosPipeline - INFO -   - Registros después de deduplicación: 16,150
2025-06-10 07:37:34,230 - LogUsuariosPipeline - INFO -   - Duplicados eliminados: 3,604
2025-06-10 07:37:34,232 - LogUsuariosPipeline - INFO - Tipos de transacción mantenidos:
2025-06-10 07:37:34,232 - LogUsuariosPipeline - INFO -   - User Modification: 6,427 registros
2025-06-10 07:37:34,232 - LogUsuariosPipeline - INFO -   - CHANGE_AUTH_FACTOR: 5,102 registros
2025-06-10 07:37:34,232 - LogUsuariosPipeline - INFO -   - AfiliaUser: 1,796 registros
2025-06-10 07:37:34,232 - LogUsuariosPipeline - INFO -   - ActivateUser: 1,796 registros
2025-06-10 07:37:34,232 - LogUsuariosPipeline - INFO -   - ClosedAccount: 279 registros
2025-06-10 07:37:34,232 - LogUsuariosPipeline - INFO -   - Delete User: 279 registros
2025-06-10 07:37:34,232 - LogUsuariosPipeline - INFO -   - ClosedUserAccount: 279 registros
2025-06-10 07:37:34,232 - LogUsuariosPipeline - INFO -   - Lock Wallet: 156 registros
2025-06-10 07:37:34,232 - LogUsuariosPipeline - INFO -   - Unlock Wallet: 26 registros
2025-06-10 07:37:34,232 - LogUsuariosPipeline - INFO -   - Suspend User: 8 registros
2025-06-10 07:37:34,232 - LogUsuariosPipeline - INFO - Aplicando conversiones de perfiles al archivo: output/********/LOG_USR_DEDUPLICATED.parquet
2025-06-10 07:37:34,232 - LogUsuariosPipeline - INFO - Cargando tabla de conversión de perfiles...
2025-06-10 07:37:34,235 - LogUsuariosPipeline - INFO - Tabla conv_perfil.csv cargada: 796 registros
2025-06-10 07:37:34,239 - LogUsuariosPipeline - INFO - Tabla de conversión de perfiles cargada exitosamente en DuckDB
2025-06-10 07:37:34,241 - LogUsuariosPipeline - ERROR - Error aplicando conversiones al archivo: Binder Error: Values list "cp_a" does not have a column named "PERFIL_CODIGO"

LINE 8: ...            LEFT JOIN conv_perfil_table cp_a ON l.perfilA = cp_a.PERFIL_CODIGO
                                                                       ^
2025-06-10 07:37:34,241 - LogUsuariosPipeline - INFO - Omitiendo CSV principal redundante - usando solo archivos Parquet y CSVs segmentados
2025-06-10 07:37:34,241 - LogUsuariosPipeline - INFO - Iniciando exportación segmentada por BankDomain
2025-06-10 07:37:34,241 - LogUsuariosPipeline - INFO - Procesando TODOS los bancos del sistema: ['0231FCONFIANZA', 'BNACION', 'CCUSCO', 'CRANDES', 'FCOMPARTAMOS', 'FCONFIANZA', None]
2025-06-10 07:37:34,259 - LogUsuariosPipeline - INFO - Exportado dominio 0231FCONFIANZA: 10 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-0231FCONFIANZA-********.csv
2025-06-10 07:37:34,282 - LogUsuariosPipeline - INFO - Exportado dominio BNACION: 182 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-BNACION-********.csv
2025-06-10 07:37:34,300 - LogUsuariosPipeline - INFO - Exportado dominio CCUSCO: 125 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-CCUSCO-********.csv
2025-06-10 07:37:34,316 - LogUsuariosPipeline - INFO - Exportado dominio CRANDES: 31 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-CRANDES-********.csv
2025-06-10 07:37:34,423 - LogUsuariosPipeline - INFO - Exportado dominio FCOMPARTAMOS: 19405 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-FCOMPARTAMOS-********.csv
2025-06-10 07:37:34,433 - LogUsuariosPipeline - INFO - Exportado dominio FCONFIANZA: 0 registros (archivo vacío SIN headers) -> output/********/csv_exports/by_bank/LOG-USUARIOS-FCONFIANZA-********.csv
2025-06-10 07:37:34,460 - LogUsuariosPipeline - INFO - Exportado dominio NULL: 1 registros -> output/********/csv_exports/by_bank/LOG-USUARIOS-NULL-********.csv
2025-06-10 07:37:34,460 - LogUsuariosPipeline - INFO - Exportación segmentada completada: 7 archivos generados
2025-06-10 07:37:34,460 - LogUsuariosPipeline - INFO - Estructura EXACTA como flujo original: solo by_bank (sin by_profile)
2025-06-10 07:37:34,460 - LogUsuariosPipeline - INFO - 🔄 APLICANDO PROCESAMIENTO EXACTO como procesar.py...
2025-06-10 07:37:34,461 - LogUsuariosPipeline - INFO - 🔄 INICIANDO PROCESAMIENTO EXACTO como procesar.py
2025-06-10 07:37:34,461 - LogUsuariosPipeline - INFO - 📁 Input: output/********/LOG_USR.parquet
2025-06-10 07:37:34,461 - LogUsuariosPipeline - INFO - 📅 Fecha: 2025-06-09
2025-06-10 07:37:34,461 - LogUsuariosPipeline - INFO - 📁 Output: output/********/csv_exports/csv_final_procesado
2025-06-10 07:37:34,461 - LogUsuariosPipeline - INFO - 📊 Cargando datos desde LOG_USR.parquet...
2025-06-10 07:37:34,527 - LogUsuariosPipeline - INFO - 📊 Registros cargados: 19,754
2025-06-10 07:37:34,527 - LogUsuariosPipeline - INFO - 🔄 Procesando grupos por USERHISTID...
2025-06-10 07:37:37,742 - LogUsuariosPipeline - INFO - 📊 Convirtiendo a DataFrame...
2025-06-10 07:37:37,874 - LogUsuariosPipeline - INFO - 📊 Registros después del procesamiento inicial: 49,009
2025-06-10 07:37:37,875 - LogUsuariosPipeline - INFO - 🔍 Aplicando filtro de valores válidos...
2025-06-10 07:37:37,884 - LogUsuariosPipeline - INFO - 📊 Registros después del filtro: 18,154
2025-06-10 07:37:37,884 - LogUsuariosPipeline - INFO - 🥷 Aplicando filtros adicionales de calidad de datos...
2025-06-10 07:37:37,892 - LogUsuariosPipeline - INFO -   🔍 Eliminados 1 registros con BANKDOMAIN inválido
2025-06-10 07:37:37,905 - LogUsuariosPipeline - INFO - 📊 Registros después de filtros de calidad: 18,153
2025-06-10 07:37:37,905 - LogUsuariosPipeline - INFO - 🔄 Agrupando y procesando JSON...
2025-06-10 07:37:40,837 - LogUsuariosPipeline - INFO - 📊 Registros después de eliminar duplicados: 13,176
2025-06-10 07:37:40,849 - LogUsuariosPipeline - INFO - 🔄 Aplicando asignaciones a columnas...
2025-06-10 07:37:41,840 - LogUsuariosPipeline - INFO - 📊 Cargando tabla de conversión de perfiles...
2025-06-10 07:37:41,842 - LogUsuariosPipeline - INFO - 📊 Tabla de conversión cargada: 796 registros
2025-06-10 07:37:42,145 - LogUsuariosPipeline - INFO - 🔄 Aplicando función crítica de expansión de registros...
2025-06-10 07:37:42,146 - LogUsuariosPipeline - INFO - 📊 Registros antes de expansión: 13,176
2025-06-10 07:37:42,702 - LogUsuariosPipeline - INFO - 📊 Registros después de expansión: 13,220
2025-06-10 07:37:42,702 - LogUsuariosPipeline - INFO - ✅ Función crítica de expansión aplicada exitosamente
2025-06-10 07:37:42,702 - LogUsuariosPipeline - INFO - 🔄 Aplicando mapeo de columnas...
2025-06-10 07:37:42,704 - LogUsuariosPipeline - INFO - 🔄 Procesando tipos de transacciones válidas...
2025-06-10 07:37:42,751 - LogUsuariosPipeline - INFO - 📊 Registros después de concatenar: 13,220
2025-06-10 07:37:58,365 - LogUsuariosPipeline - INFO - 📊 Registros finales para exportar: 13,220
2025-06-10 07:37:58,366 - LogUsuariosPipeline - INFO - 📁 Generando archivos CSV por BankDomain...
2025-06-10 07:37:58,434 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-FCOMPARTAMOS-**************.csv (12,941 registros)
2025-06-10 07:37:58,438 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-BNACION-**************.csv (115 registros)
2025-06-10 07:37:58,439 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-CRANDES-**************.csv (23 registros)
2025-06-10 07:37:58,440 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-CCUSCO-**************.csv (135 registros)
2025-06-10 07:37:58,442 - LogUsuariosPipeline - INFO - 📁 Archivo generado: output/********/csv_exports/csv_final_procesado/LOGUSR-0231FCONFIANZA-**************.csv (6 registros)
2025-06-10 07:37:58,442 - LogUsuariosPipeline - INFO - ✅ Procesamiento completado: 5 archivos generados
2025-06-10 07:37:58,442 - LogUsuariosPipeline - INFO - 📊 Total registros procesados: 13,220
2025-06-10 07:37:58,494 - LogUsuariosPipeline - INFO - ✅ Procesamiento exacto completado: 5 archivos
2025-06-10 07:37:58,494 - LogUsuariosPipeline - INFO -   📁 LOGUSR-FCOMPARTAMOS-**************.csv
2025-06-10 07:37:58,494 - LogUsuariosPipeline - INFO -   📁 LOGUSR-BNACION-**************.csv
2025-06-10 07:37:58,494 - LogUsuariosPipeline - INFO -   📁 LOGUSR-CRANDES-**************.csv
2025-06-10 07:37:58,494 - LogUsuariosPipeline - INFO -   📁 LOGUSR-CCUSCO-**************.csv
2025-06-10 07:37:58,494 - LogUsuariosPipeline - INFO -   📁 LOGUSR-0231FCONFIANZA-**************.csv
2025-06-10 07:37:58,495 - LogUsuariosPipeline - INFO - Archivo temporal eliminado: TEMP_LOGS_USUARIOS/********/WALLET_OLD.parquet
2025-06-10 07:37:58,495 - LogUsuariosPipeline - INFO - EXPORT-CSV: OK
2025-06-10 07:37:58,495 - LogUsuariosPipeline - INFO - === ARCHIVOS CSV GENERADOS ===
2025-06-10 07:37:58,495 - LogUsuariosPipeline - INFO - Total de archivos CSV segmentados: 12
2025-06-10 07:37:58,495 - LogUsuariosPipeline - INFO - Archivos por BankDomain (7) - EXACTO como flujo original:
2025-06-10 07:37:58,495 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-0231FCONFIANZA-********.csv
2025-06-10 07:37:58,495 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-BNACION-********.csv
2025-06-10 07:37:58,495 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-CCUSCO-********.csv
2025-06-10 07:37:58,495 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-CRANDES-********.csv
2025-06-10 07:37:58,495 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-FCOMPARTAMOS-********.csv
2025-06-10 07:37:58,495 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-FCONFIANZA-********.csv
2025-06-10 07:37:58,495 - LogUsuariosPipeline - INFO -   - LOG-USUARIOS-NULL-********.csv
2025-06-10 07:37:58,495 - LogUsuariosPipeline - INFO - Estructura OPTIMIZADA: solo archivos segmentados (datos completos en Parquet)
2025-06-10 07:37:58,495 - LogUsuariosPipeline - INFO - === PIPELINE COMPLETADO EXITOSAMENTE ===
2025-06-10 07:37:58,495 - LogUsuariosPipeline - INFO - 
=== ARCHIVOS GENERADOS ===
2025-06-10 07:37:58,495 - LogUsuariosPipeline - INFO - Archivos temporales en TEMP_LOGS_USUARIOS/********:
2025-06-10 07:37:58,495 - LogUsuariosPipeline - INFO -   - USER_ACCOUNT_HISTORY.parquet (0.00 MB)
2025-06-10 07:37:58,495 - LogUsuariosPipeline - INFO -   - USER_DATA_TRX.parquet (127.47 MB)
2025-06-10 07:37:58,495 - LogUsuariosPipeline - INFO -   - USER_MODIFICATION_DAY.parquet (3.29 MB)
2025-06-10 07:37:58,495 - LogUsuariosPipeline - INFO -   - USER_AUTH_CHANGE_HISTORY.parquet (0.11 MB)
2025-06-10 07:37:58,495 - LogUsuariosPipeline - INFO - Archivos de salida en output/********:
2025-06-10 07:37:58,496 - LogUsuariosPipeline - INFO -   - LOG_USR.parquet (4.30 MB)
2025-06-10 07:37:58,496 - LogUsuariosPipeline - INFO -   - LOG_USR_DEDUPLICATED.parquet (4.17 MB)
2025-06-10 07:37:58,496 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-0231FCONFIANZA-********.csv (0.00 MB)
2025-06-10 07:37:58,496 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-BNACION-********.csv (0.11 MB)
2025-06-10 07:37:58,496 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-CCUSCO-********.csv (0.06 MB)
2025-06-10 07:37:58,496 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-CRANDES-********.csv (0.02 MB)
2025-06-10 07:37:58,496 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-FCOMPARTAMOS-********.csv (30.24 MB)
2025-06-10 07:37:58,496 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-FCONFIANZA-********.csv (0.00 MB)
2025-06-10 07:37:58,496 - LogUsuariosPipeline - INFO -   - csv_exports/by_bank/LOG-USUARIOS-NULL-********.csv (0.00 MB)
2025-06-10 07:37:58,496 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-FCOMPARTAMOS-**************.csv (1.99 MB)
2025-06-10 07:37:58,496 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-BNACION-**************.csv (0.01 MB)
2025-06-10 07:37:58,496 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-CRANDES-**************.csv (0.00 MB)
2025-06-10 07:37:58,496 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-CCUSCO-**************.csv (0.02 MB)
2025-06-10 07:37:58,496 - LogUsuariosPipeline - INFO -   - csv_exports/csv_final_procesado/LOGUSR-0231FCONFIANZA-**************.csv (0.00 MB)
