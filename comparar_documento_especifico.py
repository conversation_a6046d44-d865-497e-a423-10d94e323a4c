#!/usr/bin/env python3
"""
Script para comparar un documento específico entre pipeline modernizado y Oracle
Enfoque en la columna DiaHora para verificar homologación
"""
import pandas as pd
import os

def comparar_documento_especifico():
    """Compara documento específico entre modernizado y Oracle"""
    print("🔍 COMPARACIÓN ESPECÍFICA DE DOCUMENTO")
    print("=" * 50)
    
    documento_buscar = "70635331"
    
    # Archivos a comparar - usar el más reciente (después de corrección CRÍTICA)
    archivo_modernizado = "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/output/20250609/csv_exports/csv_final_procesado/LOGUSR-FCOMPARTAMOS-20250609184618.csv"
    archivo_oracle = "/home/<USER>/generate/log_usuarios/output/LOGUSR-FCOMPARTAMOS-20250610052953.csv"
    
    print(f"🎯 Documento a analizar: {documento_buscar}")
    print(f"📄 Archivo modernizado: {os.path.basename(archivo_modernizado)}")
    print(f"📄 Archivo Oracle: {os.path.basename(archivo_oracle)}")
    
    # Verificar que ambos archivos existan
    if not os.path.exists(archivo_modernizado):
        print(f"❌ Archivo modernizado no encontrado: {archivo_modernizado}")
        return
    
    if not os.path.exists(archivo_oracle):
        print(f"❌ Archivo Oracle no encontrado: {archivo_oracle}")
        return
    
    # Analizar archivo modernizado
    print(f"\n1️⃣ ANÁLISIS ARCHIVO MODERNIZADO:")
    registros_modernizado = analizar_documento_en_archivo(archivo_modernizado, documento_buscar, "MODERNIZADO")
    
    # Analizar archivo Oracle
    print(f"\n2️⃣ ANÁLISIS ARCHIVO ORACLE:")
    registros_oracle = analizar_documento_en_archivo(archivo_oracle, documento_buscar, "ORACLE")
    
    # Comparación detallada
    print(f"\n3️⃣ COMPARACIÓN DETALLADA:")
    comparar_registros_detallado(registros_modernizado, registros_oracle, documento_buscar)

def analizar_documento_en_archivo(archivo_path, documento, tipo_archivo):
    """Analiza un documento específico en un archivo"""
    try:
        # Leer archivo
        df = pd.read_csv(archivo_path, header=None)
        
        print(f"   📊 {tipo_archivo} - Total registros: {len(df):,}")
        
        # Asignar nombres de columnas estándar
        columnas = ['OPERACION', 'TRANSACTIONID', 'FECHA_HORA', 'CANAL', 'TIPODOCUMENTO', 
                   'DOCUMENTO', 'CELULAR', 'EMPRESA'] + [f'COL{i}' for i in range(8, 30)]
        
        df.columns = columnas[:len(df.columns)]
        
        # Buscar documento específico
        mask_documento = df['DOCUMENTO'].astype(str) == documento
        registros_documento = df[mask_documento]
        
        print(f"   🎯 Registros del documento {documento}: {len(registros_documento)}")
        
        if len(registros_documento) > 0:
            print(f"   📋 Operaciones encontradas:")
            operaciones = registros_documento['OPERACION'].value_counts()
            for op, count in operaciones.items():
                print(f"      • {op}: {count}")
            
            print(f"   📅 Análisis de DiaHora:")
            
            # Mostrar todos los registros con detalles de DiaHora
            registros_detalle = []
            for i, (_, registro) in enumerate(registros_documento.iterrows(), 1):
                fecha_hora = registro['FECHA_HORA']
                operacion = registro['OPERACION']
                transactionid = registro['TRANSACTIONID']
                celular = registro['CELULAR']
                
                print(f"      {i}. {operacion} - {fecha_hora} - TID: {transactionid} - Cel: {celular}")
                
                registros_detalle.append({
                    'operacion': operacion,
                    'fecha_hora': fecha_hora,
                    'transactionid': transactionid,
                    'celular': celular,
                    'registro_completo': registro
                })
            
            return registros_detalle
        else:
            print(f"   ❌ Documento {documento} NO encontrado en {tipo_archivo}")
            return []
            
    except Exception as e:
        print(f"   ❌ Error analizando {tipo_archivo}: {e}")
        return []

def comparar_registros_detallado(registros_modernizado, registros_oracle, documento):
    """Compara los registros detalladamente"""
    
    print(f"   📊 Resumen de comparación:")
    print(f"      • Modernizado: {len(registros_modernizado)} registros")
    print(f"      • Oracle: {len(registros_oracle)} registros")
    print(f"      • Diferencia: {len(registros_oracle) - len(registros_modernizado):+} registros")
    
    if len(registros_modernizado) == 0 and len(registros_oracle) == 0:
        print(f"   ⚠️  Documento {documento} no encontrado en ningún archivo")
        return
    
    if len(registros_modernizado) == 0:
        print(f"   ❌ Documento {documento} SOLO existe en Oracle")
        print(f"   📋 Registros en Oracle:")
        for i, reg in enumerate(registros_oracle, 1):
            print(f"      {i}. {reg['operacion']} - {reg['fecha_hora']} - TID: {reg['transactionid']}")
        return
    
    if len(registros_oracle) == 0:
        print(f"   ❌ Documento {documento} SOLO existe en Modernizado")
        print(f"   📋 Registros en Modernizado:")
        for i, reg in enumerate(registros_modernizado, 1):
            print(f"      {i}. {reg['operacion']} - {reg['fecha_hora']} - TID: {reg['transactionid']}")
        return
    
    # Comparación detallada cuando ambos tienen registros
    print(f"\n   🔍 COMPARACIÓN DETALLADA DE FECHA_HORA:")
    
    # Crear mapas por operación para comparar
    ops_modernizado = {}
    ops_oracle = {}
    
    for reg in registros_modernizado:
        op = reg['operacion']
        if op not in ops_modernizado:
            ops_modernizado[op] = []
        ops_modernizado[op].append(reg)
    
    for reg in registros_oracle:
        op = reg['operacion']
        if op not in ops_oracle:
            ops_oracle[op] = []
        ops_oracle[op].append(reg)
    
    # Comparar por operación
    todas_operaciones = set(ops_modernizado.keys()) | set(ops_oracle.keys())
    
    for operacion in sorted(todas_operaciones):
        print(f"\n      📋 OPERACIÓN: {operacion}")
        
        regs_mod = ops_modernizado.get(operacion, [])
        regs_ora = ops_oracle.get(operacion, [])
        
        print(f"         • Modernizado: {len(regs_mod)} registros")
        print(f"         • Oracle: {len(regs_ora)} registros")
        
        if len(regs_mod) > 0:
            print(f"         📅 Modernizado - DiaHora:")
            for reg in regs_mod:
                print(f"            {reg['fecha_hora']} - TID: {reg['transactionid']}")
        
        if len(regs_ora) > 0:
            print(f"         📅 Oracle - DiaHora:")
            for reg in regs_ora:
                print(f"            {reg['fecha_hora']} - TID: {reg['transactionid']}")
        
        # Verificar homologación de fechas
        if len(regs_mod) == len(regs_ora) and len(regs_mod) > 0:
            fechas_mod = [reg['fecha_hora'] for reg in regs_mod]
            fechas_ora = [reg['fecha_hora'] for reg in regs_ora]
            
            if set(fechas_mod) == set(fechas_ora):
                print(f"         ✅ HOMOLOGACIÓN PERFECTA - Fechas idénticas")
            else:
                print(f"         ⚠️  DIFERENCIAS EN FECHAS:")
                solo_mod = set(fechas_mod) - set(fechas_ora)
                solo_ora = set(fechas_ora) - set(fechas_mod)
                
                if solo_mod:
                    print(f"            Solo en Modernizado: {solo_mod}")
                if solo_ora:
                    print(f"            Solo en Oracle: {solo_ora}")
        elif len(regs_mod) != len(regs_ora):
            print(f"         ❌ DIFERENCIA EN CANTIDAD DE REGISTROS")
            if len(regs_mod) < len(regs_ora):
                print(f"            Faltan {len(regs_ora) - len(regs_mod)} registros en Modernizado")
            else:
                print(f"            Sobran {len(regs_mod) - len(regs_ora)} registros en Modernizado")

def analizar_patrones_temporales(registros_modernizado, registros_oracle):
    """Analiza patrones temporales entre ambos archivos"""
    print(f"\n4️⃣ ANÁLISIS DE PATRONES TEMPORALES:")
    
    if not registros_modernizado and not registros_oracle:
        print(f"   ⚠️  No hay registros para analizar patrones temporales")
        return
    
    # Extraer horas de ambos conjuntos
    horas_modernizado = []
    horas_oracle = []
    
    for reg in registros_modernizado:
        try:
            fecha_hora = reg['fecha_hora']
            if isinstance(fecha_hora, str) and len(fecha_hora) >= 16:
                hora = fecha_hora[11:16]  # HH:MM
                horas_modernizado.append(hora)
        except:
            pass
    
    for reg in registros_oracle:
        try:
            fecha_hora = reg['fecha_hora']
            if isinstance(fecha_hora, str) and len(fecha_hora) >= 16:
                hora = fecha_hora[11:16]  # HH:MM
                horas_oracle.append(hora)
        except:
            pass
    
    print(f"   📊 Distribución temporal:")
    print(f"      • Modernizado: {horas_modernizado}")
    print(f"      • Oracle: {horas_oracle}")
    
    if set(horas_modernizado) == set(horas_oracle):
        print(f"   ✅ PATRONES TEMPORALES IDÉNTICOS")
    else:
        print(f"   ⚠️  DIFERENCIAS EN PATRONES TEMPORALES")

if __name__ == "__main__":
    print("🕵️ COMPARACIÓN ESPECÍFICA DE DOCUMENTO - HOMOLOGACIÓN FECHA_HORA")
    print("=" * 80)
    
    comparar_documento_especifico()
    
    print(f"\n✅ Comparación completada")
    print(f"\n📋 OBJETIVO:")
    print(f"   Verificar si la columna DiaHora (FECHA_HORA) está perfectamente homologada")
    print(f"   entre el pipeline modernizado y el flujo original de Oracle")
