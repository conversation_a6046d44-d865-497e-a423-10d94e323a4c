#!/usr/bin/env python3
"""
Script para buscar el usuario faltante en los archivos temporales correctos
"""
import pandas as pd
import os

def buscar_en_archivos_temporales():
    """Busca el usuario en los archivos temporales del pipeline"""
    print("🔍 BÚSQUEDA EN ARCHIVOS TEMPORALES DEL PIPELINE")
    print("=" * 55)
    
    # Datos del registro faltante
    documento = "71793435"
    celular = "51907368782"
    fecha_hora = "2025-06-09 22:52:32"
    
    print(f"🎯 BUSCANDO:")
    print(f"   • Documento: {documento}")
    print(f"   • Celular: {celular}")
    print(f"   • Fecha/Hora: {fecha_hora}")
    
    # Archivos temporales del pipeline
    archivos_temporales = [
        "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/TEMP_LOGS_USUARIOS/********/USER_DATA_TRX.parquet",
        "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/TEMP_LOGS_USUARIOS/********/USER_MODIFICATION_DAY.parquet",
        "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/TEMP_LOGS_USUARIOS/********/USER_ACCOUNT_HISTORY.parquet",
        "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/TEMP_LOGS_USUARIOS/********/USER_AUTH_CHANGE_HISTORY.parquet"
    ]
    
    encontrado_en = []
    
    for archivo in archivos_temporales:
        if os.path.exists(archivo):
            print(f"\n📄 Analizando: {os.path.basename(archivo)}")
            try:
                resultado = analizar_archivo_temporal(archivo, documento, celular)
                if resultado:
                    encontrado_en.append((archivo, resultado))
                    print(f"   ✅ USUARIO ENCONTRADO!")
                    for reg in resultado:
                        print(f"      - Documento: {reg.get('documento', 'N/A')}")
                        print(f"      - Celular: {reg.get('celular', 'N/A')}")
                        print(f"      - Fecha: {reg.get('fecha', 'N/A')}")
                        print(f"      - Tipo: {reg.get('tipo', 'N/A')}")
                else:
                    print(f"   ❌ Usuario no encontrado")
            except Exception as e:
                print(f"   ❌ Error: {e}")
        else:
            print(f"❌ Archivo no existe: {archivo}")
    
    return encontrado_en

def analizar_archivo_temporal(archivo_path, documento, celular):
    """Analiza un archivo temporal específico"""
    try:
        df = pd.read_parquet(archivo_path)
        
        print(f"      Registros totales: {len(df):,}")
        print(f"      Columnas: {list(df.columns)}")
        
        registros_encontrados = []
        
        # Buscar por documento en diferentes columnas posibles
        columnas_documento = [col for col in df.columns if any(x in col.upper() for x in ['DOCUMENTO', 'DOC', 'ID_VALUE', 'USER_ID'])]
        columnas_celular = [col for col in df.columns if any(x in col.upper() for x in ['CELULAR', 'MSISDN', 'PHONE', 'WALLET'])]
        columnas_fecha = [col for col in df.columns if any(x in col.upper() for x in ['CREATED', 'MODIFIED', 'DATE', 'TIME'])]
        
        print(f"      Columnas documento: {columnas_documento}")
        print(f"      Columnas celular: {columnas_celular}")
        print(f"      Columnas fecha: {columnas_fecha}")
        
        # Buscar por documento
        for col_doc in columnas_documento:
            if col_doc in df.columns:
                mask_doc = df[col_doc].astype(str).str.contains(documento, na=False)
                registros_doc = df[mask_doc]
                
                if len(registros_doc) > 0:
                    print(f"      ✅ Encontrados {len(registros_doc)} registros con documento {documento} en columna {col_doc}")
                    
                    for _, registro in registros_doc.iterrows():
                        reg_info = {
                            'documento': registro.get(col_doc, 'N/A'),
                            'celular': 'N/A',
                            'fecha': 'N/A',
                            'tipo': f'Documento en {col_doc}',
                            'datos_completos': dict(registro)
                        }
                        
                        # Buscar celular en el mismo registro
                        for col_cel in columnas_celular:
                            if col_cel in registro and pd.notna(registro[col_cel]):
                                if celular in str(registro[col_cel]):
                                    reg_info['celular'] = registro[col_cel]
                                    break
                        
                        # Buscar fecha en el mismo registro
                        for col_fecha in columnas_fecha:
                            if col_fecha in registro and pd.notna(registro[col_fecha]):
                                reg_info['fecha'] = registro[col_fecha]
                                break
                        
                        registros_encontrados.append(reg_info)
        
        # Si no se encontró por documento, buscar por celular
        if not registros_encontrados:
            for col_cel in columnas_celular:
                if col_cel in df.columns:
                    mask_cel = df[col_cel].astype(str).str.contains(celular, na=False)
                    registros_cel = df[mask_cel]
                    
                    if len(registros_cel) > 0:
                        print(f"      ✅ Encontrados {len(registros_cel)} registros con celular {celular} en columna {col_cel}")
                        
                        for _, registro in registros_cel.iterrows():
                            reg_info = {
                                'documento': 'N/A',
                                'celular': registro.get(col_cel, 'N/A'),
                                'fecha': 'N/A',
                                'tipo': f'Celular en {col_cel}',
                                'datos_completos': dict(registro)
                            }
                            
                            # Buscar documento en el mismo registro
                            for col_doc in columnas_documento:
                                if col_doc in registro and pd.notna(registro[col_doc]):
                                    if documento in str(registro[col_doc]):
                                        reg_info['documento'] = registro[col_doc]
                                        break
                            
                            # Buscar fecha en el mismo registro
                            for col_fecha in columnas_fecha:
                                if col_fecha in registro and pd.notna(registro[col_fecha]):
                                    reg_info['fecha'] = registro[col_fecha]
                                    break
                            
                            registros_encontrados.append(reg_info)
                        break
        
        return registros_encontrados if registros_encontrados else None
        
    except Exception as e:
        raise e

def revisar_filtros_pipeline():
    """Revisa los filtros en el código del pipeline"""
    print(f"\n🔍 REVISIÓN DE FILTROS EN EL PIPELINE:")
    
    archivo_pipeline = "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/pipeline_log_usuarios_duckdb.py"
    
    if os.path.exists(archivo_pipeline):
        print(f"   📄 Analizando: {os.path.basename(archivo_pipeline)}")
        
        with open(archivo_pipeline, 'r') as f:
            contenido = f.read()
        
        # Buscar filtros de hora específicos
        lineas = contenido.split('\n')
        filtros_hora = []
        filtros_fecha = []
        
        for i, linea in enumerate(lineas):
            linea_upper = linea.upper()
            
            # Buscar filtros de hora
            if any(hora in linea for hora in ['22:', '23:', '20:', '21:']):
                filtros_hora.append((i+1, linea.strip()))
            
            # Buscar filtros WHERE con fecha/hora
            if 'WHERE' in linea_upper and any(palabra in linea_upper for palabra in ['HOUR', 'TIME', 'BETWEEN', '>', '<']):
                filtros_fecha.append((i+1, linea.strip()))
            
            # Buscar filtros de horario de negocio
            if any(palabra in linea_upper for palabra in ['BUSINESS_HOUR', 'HORARIO', 'HOUR(', 'EXTRACT(HOUR']):
                filtros_hora.append((i+1, linea.strip()))
        
        if filtros_hora:
            print(f"   ⚠️  FILTROS DE HORA ENCONTRADOS:")
            for linea_num, linea in filtros_hora:
                print(f"      Línea {linea_num}: {linea}")
        
        if filtros_fecha:
            print(f"   🔍 FILTROS DE FECHA/HORA ENCONTRADOS:")
            for linea_num, linea in filtros_fecha:
                print(f"      Línea {linea_num}: {linea}")
        
        if not filtros_hora and not filtros_fecha:
            print(f"   ✅ No se encontraron filtros de hora específicos")
    else:
        print(f"   ❌ Archivo del pipeline no encontrado")

def analizar_diferencia_horaria():
    """Analiza la diferencia horaria entre el registro faltante y el rango del pipeline"""
    print(f"\n🕐 ANÁLISIS DE DIFERENCIA HORARIA:")
    
    fecha_registro = "2025-06-09 22:52:32"
    
    # Leer el archivo final para ver el rango de horas
    archivo_final = "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/output/********/csv_exports/csv_final_procesado/LOGUSR-FCOMPARTAMOS-********074148.csv"
    
    if os.path.exists(archivo_final):
        try:
            df = pd.read_csv(archivo_final, header=None, nrows=1000)  # Solo primeras 1000 filas para análisis rápido
            
            if len(df.columns) >= 3:
                fechas = df.iloc[:, 2].dropna()  # Columna de fecha/hora
                
                if len(fechas) > 0:
                    print(f"   📊 Muestra de fechas en el archivo final:")
                    for i, fecha in enumerate(fechas.head(10)):
                        print(f"      {i+1}. {fecha}")
                    
                    # Extraer horas
                    horas = []
                    for fecha in fechas:
                        try:
                            if isinstance(fecha, str) and len(fecha) >= 19:
                                hora = fecha[11:13]  # Extraer HH de YYYY-MM-DD HH:MM:SS
                                if hora.isdigit():
                                    horas.append(int(hora))
                        except:
                            pass
                    
                    if horas:
                        hora_min = min(horas)
                        hora_max = max(horas)
                        print(f"   🕐 Rango de horas en el pipeline: {hora_min:02d}:XX - {hora_max:02d}:XX")
                        print(f"   🎯 Hora del registro faltante: 22:52")
                        
                        if 22 > hora_max:
                            print(f"   ❌ PROBLEMA IDENTIFICADO: El registro (22:52) está fuera del rango del pipeline")
                            print(f"   💡 El pipeline solo procesó hasta las {hora_max:02d}:XX")
                        else:
                            print(f"   ✅ La hora del registro está dentro del rango esperado")
        except Exception as e:
            print(f"   ❌ Error analizando archivo final: {e}")

if __name__ == "__main__":
    print("🕵️ INVESTIGACIÓN EN ARCHIVOS TEMPORALES")
    print("=" * 50)
    
    encontrado_en = buscar_en_archivos_temporales()
    revisar_filtros_pipeline()
    analizar_diferencia_horaria()
    
    print(f"\n📊 RESUMEN FINAL:")
    if encontrado_en:
        print(f"   ✅ Usuario encontrado en {len(encontrado_en)} archivos temporales")
        print(f"   💡 El usuario EXISTE en los datos de origen")
        print(f"   🔍 Se perdió durante el procesamiento del pipeline")
    else:
        print(f"   ❌ Usuario NO encontrado en archivos temporales")
        print(f"   💡 El usuario NO EXISTE en los datos de origen del pipeline")
        print(f"   🔍 Diferencia entre datos de Oracle y S3")
    
    print(f"\n✅ Investigación completada")
